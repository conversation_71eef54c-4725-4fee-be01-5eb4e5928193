// Bridge script that provides OpenSearch functionality to the page
window.OpenSearchBridge = {
  async makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage({
        action: 'opensearchRequest',
        config: {
          url,
          method: options.method || 'GET',
          headers: options.headers || {},
          body: options.body
        }
      }, (response) => {
        if (response.success) {
          resolve(response.data);
        } else {
          reject(new Error(response.error));
        }
      });
    });
  },

  isAvailable() {
    return typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage;
  }
};
