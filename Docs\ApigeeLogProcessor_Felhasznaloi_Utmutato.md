# Apigee Log Processor - Felhasználói Útmutató

## Tartalomjegyzék
1. [<PERSON><PERSON><PERSON><PERSON><PERSON>](#<PERSON><PERSON><PERSON><PERSON>s)
2. [<PERSON><PERSON><PERSON><PERSON> l<PERSON>](#kezdeti-lépések)
3. [<PERSON>l<PERSON>z<PERSON><PERSON><PERSON><PERSON> felület](#felhasználói-felület)
4. [Logok feldolgozása](#logok-feldolgozása)
5. [Nézetek használata](#nézetek-használata)
6. [Keresés és szűrés](#keresés-és-szűrés)
7. [Részletes nézet](#részletes-nézet)
8. [Exportálási lehetőségek](#exportálási-lehetőségek)
9. [Érzékeny adatok kezelése](#érzékeny-adatok-kezelése)
10. [Hibaelhárítás](#hibaelhárítás)

## Áttekintés

Az Apigee Log Processor egy kliens oldali webalkalmazás, amely az Apigee API proxy logok elemzésére, feldolgozására és megjelenítésére szolgál. Az alkalmazás különböző formátumú logokat képes kezelni, és felhasználóbarát felületet biztosít az API hívások elemzéséhez, a kérések és válaszok részleteinek megtekintéséhez, valamint az API problémák elhárításához.

### Főbb funkciók
- Többféle log formátum támogatása (standard, CSV útvonallal, egyszerű üzenet, nyers JSON)
- Flow nézet és API hívás nézet a különböző elemzési perspektívákhoz
- Részletes oldalsó panel az egyes API hívások vizsgálatához
- Keresési funkció a log bejegyzésekben
- Szűrés üzenet azonosító, flow típus és állapotkód alapján
- Exportálási lehetőségek (TXT, XLSX)
- Érzékeny információk maszkolása
- Reszponzív felhasználói felület világos/sötét téma támogatással

## Kezdeti lépések

### Alkalmazás betöltése
1. Nyissa meg az Apigee Log Processor alkalmazást a böngészőjében
2. Az alkalmazás betöltődik és megjeleníti a kezdőképernyőt

### Téma beállítása
- A jobb felső sarokban található témaváltó gombbal válthat a világos és sötét téma között
- Az alkalmazás alapértelmezetten a rendszer beállításait követi

## Felhasználói felület

Az alkalmazás felhasználói felülete a következő fő részekből áll:

### Beviteli szakasz
- Szövegmező a logok beillesztéséhez
- Fájl feltöltési lehetőség (.txt, .log, .json, .csv formátumok támogatottak)
- Művelet gombok:
  - **Process Logs**: Logok feldolgozása és megjelenítése
  - **Extract message Id**: Üzenet azonosítók kinyerése
  - **Clear All**: Minden adat törlése

### Eredmények nézet
- Nézet váltó a Flow és API hívás nézetek között
- Adattáblák rendezési lehetőséggel
- Oszlop láthatósági beállítások
- Szűrők:
  - Üzenet azonosító szűrő
  - Flow típus szűrő
  - Állapotkód szűrő
- Keresési funkció
- Lapozási vezérlők

### Oldalsó részletes panel
- Átfogó nézet egy API hívásról vagy flow-ról
- Előnézeti szakasz a kulcsfontosságú metaadatokkal
- HTTP fejlécek formázott megjelenítése
- Üzenettörzs megjelenítése szintaxis kiemeléssel
- Másolási és exportálási funkciók
- Érzékeny adatok maszkolásának kapcsolója

## Logok feldolgozása

### Log adatok betöltése
1. **Szöveg beillesztése**:
   - Másolja a log tartalmat a vágólapra
   - Illessze be a szövegmezőbe

   VAGY

2. **Fájl feltöltése**:
   - Kattintson a "Tallózás" gombra a fájl kiválasztó megnyitásához
   - Válassza ki a log fájlt (.txt, .log, .json vagy .csv)

### Feldolgozás indítása
1. Kattintson a **Process Logs** gombra
2. Az alkalmazás automatikusan felismeri a log formátumot
3. Feldolgozza az adatokat és megjeleníti az eredményeket
4. A beviteli szakasz összecsukódik, hogy több hely legyen az eredmények megjelenítésére

### Üzenet azonosítók kinyerése
1. Töltse be a log adatokat (szöveg beillesztésével vagy fájl feltöltésével)
2. Kattintson az **Extract message Id** gombra
3. Az alkalmazás kinyeri és megjeleníti az egyedi üzenet azonosítókat

### Adatok törlése
- Kattintson a **Clear All** gombra az összes adat törléséhez és az alkalmazás alaphelyzetbe állításához

## Nézetek használata

### Flow nézet
A Flow nézet az egyes log bejegyzéseket külön-külön jeleníti meg, és részletes információkat nyújt minden flow eseményről.

#### Flow nézet jellemzői
- Minden sor egy különálló log bejegyzést jelenít meg
- Időbélyeg, flow típus, URI, állapotkód és egyéb adatok megjelenítése
- Színkódolt állapotkódok a gyors vizuális azonosításhoz
- Kattintható sorok a részletes nézet megnyitásához

#### Flow nézet használata
1. Kattintson a nézet választó gombra és válassza az "API Flows" opciót
2. A táblázat minden sora egy log bejegyzést jelenít meg
3. Kattintson egy sorra a részletes információk megtekintéséhez az oldalsó panelen
4. Használja a szűrőket és a keresést a bejegyzések szűkítéséhez

### API hívás nézet
Az API hívás nézet az összetartozó flow bejegyzéseket egyetlen API hívásba csoportosítja, így átfogó képet nyújt a teljes kérés-válasz ciklusról.

#### API hívás nézet jellemzői
- A kapcsolódó flow-k egyetlen API hívásként jelennek meg
- Megjeleníti a kérés módszert, URI-t, állapotkódot
- Kiszámítja és megjeleníti a válaszidőt
- Kattintható sorok a részletes nézet megnyitásához

#### API hívás nézet használata
1. Kattintson a nézet választó gombra és válassza az "API Calls" opciót
2. A táblázat minden sora egy teljes API hívást jelenít meg
3. Kattintson egy sorra a részletes információk megtekintéséhez az oldalsó panelen
4. Használja a szűrőket és a keresést a hívások szűkítéséhez

## Keresés és szűrés

### Keresés
Az alkalmazás hatékony keresési funkciókat kínál a log bejegyzések között.

#### Keresés használata
1. Írja be a keresési kifejezést a keresőmezőbe
2. Az alkalmazás automatikusan szűri a bejegyzéseket a beírt kifejezés alapján
3. A találatok száma megjelenik a bejegyzés számlálóban
4. A keresési kifejezés kiemelve jelenik meg a találatokban

#### Keresési opciók
- **Globális keresés**: Jelölje be a "Globális keresés" jelölőnégyzetet a kereséshez minden mezőben
- **Üzenettörzs keresés**: Alapértelmezetten csak az üzenettörzsben keres
- **Összetett keresés**: Használhat AND/OR operátorokat (pl. "error AND timeout" vagy "404 OR 500")

### Szűrés
Az alkalmazás többféle szűrési lehetőséget kínál a log bejegyzések szűkítéséhez.

#### Üzenet azonosító szűrő
1. Kattintson a "Message IDs" gombra a szűrő megnyitásához
2. Jelölje be vagy törölje a jelölést az egyes üzenet azonosítóknál
3. Használja az "All message IDs" opciót az összes kiválasztásához vagy törléséhez

#### Flow típus szűrő
1. Kattintson a "Flows" gombra a szűrő megnyitásához
2. Jelölje be vagy törölje a jelölést az egyes flow típusoknál (PROXY_REQ_FLOW, TARGET_REQ_FLOW, stb.)
3. Használja az "All flows" opciót az összes kiválasztásához vagy törléséhez

#### Állapotkód szűrő
1. Kattintson a "Status Codes" gombra a szűrő megnyitásához
2. Jelölje be vagy törölje a jelölést az egyes állapotkódoknál
3. Használja az "All status codes" opciót az összes kiválasztásához vagy törléséhez

#### Oszlop láthatóság
1. Kattintson a "Columns" gombra a láthatósági beállítások megnyitásához
2. Jelölje be vagy törölje a jelölést az egyes oszlopoknál
3. Használja az "All columns" opciót az összes kiválasztásához vagy törléséhez

### Lapozás
Az alkalmazás lapozási funkciókat kínál a nagy mennyiségű log adat kezeléséhez.

#### Lapozás használata
1. Használja a lapozási vezérlőket a táblázat alján a lapok közötti navigáláshoz
2. Válassza ki a laponkénti bejegyzések számát a legördülő menüből (25, 50, 100, 200)
3. Használja az első, előző, következő és utolsó lap gombokat a gyors navigációhoz
4. A jelenlegi oldal és a teljes oldalszám megjelenik a vezérlők között

## Részletes nézet

A részletes nézet egy oldalsó panelen jelenik meg, és átfogó információkat nyújt egy kiválasztott log bejegyzésről vagy API hívásról.

### Részletes nézet megnyitása
- **Flow nézetben**: Kattintson egy log bejegyzés sorára
- **API hívás nézetben**: Kattintson egy API hívás sorára

### Részletes nézet elemei

#### Fejléc
- Üzenet azonosító
- Flow típus
- Környezet/szervezet
- Állapotkód
- Navigációs gombok az előző/következő bejegyzéshez
- Másolás gomb
- Exportálás gomb
- Maszkolás kapcsoló

#### Előnézeti szakasz
- Kulcsfontosságú metaadatok (időbélyeg, URI, HTTP módszer, stb.)
- Kattintható mezők a gyors másoláshoz (üzenet azonosító, URI, kérés azonosító)

#### HTTP fejlécek szakasz
- Formázott HTTP fejlécek színkiemeléssel
- Fejléc nevek és értékek elkülönítve
- Érzékeny fejlécek automatikus maszkolása (beállítástól függően)

#### Üzenettörzs szakasz
- Formázott üzenettörzs szintaxis kiemeléssel (JSON, XML)
- Görgethető konténer a nagy üzenettörzsek kezeléséhez
- Érzékeny adatok maszkolása (beállítástól függően)

### Navigáció a részletes nézetben
- Használja a **Előző** és **Következő** gombokat a bejegyzések közötti navigáláshoz
- A navigációs számláló mutatja a jelenlegi pozíciót (pl. "Entry 3 of 42")
- Használhatja a billentyűzet nyíl billentyűit is a navigáláshoz (← és →)

### API hívás részletes nézet
Az API hívás részletes nézet harmonika paneleket használ a különböző flow-k megjelenítéséhez:

1. **PROXY_REQ_FLOW**: Alapértelmezetten nyitva
2. **PROXY_RESP_FLOW**: Alapértelmezetten nyitva, ha létezik
3. **TARGET_REQ_FLOW**: Alapértelmezetten zárva
4. **TARGET_RESP_FLOW**: Nyitva, ha nincs PROXY_RESP_FLOW

## Exportálási lehetőségek

Az alkalmazás többféle exportálási lehetőséget kínál a log adatok mentéséhez és megosztásához.

### Exportálás TXT formátumba
1. Kattintson az "Export" gombra a legördülő menü megnyitásához
2. Válassza az "Export logs to TXT" opciót
3. A fájl letöltődik a böngészőn keresztül
4. Az exportált fájl tartalmazza az összes szűrt bejegyzést részletes formátumban
5. Az érzékeny adatok alapértelmezetten maszkolva vannak

### Exportálás XLSX formátumba
1. Kattintson az "Export" gombra a legördülő menü megnyitásához
2. Válassza az "Export logs to XLSX" opciót
3. A fájl letöltődik a böngészőn keresztül
4. Az exportált fájl tartalmazza az összes szűrt bejegyzést táblázatos formátumban
5. Csak a látható oszlopok kerülnek exportálásra

### Exportálás képként
1. Kattintson az "Export" gombra a legördülő menü megnyitásához
2. Válassza az "Export current page to JPEG" opciót
3. A fájl letöltődik a böngészőn keresztül
4. Az exportált kép csak az aktuális oldalt tartalmazza

### Másolás a vágólapra
1. Kattintson a "Copy Text" gombra a főképernyőn
2. A szűrt bejegyzések táblázatos formátumban másolódnak a vágólapra
3. Beillesztheti a másolt adatokat bármely szövegszerkesztőbe vagy táblázatkezelőbe

### Részletes nézet exportálása
1. A részletes nézetben kattintson az exportálás gombra
2. A kiválasztott bejegyzés vagy API hívás részletei TXT formátumban exportálódnak
3. Az exportált fájl tartalmazza az összes szakaszt (előnézet, fejlécek, üzenettörzs)
4. Az érzékeny adatok a jelenlegi maszkolási beállítás szerint exportálódnak

## Érzékeny adatok kezelése

Az alkalmazás átfogó maszkolási funkciókat kínál az érzékeny információk védelmére.

### Maszkolható elemek
- Authorization fejlécek
- Kliens azonosítók (client_id)
- API kulcsok
- OAuth tokenek
- Jelszavak
- Munkamenet tokenek

### Maszkolás beállítása
1. Alapértelmezetten az érzékeny adatok maszkolva vannak
2. A részletes nézetben használja a maszkolás kapcsolót az érzékeny adatok megjelenítéséhez/elrejtéséhez
3. A kapcsoló a részletes nézet fejlécében található (szem ikon)

### Maszkolás működése
- Az érzékeny adatok "*** masked ***" szöveggel helyettesítődnek
- Az eredeti adatok megmaradnak, és a maszkolás kapcsolóval megjeleníthetők
- A másolási és exportálási műveletek figyelembe veszik a jelenlegi maszkolási állapotot
- A TXT exportálás mindig maszkolja az érzékeny adatokat a biztonság érdekében

### Maszkolás a különböző nézetekben
- **Táblázatos nézet**: Az érzékeny adatok nem jelennek meg a táblázatban
- **Részletes nézet**: Az érzékeny adatok maszkolva jelennek meg, de a maszkolás kapcsolóval megjeleníthetők
- **Exportált fájlok**: Az érzékeny adatok a beállításoknak megfelelően maszkolódnak

## Hibaelhárítás

### Gyakori problémák és megoldások

#### A log formátum nem ismerhető fel
**Probléma**: Az alkalmazás nem ismeri fel a log formátumot vagy hibásan azonosítja.
**Megoldás**:
1. Ellenőrizze, hogy a log tartalom teljes és érvényes
2. Próbálja meg előfeldolgozni a logot egy szövegszerkesztőben (pl. felesleges karakterek eltávolítása)
3. Ellenőrizze, hogy a log formátum megfelel a támogatott formátumok egyikének

#### Nem jelennek meg a log bejegyzések
**Probléma**: A logok feldolgozása sikeresnek tűnik, de nem jelennek meg bejegyzések.
**Megoldás**:
1. Ellenőrizze, hogy nincsenek-e aktív szűrők, amelyek kiszűrik az összes bejegyzést
2. Kattintson a "Flows" gombra és ellenőrizze, hogy az "All flows" opció be van-e jelölve
3. Kattintson a "Message IDs" gombra és ellenőrizze, hogy az "All message IDs" opció be van-e jelölve
4. Törölje a keresőmezőt, ha van benne szöveg

#### Hibás HTTP fejléc formázás
**Probléma**: A HTTP fejlécek nem megfelelően formázódnak a részletes nézetben.
**Megoldás**:
1. Ellenőrizze, hogy a log tartalom tartalmazza-e a HTTP fejléceket a megfelelő formátumban
2. Próbáljon meg egy másik log fájlt betölteni összehasonlításképpen
3. Jelentse a problémát a fejlesztőknek a log minta megadásával

#### Teljesítmény problémák nagy log fájloknál
**Probléma**: Az alkalmazás lelassul vagy lefagy nagy log fájlok feldolgozásakor.
**Megoldás**:
1. Ossza fel a nagy log fájlokat kisebb részekre
2. Növelje a böngésző memóriáját vagy használjon erősebb számítógépet
3. Használja a lapozási funkciót a nagy adathalmazok kezeléséhez
4. Alkalmazza a szűrőket a feldolgozás előtt a releváns adatok kiválasztásához

### Támogatott böngészők
- Google Chrome (ajánlott)
- Mozilla Firefox
- Microsoft Edge
- Safari

### Kapcsolat és támogatás
Ha további segítségre van szüksége vagy hibát szeretne jelenteni, kérjük, forduljon a rendszergazdához vagy a fejlesztőcsapathoz.
