<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenSearch Integration Test</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .console-output {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .success { color: #4caf50; }
        .error { color: #f44336; }
        .info { color: #2196f3; }
        .warning { color: #ff9800; }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="bi bi-search me-2"></i>OpenSearch Integration Test</h1>
        <p class="text-muted">Test the OpenSearch integration components</p>

        <!-- Connection Test Section -->
        <div class="test-section">
            <h3><i class="bi bi-wifi me-2"></i>Connection Test</h3>
            <p>Test connection to OpenSearch cluster</p>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="testEndpoint" class="form-label">OpenSearch URL</label>
                        <input type="text" class="form-control" id="testEndpoint"
                               value="https://bslogmesprod1.vodafone.hu:9200" readonly>
                        <div class="form-text">
                            <i class="bi bi-info-circle text-info me-1"></i>
                            Pre-configured for your environment
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="testIndex" class="form-label">Index Pattern</label>
                        <select class="form-select" id="testIndex">
                            <option value="okd-test*">okd-test*</option>
                            <option value="okd-prod*">okd-prod*</option>
                            <option value="apigee-e2e*">apigee-e2e*</option>
                            <option value="apigee-sit*">apigee-sit*</option>
                            <option value="apigee-uat-pet-*">apigee-uat-pet-*</option>
                            <option value="apigee-prod-*" selected>apigee-prod-*</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="testUsername" class="form-label">Username</label>
                        <input type="text" class="form-control" id="testUsername" 
                               placeholder="Enter username">
                    </div>
                    <div class="mb-3">
                        <label for="testPassword" class="form-label">Password</label>
                        <input type="password" class="form-control" id="testPassword" 
                               placeholder="Enter password">
                    </div>
                    <button class="btn btn-warning" onclick="acceptCertificate()">
                        <i class="bi bi-shield-exclamation me-1"></i>Accept Certificate
                    </button>
                    <button class="btn btn-primary" onclick="testConnection()">
                        <i class="bi bi-play-circle me-1"></i>Test Connection
                    </button>
                    <button class="btn btn-secondary" onclick="testSearch()">
                        <i class="bi bi-search me-1"></i>Test Search
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearConsole()">
                        <i class="bi bi-trash me-1"></i>Clear Console
                    </button>
                </div>
                <div class="col-md-6">
                    <label class="form-label">Console Output</label>
                    <div id="console" class="console-output">
                        Ready to test OpenSearch integration...
                    </div>
                </div>
            </div>
        </div>

        <!-- Mode Switcher Test Section -->
        <div class="test-section">
            <h3><i class="bi bi-toggle-on me-2"></i>Mode Switcher Test</h3>
            <p>Test the mode switching functionality</p>
            
            <div class="mode-switcher-wrapper mb-3">
                <label class="mode-switch" for="testModeSwitch">
                    <span class="mode-label">File Mode</span>
                    <input type="checkbox" id="testModeSwitch" />
                    <div class="mode-slider">
                        <i class="bi bi-file-earmark-text mode-icon file"></i>
                        <i class="bi bi-search mode-icon search"></i>
                    </div>
                    <span class="mode-label">OpenSearch Mode</span>
                </label>
            </div>
            
            <div id="currentMode" class="alert alert-info">
                Current Mode: <strong>File Mode</strong>
            </div>
        </div>

        <!-- Data Transformation Test Section -->
        <div class="test-section">
            <h3><i class="bi bi-arrow-left-right me-2"></i>Data Transformation Test</h3>
            <p>Test OpenSearch response transformation</p>
            
            <button class="btn btn-info" onclick="testTransformation()">
                <i class="bi bi-gear me-1"></i>Test Data Transformation
            </button>
            
            <div id="transformationResult" class="mt-3" style="display: none;">
                <h5>Transformation Result:</h5>
                <pre id="transformationOutput" class="console-output"></pre>
            </div>
        </div>
    </div>

    <!-- Include Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Include our modules -->
    <script type="module">
        import { opensearchClient } from './modules/opensearch-client.js';
        import { opensearchTransformer } from './modules/opensearch-transformer.js';
        import { modeManager } from './modules/mode-manager.js';

        // Make modules available globally for testing
        window.opensearchClient = opensearchClient;
        window.opensearchTransformer = opensearchTransformer;
        window.modeManager = modeManager;

        // Test functions
        window.acceptCertificate = function() {
            const endpoint = document.getElementById('testEndpoint').value;
            logToConsole('Opening OpenSearch URL to accept certificate...', 'info');
            logToConsole('Please accept the certificate in the new tab, then return here to test connection.', 'warning');
            window.open(endpoint, '_blank');
        };

        window.testConnection = async function() {
            const endpoint = document.getElementById('testEndpoint').value;
            const indexPattern = document.getElementById('testIndex').value;
            const username = document.getElementById('testUsername').value;
            const password = document.getElementById('testPassword').value;

            if (!username || !password) {
                logToConsole('Please enter username and password', 'warning');
                return;
            }

            logToConsole('Testing OpenSearch connection...', 'info');

            try {
                opensearchClient.updateConfig({
                    endpoint,
                    indexPattern,
                    username,
                    password
                });

                const result = await opensearchClient.testConnection();
                
                if (result.success) {
                    logToConsole('✅ Connection successful!', 'success');
                    logToConsole(`Cluster: ${result.cluster.name}`, 'info');
                    logToConsole(`Version: ${result.cluster.version}`, 'info');
                    logToConsole(`Indices found: ${result.indices.length}`, 'info');
                } else {
                    logToConsole('❌ Connection failed: ' + result.error, 'error');
                }
            } catch (error) {
                logToConsole('❌ Error: ' + error.message, 'error');
            }
        };

        window.testSearch = async function() {
            logToConsole('Testing OpenSearch search...', 'info');

            try {
                const searchResult = await opensearchClient.search(
                    { match_all: {} }, 
                    { size: 3 }
                );

                if (searchResult.success) {
                    logToConsole('✅ Search successful!', 'success');
                    logToConsole(`Total hits: ${searchResult.total.value}`, 'info');
                    logToConsole(`Returned: ${searchResult.hits.length} documents`, 'info');
                    
                    // Transform the results
                    const transformed = opensearchTransformer.transformSearchResponse(searchResult.rawResponse);
                    logToConsole(`Transformed ${transformed.entries.length} entries`, 'info');
                    
                    // Show first entry as example
                    if (transformed.entries.length > 0) {
                        logToConsole('Sample transformed entry:', 'info');
                        logToConsole(JSON.stringify(transformed.entries[0], null, 2), 'info');
                    }
                } else {
                    logToConsole('❌ Search failed: ' + searchResult.error, 'error');
                }
            } catch (error) {
                logToConsole('❌ Search error: ' + error.message, 'error');
            }
        };

        window.testTransformation = function() {
            // Sample OpenSearch response
            const sampleResponse = {
                hits: {
                    total: { value: 1, relation: 'eq' },
                    hits: [{
                        _id: 'test123',
                        _index: 'apigee-prod-2024-01',
                        _score: 1.0,
                        _source: {
                            '@timestamp': '2024-01-15T10:30:00.000Z',
                            messageId: 'msg-12345',
                            flow: 'PROXY_REQ_FLOW',
                            statusCode: '200',
                            uri: '/api/v1/users',
                            appName: 'test-app',
                            environment: 'prod',
                            verb: 'GET',
                            headers: {
                                'content-type': 'application/json',
                                'authorization': 'Bearer token123'
                            },
                            messageBody: '{"user": "john", "action": "login"}'
                        }
                    }]
                },
                took: 5
            };

            const transformed = opensearchTransformer.transformSearchResponse(sampleResponse);
            
            document.getElementById('transformationResult').style.display = 'block';
            document.getElementById('transformationOutput').textContent = 
                JSON.stringify(transformed, null, 2);
            
            logToConsole('✅ Data transformation test completed', 'success');
        };

        window.logToConsole = function(message, type = 'info') {
            const console = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type;
            
            console.innerHTML += `<span class="${colorClass}">[${timestamp}] ${message}</span>\n`;
            console.scrollTop = console.scrollHeight;
        };

        window.clearConsole = function() {
            document.getElementById('console').innerHTML = 'Console cleared...\n';
        };

        // Set up mode switcher test
        document.getElementById('testModeSwitch').addEventListener('change', function(e) {
            const mode = e.target.checked ? 'OpenSearch Mode' : 'File Mode';
            document.getElementById('currentMode').innerHTML = 
                `Current Mode: <strong>${mode}</strong>`;
            logToConsole(`Mode switched to: ${mode}`, 'info');
        });

        logToConsole('OpenSearch Integration Test loaded successfully', 'success');
    </script>

    <!-- Include CSS for mode switcher -->
    <link rel="stylesheet" href="apigeeLogProcessor.css">
</body>
</html>
