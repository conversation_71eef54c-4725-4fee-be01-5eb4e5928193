(function() {
    // State management
    const initialState = {
        logEntries: [],
        currentLogIndex: 0,
        filteredIndex: 0,
        filters: {
            info: true,
            debug: true,
            trace: true,
            error: true
        },
        selectedJobIds: [], // For specific selections
        allJobIds: true,    // By default show all jobIds
        globalSearchActive: false, // New flag for global search mode
        globalSearchTerm: '', // Store the global search term
        globalSearchMatches: 0, // Counter for global search matches
        dateFilter: {
            active: false,
            startDate: null,
            endDate: null
        }
    };

    // Initialize state
    let logEntries = [...initialState.logEntries];
    let currentLogIndex = initialState.currentLogIndex;
    let filteredIndex = initialState.filteredIndex;
    let filters = { ...initialState.filters };
    let selectedJobIds = [...initialState.selectedJobIds];
    let allJobIds = initialState.allJobIds;  // Add this flag to show all by default
    let globalSearchActive = initialState.globalSearchActive; // Track if global search is active
    let globalSearchTerm = initialState.globalSearchTerm; // Track global search term
    let globalSearchMatches = initialState.globalSearchMatches; // Counter for global search matches
    let dateFilter = { ...initialState.dateFilter }; // Initialize date filter state

    // Global search state
    let searchState = {
        searchTerm: ''
    };

    // Function to parse complex search expressions with parentheses and AND/OR operators
    function parseSearchTerms(searchStr) {
        let pos = 0;
        const str = searchStr.trim();

        if (!str) return null;

        // Handle escaped characters in quotes
        function unescapeString(str) {
            return str.replace(/\\([\\"])/, '$1');
        }

        function parseExpression() {
            let terms = [];
            let currentTerm = '';
            let operator = null;
            let inQuotes = false;
            let escaped = false;

            function addTerm(term) {
                if (!term) return;
                term = term.trim();
                if (!term) return;

                // Handle quoted terms specially
                if (term.startsWith('"') && term.endsWith('"')) {
                    terms.push({
                        value: unescapeString(term.slice(1, -1)),
                        quoted: true,
                        exact: true
                    });
                } else {
                    terms.push({
                        value: term,
                        quoted: false,
                        exact: false
                    });
                }
            }

            function processSubExpression(start, end) {
                const subExpr = str.substring(start, end).trim();
                if (subExpr) {
                    const savedPos = pos;
                    pos = 0;
                    const result = parseSearchTerms(subExpr);
                    pos = savedPos;
                    return result;
                }
                return null;
            }

            // First, handle any outer parentheses
            if (str.startsWith('(')) {
                let parenCount = 1;
                let endPos = 1;
                while (endPos < str.length && parenCount > 0) {
                    if (str[endPos] === '(') parenCount++;
                    if (str[endPos] === ')') parenCount--;
                    endPos++;
                }
                if (parenCount === 0) {
                    const subResult = processSubExpression(1, endPos - 1);
                    if (subResult) return subResult;
                }
            }

            // Process the expression character by character
            while (pos < str.length) {
                const char = str[pos];

                if (char === '\\' && !escaped) {
                    escaped = true;
                    currentTerm += char;
                }
                else if (char === '"' && !escaped) {
                    inQuotes = !inQuotes;
                    currentTerm += char;
                }
                else if (!inQuotes && char === '(') {
                    // Start of a sub-expression
                    if (currentTerm.trim()) {
                        addTerm(currentTerm);
                        currentTerm = '';
                    }
                    let parenCount = 1;
                    const startPos = pos + 1;
                    pos++;
                    while (pos < str.length && parenCount > 0) {
                        if (str[pos] === '(') parenCount++;
                        if (str[pos] === ')') parenCount--;
                        pos++;
                    }
                    if (parenCount <= 0) {
                        const subResult = processSubExpression(startPos, pos - 1);
                        if (subResult) terms.push(subResult);
                    } else {
                        // Unclosed parenthesis - treat remaining as normal text
                        const remaining = str.substring(startPos);
                        if (remaining.trim()) {
                            const subResult = processSubExpression(startPos, str.length);
                            if (subResult) terms.push(subResult);
                        }
                    }
                    continue;
                }
                else if (!inQuotes && (
                    str.substr(pos, 5).toUpperCase() === ' AND ' ||
                    str.substr(pos, 4).toUpperCase() === ' OR '
                )) {
                    addTerm(currentTerm);
                    currentTerm = '';
                    const newOperator = str.substr(pos, str.substr(pos, 5).toUpperCase() === ' AND ' ? 5 : 4).trim().toUpperCase();
                    if (operator && operator !== newOperator) {
                        terms = [{ type: operator, terms }];
                    }
                    operator = newOperator;
                    pos += newOperator.length + (newOperator === 'AND' ? 2 : 1);
                    continue;
                }
                else {
                    currentTerm += char;
                }
                pos++;
            }

            // Handle any remaining term
            addTerm(currentTerm);

            // Clean up and return result
            if (terms.length === 0) return null;
            if (terms.length === 1) return terms[0];
            return { type: operator || 'AND', terms };
        }

        return parseExpression();
    }

    // Recursive function to evaluate terms with AND/OR logic
    function evaluateSearchTerms(content, terms) {
        if (!terms || terms.length === 0) return true;

        if (terms.type === 'AND') {
            return terms.terms.every(term => evaluateSearchTerms(content, term));
        } else if (terms.type === 'OR') {
            return terms.terms.some(term => evaluateSearchTerms(content, term));
        }

        // Handle individual terms with search options
        let searchContent = content.toLowerCase();
        let termValue = terms.value || terms;

        // For exact matches, always use case-sensitive comparison
        if (terms.exact) {
            return content.includes(termValue);
        }

        // For quoted/exact terms, use case-sensitive search
        if (terms.quoted || terms.exact) {
            return content.includes(termValue);
        }

        // For regular terms, always use case-insensitive search
        return searchContent.includes(termValue.toLowerCase());
    }

    function performSearch() {
        const searchInput = document.getElementById('logSearchInput');
        const searchTerm = searchInput.value.trim();

        if (!searchTerm) {
            clearSearchHighlights();
            updateMatchCount(0);
            return;
        }

        try {
            const terms = parseSearchTerms(searchTerm);
            if (!terms) {
                updateSearchHelpText('No valid search terms');
                return;
            }

            // Perform the search and highlight matches
            const logContent = document.getElementById('logContent');
            const content = logContent.textContent;

            // Clear previous highlights
            clearSearchHighlights();

            // Find and highlight all matches
            highlightMatches(terms);

            // Update match count and navigation
            const matches = logContent.querySelectorAll('.search-highlight, .search-highlight-and, .search-highlight-or');
            updateMatchCount(matches.length);

            // If we have matches, highlight the first one
            if (matches.length > 0) {
                currentMatchIndex = 0;
                highlightCurrentMatch();
            }
        } catch (e) {
            console.error('Search error:', e);
            updateSearchHelpText('Invalid search pattern');
        }
    }

    function updateMatchCount(count) {
        const matchCount = document.getElementById('searchMatchCount');
        const prevBtn = document.getElementById('prevMatchBtn');
        const nextBtn = document.getElementById('nextMatchBtn');

        matchCount.textContent = count === 0 ? 'No matches' :
            count === 1 ? '1 match' : `${count} matches`;

        prevBtn.disabled = count === 0;
        nextBtn.disabled = count === 0;

        if (count > 0) {
            matchCount.textContent += ` (${currentMatchIndex + 1}/${count})`;
        }
    }

    // Debounce helper function
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM content loaded');
        document.getElementById('textInput').value = '';
        document.getElementById('fileInput').value = '';

        // Theme switching functionality
        initTheme();

        // Set up toggle input button with a slight delay to ensure all elements are loaded
        setTimeout(function() {
            setupToggleInputButton();
            console.log('Toggle button setup completed');
        }, 500);

        document.getElementById('infoBtn').addEventListener('change', function() {
            filters.info = this.checked;
            displayCurrentLogEntry(true);
        });
        document.getElementById('debugBtn').addEventListener('change', function() {
            filters.debug = this.checked;
            displayCurrentLogEntry(true);
        });
        document.getElementById('traceBtn').addEventListener('change', function() {
            filters.trace = this.checked;
            displayCurrentLogEntry(true);
        });
        document.getElementById('errorBtn').addEventListener('change', function() {
            filters.error = this.checked;
            displayCurrentLogEntry(true);
        });

        document.getElementById('fileInput').addEventListener('change', function() {
            document.getElementById('textInput').value = '';
        });
        document.getElementById('textInput').addEventListener('input', function() {
            document.getElementById('fileInput').value = '';
        });

        // Set up back to top button
        const backToTopButton = document.getElementById('backToTopButton');

        // Show button when user scrolls down 300px
        window.addEventListener('scroll', function() {
            if (window.scrollY > 300) {
                backToTopButton.classList.remove('hidden');
                backToTopButton.classList.add('visible');
            } else {
                backToTopButton.classList.remove('visible');
                backToTopButton.classList.add('hidden');
            }
        });

        // Scroll to top when button is clicked
        backToTopButton.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });

        // Initialize JobID filtering
        selectedJobIds = [];
        allJobIds = true;

        // Initialize date filter functionality
        initDateFilter();
    });

    // Set up toggle input button functionality
    function setupToggleInputButton() {
        console.log('Setting up toggle input button');
        const toggleInputButton = document.getElementById('toggleInputButton');
        const inputSection = document.getElementById('inputSection');

        if (!toggleInputButton || !inputSection) {
            console.error('Toggle button or input section not found');
            return;
        }

        // Remove any existing event listeners to prevent duplicates
        toggleInputButton.removeEventListener('click', handleToggleClick);

        // Add the event listener
        toggleInputButton.addEventListener('click', handleToggleClick);

        console.log('Toggle button event listener added');
    }

    // Handle toggle button click
    function handleToggleClick(event) {
        console.log('Toggle button clicked');
        const toggleInputButton = document.getElementById('toggleInputButton');
        const inputSection = document.getElementById('inputSection');

        if (!toggleInputButton || !inputSection) {
            console.error('Toggle button or input section not found in click handler');
            return;
        }

        // Check if input section is currently collapsed
        const isCollapsed = inputSection.classList.contains('collapsed');
        console.log('Input section is currently', isCollapsed ? 'collapsed' : 'expanded');

        if (isCollapsed) {
            // Expand the input section
            inputSection.classList.remove('collapsed');
            toggleInputButton.classList.remove('collapsed');
            toggleInputButton.setAttribute('title', 'Collapse input section');
            toggleInputButton.querySelector('i').className = 'bi bi-chevron-up';
            console.log('Input section expanded - showing up arrow');
        } else {
            // Collapse the input section
            inputSection.classList.add('collapsed');
            toggleInputButton.classList.add('collapsed');
            toggleInputButton.setAttribute('title', 'Expand input section');
            toggleInputButton.querySelector('i').className = 'bi bi-chevron-down';
            console.log('Input section collapsed - showing down arrow');
        }
    }

    // Initialize theme based on user preference or system preference
    function initTheme() {
        const themeSwitch = document.getElementById('themeSwitch');
        if (!themeSwitch) {
            console.warn('Theme switch element not found');
            return;
        }

        // Check if user previously set a theme preference
        const currentTheme = localStorage.getItem('theme') ||
            (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light');

        // Set initial state based on saved preference or system preference
        if (currentTheme === 'dark') {
            document.body.classList.add('dark-theme');
            themeSwitch.checked = true;
        } else {
            document.body.classList.remove('dark-theme');
            themeSwitch.checked = false;
        }

        // Listen for theme changes from localStorage (set by the parent page)
        window.addEventListener('storage', (event) => {
            if (event.key === 'theme') {
                const newTheme = event.newValue;
                if (newTheme && (newTheme === 'light' || newTheme === 'dark')) {
                    console.log('OKD theme: Theme changed via localStorage to:', newTheme);
                    if (newTheme === 'dark') {
                        document.body.classList.add('dark-theme');
                        themeSwitch.checked = true;
                    } else {
                        document.body.classList.remove('dark-theme');
                        themeSwitch.checked = false;
                    }
                }
            }
        });
    }

    function resetState() {
        logEntries = [...initialState.logEntries];
        currentLogIndex = initialState.currentLogIndex;
        filteredIndex = initialState.filteredIndex;
        filters = { ...initialState.filters };
        selectedJobIds = [...initialState.selectedJobIds];
        globalSearchActive = initialState.globalSearchActive;
        globalSearchTerm = initialState.globalSearchTerm;
        globalSearchMatches = initialState.globalSearchMatches;
        dateFilter = { ...initialState.dateFilter }; // Reset date filter
    }

    // Initialize date filter functionality
    function initDateFilter() {
        const dateFilterPanel = document.getElementById('dateFilterPanel');
        const startDateInput = document.getElementById('startDateInput');
        const startTimeInput = document.getElementById('startTimeInput');
        const endDateInput = document.getElementById('endDateInput');
        const endTimeInput = document.getElementById('endTimeInput');

        const applyDateFilterBtn = document.getElementById('applyDateFilterBtn');
        const resetDateFilterBtn = document.getElementById('resetDateFilterBtn');
        const dateFilterStatus = document.getElementById('dateFilterStatus');

        if (!dateFilterPanel || !startDateInput || !startTimeInput ||
            !endDateInput || !endTimeInput || !applyDateFilterBtn || !resetDateFilterBtn || !dateFilterStatus) {
            console.error('Date filter elements not found');
            return;
        }

        // Set default time values for better user experience
        initializeDefaultTimeValues();

        // Apply date filter
        applyDateFilterBtn.addEventListener('click', function() {
            // Get date and time values
            const startDateValue = startDateInput.value;
            const startTimeValue = startTimeInput.value || '00:00:00';
            const endDateValue = endDateInput.value;
            const endTimeValue = endTimeInput.value || '23:59:59';

            if (!startDateValue && !endDateValue) {
                showDateFilterStatus('Please set at least one date', 'warning');
                return;
            }

            // Parse dates and validate
            let startDate = null;
            let endDate = null;

            if (startDateValue) {
                // Combine date and time
                const startDateTime = `${startDateValue}T${startTimeValue}`;
                startDate = new Date(startDateTime);

                if (isNaN(startDate.getTime())) {
                    showDateFilterStatus('Invalid start date/time', 'warning');
                    return;
                }
            }

            if (endDateValue) {
                // Combine date and time
                const endDateTime = `${endDateValue}T${endTimeValue}`;
                endDate = new Date(endDateTime);

                if (isNaN(endDate.getTime())) {
                    showDateFilterStatus('Invalid end date/time', 'warning');
                    return;
                }
            }

            if (startDate && endDate && startDate > endDate) {
                showDateFilterStatus('Start date/time cannot be after end date/time', 'warning');
                return;
            }

            // Set the date filter and display filtered logs
            dateFilter.active = true;
            dateFilter.startDate = startDate;
            dateFilter.endDate = endDate;

            // Show success message
            const message = getDateFilterStatusMessage(startDate, endDate);
            showDateFilterStatus(message, 'success');

            // Apply filter and update display
            displayCurrentLogEntry(true);
        });

        // Reset date filter
        resetDateFilterBtn.addEventListener('click', function() {
            // Clear the inputs
            startDateInput.value = '';
            startTimeInput.value = '';
            endDateInput.value = '';
            endTimeInput.value = '';

            // Reset the filter state
            dateFilter.active = false;
            dateFilter.startDate = null;
            dateFilter.endDate = null;

            // Update UI
            dateFilterStatus.classList.remove('active');

            // Apply filter and update display
            displayCurrentLogEntry(true);
        });

        // Initialize default time values and handle UI interactions
        function initializeDefaultTimeValues() {
            // Set default values for time inputs for better UX
            startTimeInput.value = '00:00:00';
            endTimeInput.value = '23:59:59';

            // Set today's date when clicking on time inputs without date selected
            startTimeInput.addEventListener('focus', function() {
                if (!startDateInput.value) {
                    const today = new Date().toISOString().split('T')[0];
                    startDateInput.value = today;
                }
            });

            endTimeInput.addEventListener('focus', function() {
                if (!endDateInput.value) {
                    const today = new Date().toISOString().split('T')[0];
                    endDateInput.value = today;
                }
            });

            // Auto-set time when date is selected
            startDateInput.addEventListener('change', function() {
                if (this.value && !startTimeInput.value) {
                    startTimeInput.value = '00:00:00';
                }
            });

            endDateInput.addEventListener('change', function() {
                if (this.value && !endTimeInput.value) {
                    endTimeInput.value = '23:59:59';
                }
            });
        }

        // Helper to show status messages
        function showDateFilterStatus(message, type) {
            dateFilterStatus.textContent = message;
            dateFilterStatus.className = 'date-filter-status active';

            if (type) {
                dateFilterStatus.classList.add(type);
            }
        }

        // Helper to format date filter status message
        function getDateFilterStatusMessage(startDate, endDate) {
            if (startDate && endDate) {
                return `Filtering logs from ${formatDateForDisplay(startDate)} to ${formatDateForDisplay(endDate)}`;
            } else if (startDate) {
                return `Filtering logs from ${formatDateForDisplay(startDate)}`;
            } else if (endDate) {
                return `Filtering logs up to ${formatDateForDisplay(endDate)}`;
            }
            return '';
        }

        // Helper to format date for display using 24-hour format
        function formatDateForDisplay(date) {
            // Format date in 24h format: YYYY-MM-DD HH:MM:SS
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }
    }

  function showToast(message, type = 'warning') {
    const toastLiveExample = document.getElementById('liveToast');
    const toastMessage = document.getElementById('toastMessage');
    toastMessage.textContent = message; // Set the message

    // Set the border color based on whether it's an error or a notification
    if (type === 'warning') {
      toastLiveExample.classList.remove('border-success');
      toastLiveExample.classList.add('border-danger');
    } else {
      toastLiveExample.classList.remove('border-danger');
      toastLiveExample.classList.add('border-success');
    }

    const toast = new bootstrap.Toast(toastLiveExample);
    toast.show();
  }

    // Function to update log level badges with counts
    function updateLogLevelBadges() {
        // Count logs by level
        const levelCounts = {
            info: 0,
            debug: 0,
            trace: 0,
            error: 0
        };

        // Count occurrences of each log level
        logEntries.forEach(entry => {
            const level = entry.level ? entry.level.toLowerCase() : '';
            if (levelCounts.hasOwnProperty(level)) {
                levelCounts[level]++;
            }
        });

        console.log('Log level counts:', levelCounts);

        // Update each log level label with count
        for (const level in levelCounts) {
            const count = levelCounts[level];
            const labelId = `${level}Label`;
            const label = document.getElementById(labelId);

            if (label) {
                // Update the label text to include the count
                const levelName = level.toUpperCase();
                label.innerHTML = `${levelName} <span class="log-count">(${count})</span>`;
            }
        }
    }

    // Function to clear log level badge counts
    function clearLogLevelBadges() {
        // Reset each log level label
        const levels = ['info', 'debug', 'trace', 'error'];
        levels.forEach(level => {
            const labelId = `${level}Label`;
            const label = document.getElementById(labelId);
            if (label) {
                label.innerHTML = level.toUpperCase();
            }
        });
    }

    function processData() {
        console.log('Processing data...');
        resetState();

        // Reset JobID filtering to default state
        selectedJobIds = [];
        allJobIds = true;  // Make sure this is set to true when processing new logs

        // Set default filter states
        document.getElementById('infoBtn').checked = true;
        document.getElementById('debugBtn').checked = true;
        document.getElementById('traceBtn').checked = true;
        document.getElementById('errorBtn').checked = true;
        filters = { info: true, debug: true, trace: true, error: true };

        const fileInput = document.getElementById('fileInput');
        const textInput = document.getElementById('textInput').value.trim();

        if (!fileInput.files.length && !textInput) {
          showToast("Please provide a log file or paste log content.", 'warning');
          return; // Stop processing if no input
        }

        if (fileInput.files.length > 0) {
            const reader = new FileReader();
            reader.onload = function(event) {
                processLogs(event.target.result);

                // Show the toggle button and collapse the input section after processing
                const inputSection = document.getElementById('inputSection');
                const toggleInputButton = document.getElementById('toggleInputButton');
                if (inputSection && toggleInputButton) {
                    console.log('Initializing toggle button after processing logs');

                    // Collapse the input section
                    inputSection.classList.add('collapsed');

                    // Configure the toggle button
                    toggleInputButton.classList.remove('d-none');
                    toggleInputButton.classList.add('collapsed');
                    toggleInputButton.setAttribute('title', 'Expand input section');

                    // Make sure the button is visible and clickable
                    toggleInputButton.style.display = 'inline-block';
                    toggleInputButton.style.pointerEvents = 'auto';

                    // Ensure the icon is correct - down arrow when collapsed
                    const icon = toggleInputButton.querySelector('i');
                    if (icon) {
                        icon.className = 'bi bi-chevron-down';
                        console.log('Setting icon to down arrow for collapsed state');
                    }

                    // Re-initialize the toggle button functionality
                    setupToggleInputButton();
                }
            };
            reader.readAsText(fileInput.files[0]);
        } else if (textInput) {
            processLogs(textInput);

            // Show the toggle button and collapse the input section after processing
            const inputSection = document.getElementById('inputSection');
            const toggleInputButton = document.getElementById('toggleInputButton');
            if (inputSection && toggleInputButton) {
                console.log('Initializing toggle button after processing logs');

                // Collapse the input section
                inputSection.classList.add('collapsed');

                // Configure the toggle button
                toggleInputButton.classList.remove('d-none');
                toggleInputButton.classList.add('collapsed');
                toggleInputButton.setAttribute('title', 'Expand input section');

                // Make sure the button is visible and clickable
                toggleInputButton.style.display = 'inline-block';
                toggleInputButton.style.pointerEvents = 'auto';

                // Ensure the icon is correct - down arrow when collapsed
                const icon = toggleInputButton.querySelector('i');
                if (icon) {
                    icon.className = 'bi bi-chevron-down';
                    console.log('Setting icon to down arrow for collapsed state');
                }

                // Re-initialize the toggle button functionality
                setupToggleInputButton();
            }
        }
        // Remove the hidden class instead of changing visibility style
        document.getElementById('processedContainer').classList.remove('hidden');

        // Clear date filter inputs
        document.getElementById('startDateInput').value = '';
        document.getElementById('startTimeInput').value = '';
        document.getElementById('endDateInput').value = '';
        document.getElementById('endTimeInput').value = '';

        // Reset date filter status if you have one
        document.getElementById('dateFilterStatus').textContent = '';
    }

    function clearText() {
        document.getElementById('fileInput').value = '';
        document.getElementById('textInput').value = '';

        // Add safety checks for elements that might not exist yet
        const currentLogEntry = document.getElementById('currentLogEntry');
        if (currentLogEntry) {
            const logContentDisplay = document.getElementById('logContentDisplay');
            if (logContentDisplay) {
                logContentDisplay.textContent = '';
            }
            currentLogEntry.classList.add('hidden');
        }

        const processedContainer = document.getElementById('processedContainer');
        if (processedContainer) {
            processedContainer.classList.add('hidden');
        }

        resetState();

        // Reset log level filters
        document.getElementById('infoBtn').checked = false;
        document.getElementById('debugBtn').checked = false;
        document.getElementById('traceBtn').checked = false;
        document.getElementById('errorBtn').checked = false;
        filters = { info: false, debug: false, trace: false, error: false };

        // Clear log level badges
        clearLogLevelBadges();

        // Also clear search field
        const searchInput = document.getElementById('logSearchInput');
        if (searchInput) {
            searchInput.value = '';
        }

        // Reset search match highlights
        clearSearchHighlights();

        // Reset JobID filtering state
        selectedJobIds = [];
        allJobIds = true;

        // Reset global search state
        globalSearchActive = false;
        globalSearchTerm = '';
        globalSearchMatches = 0;

        // Reset global search toggle if it exists
        const globalSearchToggle = document.getElementById('globalSearchToggle');
        if (globalSearchToggle) {
            globalSearchToggle.checked = false;
        }

        // Reset date filter
        const startDateInput = document.getElementById('startDateInput');
        const endDateInput = document.getElementById('endDateInput');
        const startTimeInput = document.getElementById('startTimeInput');
        const endTimeInput = document.getElementById('endTimeInput');
        const dateFilterStatus = document.getElementById('dateFilterStatus');

        if (startDateInput) startDateInput.value = '';
        if (endDateInput) endDateInput.value = '';
        if (startTimeInput) startTimeInput.value = '';
        if (endTimeInput) endTimeInput.value = '';
        if (dateFilterStatus) {
            dateFilterStatus.textContent = '';
            dateFilterStatus.className = 'date-filter-status';
        }

        dateFilter = { ...initialState.dateFilter };

        // Reset navigation buttons
        const prevLogBtn = document.getElementById('prevLog');
        const nextLogBtn = document.getElementById('nextLog');
        if (prevLogBtn) prevLogBtn.disabled = true;
        if (nextLogBtn) nextLogBtn.disabled = true;

        // Reset entry counter and level indicator
        const entryCounter = document.getElementById('entryCounter');
        const levelIndicator = document.getElementById('levelIndicator');
        if (entryCounter) entryCounter.textContent = '0 of 0';
        if (levelIndicator) levelIndicator.innerHTML = '';
    }

    function extractMessageContent(log, startPos) {
        let content = '';
        let i = startPos;
        let escaped = false;
        let inQuotes = false;

        while (i < log.length) {
            let char = log[i];
            if (!inQuotes) {
                if (char === '"' && !escaped) inQuotes = true;
            } else {
                if (escaped) {
                    content += char;
                    escaped = false;
                } else if (char === '\\') {
                    escaped = true;
                } else if (char === '"') {
                    break;
                } else {
                    content += char;
                }
            }
            i++;
        }
        return content;
    }
// Format preserved content like Base64 and HTTP headers
    // Format preserved content like Base64 and HTTP headers
    function formatPreservedContent(content) {
        // Check if it appears to be base64 encoded
        if (/^[A-Za-z0-9+/=\s]+$/.test(content) && content.length > 100) {
            // Format base64 content to be more readable
            const trimmedContent = content.trim();
            const truncateLength = 50;

            if (trimmedContent.length > truncateLength * 2) {
                const start = trimmedContent.substring(0, truncateLength);
                const end = trimmedContent.substring(trimmedContent.length - truncateLength);
                const middleLength = trimmedContent.length - (truncateLength * 2);

                return `<span class="base64-content">
                    ${start}
                    <span class="truncated-indicator">... [${middleLength} more characters] ...</span>
                    ${end}
                </span>`;
            }
            return `<span class="base64-content">${content}</span>`;
        }

        // Check if it's HTTP headers
        if (content.includes('HTTP/1.1') || content.includes('Content-Type:') || content.includes('Content-Length:')) {
            return formatHttpHeaders(content);
        }

        // Return as-is for other content
        return content;
    }

    // Format HTTP headers with nice styling
    function formatHttpHeaders(content) {
        // Normalize line breaks
        content = content.replace(/\r\n?/g, '\n');

        // Regular HTTP headers (not in XML)
        // Split headers by standard HTTP line endings
        const lines = content.split(/\r?\n/);
        let formattedContent = '';

        for (let line of lines) {
            line = line.trim();
            if (!line) continue;

            // Check if this is a header line (contains ":")
            if (line.includes(':')) {
                const colonPos = line.indexOf(':');
                const name = line.substring(0, colonPos).trim();
                const value = line.substring(colonPos + 1).trim();
                formattedContent += `<span class="http-header">${name}:</span> <span class="http-header-value">${value}</span>\n`;
            } else {
                // This might be a status line or other non-header text
                formattedContent += `<span class="http-status-line">${line}</span>\n`;
            }
        }

        return formattedContent;
    }


    // Unified log processing function that handles all formats
    function processLogs(content) {
        logEntries = []; // Reset the array before processing
        let extractedMessages = [];

        // First, check if content matches the format with date patterns like "Mar 21, 2025 @ 08:06:53.152Row: 13, Column: 2:"
        const datePattern = /[A-Za-z]{3} \d{1,2}, \d{4} @ \d{2}:\d{2}:\d{2}\.\d{3}Row: \d+, Column: \d+:|[A-Za-z]{3} \d{1,2}, \d{4} @ \d{2}:\d{2}:\d{2}\.\d{3}|^\w{3} \d{2}, \d{4} @ \d{2}:\d{2}:\d{2}\.\d{3}/;
        const rowColumnPattern = /Row: \d+, Column: \d+:/g;
        const hasDatePattern = content.match(datePattern);

        // Check for "Row: X, Column: Y" pattern which appears in various logs
        const hasRowColumnPattern = content.match(rowColumnPattern);

        if (hasDatePattern || hasRowColumnPattern) {
            // This is the format like improve_formatting.txt or similar patterns
            console.log('Processing logs with date/row pattern format');

            // Split content by all potential delimiters
            let parts;
            let splitContent = content;

            if (hasRowColumnPattern) {
                // First clean up any rowColumn patterns that might be at the end of lines
                splitContent = content.replace(/Row: \d+, Column: \d+:(\r?\n|$)/g, '\n');
            }

            // Now split by empty lines or date patterns
            parts = splitContent.split(/\r?\n\r?\n|(?=[A-Za-z]{3} \d{1,2}, \d{4} @)/);

            for (let part of parts) {
                part = part.trim();
                if (!part) continue;

                try {
                    // Look for JSON object in this part - any content between { and matching }
                    const jsonStartIndex = part.indexOf('{');
                    if (jsonStartIndex !== -1) {
                        // Find the corresponding closing brace by counting opening and closing braces
                        let bracesCount = 0;
                        let jsonEndIndex = -1;

                        for (let j = jsonStartIndex; j < part.length; j++) {
                            if (part[j] === '{') bracesCount++;
                            if (part[j] === '}') bracesCount--;
                            if (bracesCount === 0) {
                                jsonEndIndex = j;
                                break;
                            }
                        }

                        if (jsonEndIndex !== -1) {
                            const jsonStr = part.substring(jsonStartIndex, jsonEndIndex + 1);
                            try {
                                const logObject = JSON.parse(jsonStr);

                                // Extract log level, message, JobId, and timestamp
                                const level = logObject.level || 'INFO';
                                const message = logObject.message || '';
                                const jobId = logObject.JobId || '';
                                const timestamp = logObject['@timestamp'] || '';

                                // Store the original JSON for export/copy functions
                                extractedMessages.push({
                                    level: level.toUpperCase(),
                                    content: jsonStr,
                                    originalJson: logObject,
                                    jobId: jobId,
                                    timestamp: timestamp
                                });
                            } catch (jsonParseError) {
                                // Silent fail for JSON parsing errors inside balanced braces
                                console.warn('JSON parse error for content:', jsonParseError.message);
                            }
                        }
                    }
                } catch (e) {
                    // Silently skip entries that cannot be processed
                    console.warn('Error processing log entry:', e.message);
                }
            }
        } else {
            // Format is like OKD_raw_log.txt
            console.log('Processing general log format');

            // Split by lines, look for JSON objects
            const lines = content.split(/\r?\n/);

            for (let line of lines) {
                line = line.trim();
                if (!line) continue;

                try {
                    // Check if the line is a complete JSON object or contains a JSON object
                    const jsonStartIndex = line.indexOf('{');
                    if (jsonStartIndex !== -1) {
                        // Find the corresponding closing brace by counting braces
                        let bracesCount = 0;
                        let jsonEndIndex = -1;

                        for (let j = jsonStartIndex; j < line.length; j++) {
                            if (line[j] === '{') bracesCount++;
                            if (line[j] === '}') bracesCount--;
                            if (bracesCount === 0) {
                                jsonEndIndex = j;
                                break;
                            }
                        }

                        if (jsonEndIndex !== -1) {
                            const jsonStr = line.substring(jsonStartIndex, jsonEndIndex + 1);
                            try {
                                const logObject = JSON.parse(jsonStr);

                                // Extract log level, message, JobId, and timestamp
                                const level = logObject.level || 'INFO';
                                const message = logObject.message || '';
                                const jobId = logObject.JobId || '';
                                const timestamp = logObject['@timestamp'] || '';

                                extractedMessages.push({
                                    level: level.toUpperCase(),
                                    content: jsonStr,
                                    originalJson: logObject,
                                    jobId: jobId,
                                    timestamp: timestamp
                                });
                            } catch (jsonError) {
                                // Not valid JSON, continue to the next line
                                console.warn('Invalid JSON:', jsonError.message);
                            }
                        }
                    }
                } catch (e) {
                    // Silently skip processing errors for this line
                }
            }
        }

        // Update the logEntries with extracted messages
        logEntries = extractedMessages;

        if (logEntries.length === 0) {
            showToast('No valid log entries found. Please check the input format.', 'warning');
            return;
        }

        // Sort entries by timestamp
        logEntries.sort((a, b) => {
            // First try to sort by the timestamp field
            if (a.timestamp && b.timestamp) {
                return new Date(a.timestamp) - new Date(b.timestamp);
            }

            // If no timestamp available, try to extract it from content
            try {
                const aTimestamp = a.content.match(/"@timestamp":"([^"]+)"/);
                const bTimestamp = b.content.match(/"@timestamp":"([^"]+)"/);

                if (aTimestamp && bTimestamp) {
                    return new Date(aTimestamp[1]) - new Date(bTimestamp[1]);
                }
            } catch (e) {
                console.error('Error comparing timestamps:', e);
            }

            // If all else fails, don't change order
            return 0;
        });

        console.log(`Processed ${logEntries.length} log entries with levels:`,
                    logEntries.map(entry => entry.level));

        // Update log level badges with counts
        updateLogLevelBadges();

        // Create initial entry instead of populating JobIDs immediately
        displayCurrentLogEntry(true);
    }

    function getSelectedLogLevels() {
        const selected = [];
        if (filters.info) selected.push('info', 'INFO');
        if (filters.debug) selected.push('debug', 'DEBUG');
        if (filters.trace) selected.push('trace', 'TRACE');
        if (filters.error) selected.push('error', 'ERROR');
        console.log('Selected log levels:', selected);
        return selected;
    }

    // Function to display current log entry with date filtering
    function displayCurrentLogEntry(resetIndex = false) {
        const selectedLevels = getSelectedLogLevels();
        console.log('Current filters:', selectedLevels, 'Selected JobId:', selectedJobIds, 'All JobId:', allJobIds,
                   'Global search:', globalSearchActive ? globalSearchTerm : 'disabled',
                   'Date filter:', dateFilter.active ? 'active' : 'disabled');

        const logContentDisplay = document.getElementById('logContentDisplay');
        const currentLogEntry = document.getElementById('currentLogEntry');
        const processedContainer = document.getElementById('processedContainer');
        const prevLogBtn = document.getElementById('prevLog');
        const nextLogBtn = document.getElementById('nextLog');

        // Ensure required elements exist
        if (!logContentDisplay || !currentLogEntry || !processedContainer || !prevLogBtn || !nextLogBtn) {
            console.error('Required DOM elements not found');
            return;
        }

        if (selectedLevels.length === 0) {
            logContentDisplay.textContent = 'Please select at least one log level filter.';
            currentLogEntry.classList.remove('hidden');
            processedContainer.classList.remove('hidden');
            prevLogBtn.disabled = true;
            nextLogBtn.disabled = true;
            return;
        }
    // Only reset the index if explicitly requested and we're not in the middle of a search operation
    if (resetIndex && (!globalSearchActive || !globalSearchTerm) &&
        (!document.getElementById('logSearchInput') ||
         !document.getElementById('logSearchInput').value)) {
        filteredIndex = 0;
    }


        // Filter logs based on selected levels, JobIDs, global search term, and date range
        const filteredLogs = logEntries.filter(log => {
            // Check log level match
            const logLevel = log.level ? log.level.toLowerCase() : '';
            const levelMatches = selectedLevels.includes(logLevel);

            // For JobID filtering, if allJobIds is true or no specific JobIDs selected, show all
            let jobIdMatches = allJobIds || selectedJobIds.length === 0;

            // Only check JobID if we're not showing all and have specific selections
            if (!allJobIds && selectedJobIds.length > 0) {
                const logContent = typeof log.content === 'string' ? log.content : JSON.stringify(log.content);
                jobIdMatches = selectedJobIds.some(jobId => logContent.includes(jobId));
            }

            // Global search filter with AND/OR support
            let globalSearchMatches = true;
            if (globalSearchActive && globalSearchTerm) {
                const searchableContent = typeof log.content === 'string'
                    ? log.content.replace(/<[^>]+>/g, '') // Strip HTML tags
                    : JSON.stringify(log.content);

                // Parse search terms for AND/OR operators
                const terms = parseSearchTerms(globalSearchTerm);

                // Apply the search logic based on parsed terms
                globalSearchMatches = evaluateSearchTerms(searchableContent.toLowerCase(), terms);
            }

            // Date filter
            let dateMatches = true;
            if (dateFilter.active && (dateFilter.startDate || dateFilter.endDate)) {
                let logDate = null;

                // Extract timestamp from log entry
                if (log.timestamp) {
                    logDate = new Date(log.timestamp);
                } else if (typeof log.content === 'string') {
                    const timestampMatch = log.content.match(/"@timestamp":"([^"]+)"/);
                    if (timestampMatch && timestampMatch[1]) {
                        logDate = new Date(timestampMatch[1]);
                    }
                }

                if (logDate && !isNaN(logDate.getTime())) {
                    // Apply start date filter if set
                    if (dateFilter.startDate && logDate < dateFilter.startDate) {
                        dateMatches = false;
                    }

                    // Apply end date filter if set
                    if (dateFilter.endDate && logDate > dateFilter.endDate) {
                        dateMatches = false;
                    }
                } else {
                    // If we can't determine a date, and date filter is active, exclude the log
                    if (dateFilter.active && (dateFilter.startDate || dateFilter.endDate)) {
                        dateMatches = false;
                    }
                }
            }

            return levelMatches && jobIdMatches && globalSearchMatches && dateMatches;
        });

        console.log('Total logs:', logEntries.length, 'Filtered logs:', filteredLogs.length);

        // Update global search matches count
        if (globalSearchActive && globalSearchTerm) {
            globalSearchMatches = filteredLogs.length;
        }

        // Handle the case when no logs match the filters
        if (filteredLogs.length === 0) {
            // Create a message based on what's causing the no matches
            let noResultsMessage = 'No logs match the selected filters.';

            if (globalSearchActive && globalSearchTerm) {
                noResultsMessage = `No logs match the search term "${globalSearchTerm}" with the current filters.`;
            }

            if (dateFilter.active) {
                let dateRangeText = '';
                if (dateFilter.startDate && dateFilter.endDate) {
                    dateRangeText = `between ${dateFilter.startDate.toLocaleString()} and ${dateFilter.endDate.toLocaleString()}`;
                } else if (dateFilter.startDate) {
                    dateRangeText = `after ${dateFilter.startDate.toLocaleString()}`;
                } else if (dateFilter.endDate) {
                    dateRangeText = `before ${dateFilter.endDate.toLocaleString()}`;
                }

                if (dateRangeText) {
                    noResultsMessage = `No logs found ${dateRangeText} with the current filters.`;
                }
            }

            // Update the entry counter
            document.getElementById('entryCounter').textContent = '0 matches';

            // Display the no results message
            document.getElementById('levelIndicator').innerHTML = ''; // Clear level indicator
            logContentDisplay.innerHTML = `
                <div class="no-results-message">
                    <p>${noResultsMessage}</p>
                </div>
            `;

            // Make sure the UI is visible
            currentLogEntry.classList.remove('hidden');
            processedContainer.classList.remove('hidden');

            // Disable navigation buttons
            prevLogBtn.disabled = true;
            nextLogBtn.disabled = true;

            // Update search placeholder if global search is active
            if (globalSearchActive) {
                document.getElementById('logSearchInput').placeholder = 'Search across all logs (use AND/OR operators, "error AND timeout")';
            } else {
                document.getElementById('logSearchInput').placeholder = 'Search in current log...';
            }

            return;
        }

        filteredIndex = Math.min(Math.max(0, filteredIndex), filteredLogs.length - 1);
        const currentLog = filteredLogs[filteredIndex];

        const entryNumber = filteredIndex + 1;
        const totalEntries = filteredLogs.length;

        // Create a span for the log level with appropriate color
        let levelSpan = '';
        switch (currentLog.level.toLowerCase()) {
            case 'info':
                levelSpan = `<span class="badge bg-success">INFO</span>`;
                break;
            case 'debug':
                levelSpan = `<span class="badge bg-primary">DEBUG</span>`;
                break;
            case 'trace':
                levelSpan = `<span class="badge bg-warning">TRACE</span>`;
                break;
            case 'error':
                levelSpan = `<span class="badge bg-danger">ERROR</span>`;
                break;
            default:
                levelSpan = `<span class="badge bg-secondary">${currentLog.level}</span>`;
        }

        // Extract timestamp from the log entry safely
        let timestamp = '';
        try {
            if (currentLog.timestamp) {
                timestamp = currentLog.timestamp;
            } else if (typeof currentLog.content === 'string' && currentLog.content.includes('@timestamp')) {
                const match = currentLog.content.match(/"@timestamp"\s*:\s*"([^"]+)"/);
                if (match && match[1]) {
                    timestamp = match[1];
                }
            }

            if (timestamp && timestamp !== 'N/A') {
                // Format the timestamp for better readability
                const date = new Date(timestamp);
                if (!isNaN(date)) {
                    timestamp = date.toLocaleString();
                }
            }
        } catch (e) {
            console.warn('Error extracting timestamp:', e);
        }

        // Parse the JSON content for formatting
        let jsonData = null;
        let enhancedContent = '';

        try {
            // If the content is a string, try to parse it as JSON
            if (typeof currentLog.content === 'string') {
                jsonData = JSON.parse(currentLog.content);
            } else if (typeof currentLog.content === 'object') {
                jsonData = currentLog.content;
            }

            if (jsonData) {
                // Format the JSON data for display with proper styling
                enhancedContent = formatLogEntryToJsonHtml(jsonData);

                // Apply global search highlighting if active
                if (globalSearchActive && globalSearchTerm && globalSearchTerm.trim() !== '') {
                    const escapeRegExp = (string) => string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

                    // Function to extract all search terms, including those in parentheses
                    function extractSearchTerms(searchStr) {
                        // Use the existing parseSearchTerms function to get the parsed search terms
                        const parsed = parseSearchTerms(searchStr);
                        const terms = [];

                        // Helper function to flatten the parsed terms into a simple array
                        function flattenTerms(node) {
                            if (typeof node === 'string') {
                                // For simple string terms, add directly
                                terms.push({
                                    term: node,
                                    isQuoted: false
                                });
                            } else if (node && node.value) {
                                // For terms with a value property
                                terms.push({
                                    term: node.value,
                                    isQuoted: node.quoted || node.exact
                                });
                            } else if (node && node.terms) {
                                // For compound expressions, recurse through terms
                                node.terms.forEach(term => flattenTerms(term));
                            }
                        }

                        // Start the flattening process
                        flattenTerms(parsed);

                        // Clean up the terms - remove quotes from quoted terms for highlighting
                        return terms.map(item => {
                            if (item.isQuoted && typeof item.term === 'string' &&
                                item.term.startsWith('"') && item.term.endsWith('"')) {
                                return {
                                    term: item.term.slice(1, -1),
                                    isQuoted: true
                                };
                            }
                            return item;
                        });
                    }

                    // First, parse the search expression to get all individual terms
                    const searchParts = extractSearchTerms(globalSearchTerm);

                    // Highlight each search part
                    searchParts.forEach(({ term, isQuoted }) => {
                        if (!term) return;

                        const escapedTerm = escapeRegExp(term);

                        // Split the content into text and HTML tags
                        const splitContent = enhancedContent.split(/(<[^>]*>)/g);

                        // Process only the text parts (even indices)
                        enhancedContent = splitContent.map((part, index) => {
                            if (index % 2 === 0) { // Text content, not HTML tag
                                // Create the regex based on whether it's a quoted/exact match
                                const regex = isQuoted
                                    ? new RegExp(escapedTerm, 'g')
                                    : new RegExp(escapedTerm, 'gi');

                                // Replace all occurrences with highlighted span
                                return part.replace(regex, match =>
                                    `<span class="global-search-highlight">${match}</span>`);
                            }
                            return part; // Return HTML tags unchanged
                        }).join('');
                    });
                }
            } else {
                // If we couldn't parse it as JSON, just use the raw content as-is
                enhancedContent = escapeHtml(String(currentLog.content));
            }
        } catch (e) {
            console.error('Error enhancing content:', e);
            // If there's an error in parsing/formatting, use the raw content as fallback
            enhancedContent = escapeHtml(String(currentLog.content));
        }

        // Update DOM elements instead of replacing entire HTML structure
        document.getElementById('levelIndicator').innerHTML = levelSpan;
        document.getElementById('entryCounter').textContent = `${entryNumber} of ${totalEntries}`;
        document.getElementById('logContentDisplay').innerHTML = enhancedContent;

        // Update the dropdown button text based on JobID selection
        const jobIdDropdownButton = document.querySelector('.jobid-dropdown-button span');
        if (jobIdDropdownButton) {
            if (allJobIds) {
                jobIdDropdownButton.textContent = "All JobId";
            } else if (selectedJobIds.length === 1) {
                jobIdDropdownButton.textContent = selectedJobIds[0];
            } else if (selectedJobIds.length > 1) {
                jobIdDropdownButton.textContent = `${selectedJobIds.length} JobId selected`;
            } else {
                jobIdDropdownButton.textContent = "Filter by JobId";
            }
        }

        // Update search placeholder if global search is active
        if (globalSearchActive) {
            document.getElementById('logSearchInput').placeholder = 'Search across all logs (use AND/OR operators, "error AND timeout")';

            // Update search matches count
            if (globalSearchTerm) {
                document.getElementById('searchMatchCount').textContent =
                    globalSearchMatches > 0 ? `${filteredIndex + 1} of ${globalSearchMatches}` : '0 matches';
            }
        } else {
            document.getElementById('logSearchInput').placeholder = 'Search in current log...';
        }

        // Preserve search input state when updating display
        const searchInput = document.getElementById('logSearchInput');
        if (searchInput) {
            const currentValue = searchInput.value;
            const isInputFocused = document.activeElement === searchInput;
            const selectionStart = searchInput.selectionStart;
            const selectionEnd = searchInput.selectionEnd;

            if (globalSearchActive) {
                // Only update if the value needs to change
                if (globalSearchTerm !== undefined && currentValue !== globalSearchTerm) {
                    searchInput.value = globalSearchTerm;
                }
            } else {
                // In local search mode, maintain the current value
                if (searchInput.hasAttribute('data-last-value')) {
                    const lastValue = searchInput.getAttribute('data-last-value');
                    if (currentValue !== lastValue) {
                        searchInput.value = lastValue;
                    }
                } else {
                    searchInput.setAttribute('data-last-value', currentValue);
                }
            }

            // Always restore focus and cursor position if input was focused
            if (isInputFocused) {
                requestAnimationFrame(() => {
                    searchInput.focus();
                    try {
                        searchInput.setSelectionRange(selectionStart, selectionEnd);
                    } catch (e) {
                        console.warn('Error restoring selection range:', e);
                    }
                });
            }
        }

        // Update global search toggle checkbox
        const globalSearchToggle = document.getElementById('globalSearchToggle');
        if (globalSearchToggle) {
            globalSearchToggle.checked = globalSearchActive;
        }

        // Now that the DOM elements exist, populate the JobID dropdown
        populateJobIdFilter(logEntries);

        // Add event listener to the copy button - make sure to remove existing ones first
        const copyButton = document.getElementById('copyLogButton');
        const newCopyButton = copyButton.cloneNode(true);
        copyButton.parentNode.replaceChild(newCopyButton, copyButton);

        newCopyButton.addEventListener('click', function() {
            copyToClipboard(currentLog.content);
            // Visual feedback
            const button = this;
            button.innerHTML = '<i class="bi bi-clipboard-check" aria-hidden="true"></i><span>Copied!</span>';
            button.classList.add('copy-success');
            setTimeout(() => {
                button.innerHTML = '<i class="bi bi-clipboard" aria-hidden="true"></i><span>Copy</span>';
                button.classList.remove('copy-success');
            }, 2000);
        });

        // Add event listeners to export options - remove existing ones first
        const exportCurrentLogLink = document.getElementById('exportCurrentLog');
        const newExportCurrentLogLink = exportCurrentLogLink.cloneNode(true);
        exportCurrentLogLink.parentNode.replaceChild(newExportCurrentLogLink, exportCurrentLogLink);

        const exportFilteredLogsLink = document.getElementById('exportFilteredLogs');
        const newExportFilteredLogsLink = exportFilteredLogsLink.cloneNode(true);
        exportFilteredLogsLink.parentNode.replaceChild(newExportFilteredLogsLink, exportFilteredLogsLink);

        const exportAllLogsLink = document.getElementById('exportAllLogs');
        const newExportAllLogsLink = exportAllLogsLink.cloneNode(true);
        exportAllLogsLink.parentNode.replaceChild(newExportAllLogsLink, exportAllLogsLink);

        newExportCurrentLogLink.addEventListener('click', function() {
            // Export only the current log
            exportLogs([currentLog], 'current');
        });

        newExportFilteredLogsLink.addEventListener('click', function() {
            // Export all filtered logs
            exportLogs(filteredLogs, 'filtered');
        });

        newExportAllLogsLink.addEventListener('click', function() {
            // Export all logs
            exportLogs(logEntries, 'all');
        });
        // Only set up search functionality if it hasn't been initialized
        if (!window.searchInitialized) {
            setupSearchFunctionality();
            window.searchInitialized = true;
        }


        // If in global search mode, scroll to the first highlighted element
        if (globalSearchActive && globalSearchTerm && globalSearchTerm.trim() !== '') {
            setTimeout(() => {
                const highlights = document.querySelectorAll('.global-search-highlight');
                if (highlights.length > 0) {
                    highlights[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }, 100);
        }

        // Make sure the log entry is visible
        currentLogEntry.classList.remove('hidden');
        processedContainer.classList.remove('hidden');

        // Update navigation button states
        prevLogBtn.disabled = filteredIndex === 0;
        nextLogBtn.disabled = filteredIndex >= filteredLogs.length - 1;
    }

    // Function to format and colorize JSON payloads
    function formatAndColorizeJsonPayload(jsonString) {
        // If input is not a string (e.g., already parsed), stringify it
        if (typeof jsonString !== 'string') {
            try {
                jsonString = JSON.stringify(jsonString);
            } catch (e) {
                console.error('Error stringifying JSON:', e);
                return `<div class="plain-content">${escapeHtml(String(jsonString))}</div>`;
            }
        }

        try {
            // Enhanced JSON parser that handles nested structures better
            function findJsonEnd(str) {
                let braceCount = 0;
                let inString = false;
                let escape = false;
                let inArray = false;

                for (let i = 0; i < str.length; i++) {
                    const char = str[i];

                    if (!inString) {
                        if (char === '{') braceCount++;
                        if (char === '}') {
                            braceCount--;
                            if (braceCount === 0 && !inArray) return i;
                        }
                        if (char === '[') inArray = true;
                        if (char === ']') inArray = false;
                        if (char === '"') inString = true;
                    } else {
                        if (char === '\\' && !escape) {
                            escape = true;
                            continue;
                        }
                        if (char === '"' && !escape) inString = false;
                        escape = false;
                    }
                }
                return -1;
            }

            // First try to split HTTP request line from JSON payload
            let httpRequest = '';
            let jsonPayload = '';
            let remainingContent = jsonString;
            let extractedRequest = '';
            let extractedJson = '';
            let extractedHeaders = '';

            // First, try to identify and extract the HTTP request line
            const requestPattern = /^(GET|POST|PUT|DELETE|HEAD|OPTIONS|PATCH)(\/[^\s\{]+)(?:HTTP\/\d\.\d)?/i;
            const requestMatch = remainingContent.match(requestPattern);
            if (requestMatch) {
                // Format request line with proper spacing and handle multiple slashes
                const method = requestMatch[1];
                // Clean up the path - normalize multiple slashes and ensure proper spacing
                let path = requestMatch[2].replace(/\/+/g, '/'); // normalize multiple slashes to single
                if (!path.startsWith('/')) path = '/' + path;
                extractedRequest = `${method} ${path} HTTP/1.1`;
                remainingContent = remainingContent.substring(requestMatch[0].length).trim();
            }

            // Next, find and extract the JSON payload
            const jsonStart = remainingContent.indexOf('{');
            if (jsonStart !== -1) {
                const jsonEnd = findJsonEnd(remainingContent.substring(jsonStart));
                if (jsonEnd > -1) {
                    extractedJson = remainingContent.substring(jsonStart, jsonStart + jsonEnd + 1);
                    extractedHeaders = remainingContent.substring(jsonStart + jsonEnd + 1).trim();
                    remainingContent = '';
                }
            }

            // If we didn't find JSON after request line, check if there are headers
            if (!extractedJson && remainingContent) {
                const headerPattern = /^(?:HTTP\/\d\.\d|[A-Za-z-]+:)/m;
                if (headerPattern.test(remainingContent)) {
                    extractedHeaders = remainingContent;
                    remainingContent = '';
                }
            }

            // Format the parts we found
            httpRequest = extractedRequest;
            jsonPayload = extractedJson;
            const headerContent = extractedHeaders;

            // Now try to find the complete JSON structure
            const jsonEndIndex = jsonPayload ? findJsonEnd(jsonPayload) : -1;
            let jsonPart = jsonPayload;

            // If we couldn't find complete JSON, try to extract the largest valid JSON fragment
            if (jsonEndIndex === -1) {
                let lastValidJson = '';
                let pos = 0;

                while (pos < jsonString.length) {
                    const nextOpen = jsonString.indexOf('{', pos);
                    if (nextOpen === -1) break;

                    const potentialEnd = findJsonEnd(jsonString.substring(nextOpen));
                    if (potentialEnd > 0) {
                        try {
                            const potentialJson = jsonString.substring(nextOpen, nextOpen + potentialEnd + 1);
                            JSON.parse(potentialJson);
                            lastValidJson = potentialJson;
                            pos = nextOpen + potentialEnd + 1;
                        } catch (e) {
                            pos++;
                        }
                    } else {
                        pos++;
                    }
                }

                if (lastValidJson) {
                    jsonPart = lastValidJson;
                    headersPart = jsonString.replace(lastValidJson, '');
                }
            }

            // Format HTTP headers and request line as HTML
            function formatHeaders(headerStr) {
                if (!headerStr) return '';

                // Format HTTP request line if present
                if (httpRequest) {
                    const formattedRequest = `<div class="http-request-line">${escapeHtml(httpRequest)}</div>`;
                    const formattedHeaders = headerStr.split(/\r?\n/)
                        .map(header => header.trim())
                        .filter(header => header)
                        .map(header => {
                            // Special formatting for headers with values
                            if (header.includes(':')) {
                                const [name, ...valueParts] = header.split(':');
                                const value = valueParts.join(':').trim();
                                return `<div class="http-header-line">
                                    <span class="http-header-name">${escapeHtml(name)}</span>:
                                    <span class="http-header-value">${escapeHtml(value)}</span>
                                </div>`;
                            }
                            return `<div class="http-header-line">${escapeHtml(header)}</div>`;
                        })
                        .join('');

                    return `<div class="http-headers">${formattedRequest}${formattedHeaders}</div>`;
                }

                // Fallback to original formatting if no request line
                return `<div class="http-headers">${headerStr.split(/(?=HTTP\/|[A-Za-z-]+:)/)
                    .map(header => header.trim())
                    .filter(header => header)
                    .map(header => `<div class="header-line">${escapeHtml(header)}</div>`)
                    .join('')}</div>`;
            }

            // Enhanced value formatter with better handling of nested structures
            function formatValue(value, indent = 0) {
                const indentStr = '  '.repeat(indent);

                if (value === null) {
                    return `<span class="log-null">null</span>`;
                }

                switch (typeof value) {
                    case 'string':
                        // Check for embedded JSON
                        if ((value.startsWith('{') && value.endsWith('}')) ||
                            (value.startsWith('[') && value.endsWith(']'))) {
                            try {
                                const parsed = JSON.parse(value);
                                return formatValue(parsed, indent);
                            } catch (e) {
                                // Not valid JSON, fall through
                            }
                        }

                        // Check for XML
                        if (value.trim().startsWith('<?xml') || value.trim().startsWith('<')) {
                            try {
                                return formatXml(value);
                            } catch (e) {
                                console.error('Error formatting XML:', e);
                                return `<span class="log-string">"${escapeHtml(value)}"</span>`;
                            }
                        }
                        return `<span class="log-string">"${escapeHtml(value)}"</span>`;

                        function formatXml(xml) {
                            let formatted = '';
                            let indent = 0;

                            xml.split(/(?=<)|(?<=>)/).forEach(node => {
                                node = node.trim();
                                if (!node) return;

                                if (node.startsWith('</') || node.endsWith('/>')) indent--;

                                formatted += `\n${'  '.repeat(Math.max(0, indent))}`;
                                if (node.startsWith('<')) {
                                    formatted += `<span class="log-xml-tag">${escapeHtml(node)}</span>`;
                                } else {
                                    formatted += `<span class="log-xml-content">${escapeHtml(node)}</span>`;
                                }

                                if (node.startsWith('<') && !node.startsWith('</') && !node.endsWith('/>')) indent++;
                            });

                            return `<div class="log-xml">${formatted}\n</div>`;
                        }
                    case 'number':
                        return `<span class="log-number">${value}</span>`;
                    case 'boolean':
                        return `<span class="log-boolean">${value}</span>`;
                    case 'object':
                        if (Array.isArray(value)) {
                            if (value.length === 0) return '[]';

                            const items = value.map(item =>
                                `${indentStr}  ${formatValue(item, indent + 1)}`
                            ).join(',\n');

                            return `[\n${items}\n${indentStr}]`;
                        } else {
                            const entries = Object.entries(value);
                            if (entries.length === 0) return '{}';

                            const properties = entries.map(([key, val]) =>
                                `${indentStr}  <span class="log-key">${escapeHtml(key)}</span>: ${formatValue(val, indent + 1)}`
                            ).join(',\n');

                            return `{\n${properties}\n${indentStr}}`;
                        }
                }
            }

            // Try to parse and format the JSON and headers
            let formattedContent = '';

            // Format JSON if we have any
            if (jsonPart) {
                try {
                    const parsedJson = JSON.parse(jsonPart);
                    formattedContent += `<div class="json-content">${formatValue(parsedJson)}</div>`;
                } catch (e) {
                    console.error('Error parsing JSON part:', e);
                    formattedContent += `<div class="plain-content">${escapeHtml(jsonPart)}</div>`;
                }
            }

            // Add HTTP request line if present
            if (httpRequest) {
                formattedContent = `<div class="http-request-line">${escapeHtml(httpRequest)}</div>` + formattedContent;
            }

            // Add headers if present
            if (headerContent) {
                const formattedHeaders = formatHeaders(headerContent);
                formattedContent += formattedHeaders;
            }

            return formattedContent || `<div class="plain-content">${escapeHtml(jsonString)}</div>`;

        } catch (e) {
            console.error('Error formatting JSON payload:', e);
            // Try to extract and format any JSON fragments
            let result = '';
            let pos = 0;

            while (pos < jsonString.length) {
                const nextOpen = jsonString.indexOf('{', pos);
                if (nextOpen === -1) {
                    result += escapeHtml(jsonString.substring(pos));
                    break;
                }

                result += escapeHtml(jsonString.substring(pos, nextOpen));
                const jsonEnd = findJsonEnd(jsonString.substring(nextOpen));

                if (jsonEnd > 0) {
                    try {
                        const jsonStr = jsonString.substring(nextOpen, nextOpen + jsonEnd + 1);
                        const json = JSON.parse(jsonStr);
                        result += formatValue(json);
                        pos = nextOpen + jsonEnd + 1;
                    } catch (e) {
                        result += escapeHtml(jsonString.substring(nextOpen, nextOpen + 1));
                        pos = nextOpen + 1;
                    }
                } else {
                    result += escapeHtml(jsonString.substring(nextOpen, nextOpen + 1));
                    pos = nextOpen + 1;
                }
            }

            return `<div class="json-fallback">${result}</div>`;
        }
    }

    // Function to format and colorize HTTP headers
    function formatAndColorizeHttpHeaders(headerString) {
        try {
            // Remove any leading/trailing whitespace
            const trimmedString = headerString.trim();

            // First check if this is a condensed HTTP headers block (no newlines)
            if (!trimmedString.includes('\n') && trimmedString.match(/[a-zA-Z0-9]+[a-zA-Z0-9]+/)) {
                // Try to split condensed headers by camelCase or similar patterns
                const spacedString = trimmedString
                    .replace(/([a-z])([A-Z0-9])/g, '$1 $2')
                    .replace(/([A-Z0-9])([A-Z][a-z])/g, '$1 $2');

                // Now split into logical lines
                const lines = spacedString.split(/(?<=:)\s*/);
                return `<div class="formatted-payload">` +
                       lines.map(line =>
                           `<div class="http-header-line">` +
                           `<span class="http-header-name">${escapeHtml(line.split(':')[0])}</span>: ` +
                           `<span class="http-header-value">${escapeHtml(line.split(':').slice(1).join(':').trim())}</span>` +
                           `</div>`
                       ).join('') +
                       `</div>`;
            }

            // Normal processing for regular HTTP headers
            const lines = trimmedString.split(/\r?\n/);

            if (lines.length === 0) {
                return '';
            }

            // Process the first line differently (status or request line)
            const firstLine = lines[0];
            let formattedFirstLine = '';

            // Check if it's a request line (GET /path HTTP/1.1) or status line (HTTP/1.1 200 OK)
            if (firstLine.startsWith('HTTP/')) {
                // Status line
                const [version, code, ...reasonParts] = firstLine.split(' ');
                const reason = reasonParts.join(' ');
                formattedFirstLine = `<span class="http-version">${escapeHtml(version)}</span> ` +
                                   `<span class="http-status-code">${escapeHtml(code)}</span> ` +
                                   `<span class="http-reason-phrase">${escapeHtml(reason)}</span>`;
            } else {
                // Request line
                const [method, path, version] = firstLine.split(' ');
                formattedFirstLine = `<span class="http-method">${escapeHtml(method)}</span> ` +
                                   `<span class="http-uri">${escapeHtml(path)}</span> ` +
                                   `<span class="http-version">${escapeHtml(version || '')}</span>`;
            }

            // Process headers
            const formattedHeaders = lines.slice(1).map(line => {
                if (!line.trim()) return '';

                if (line.includes(':')) {
                    const [key, ...valueParts] = line.split(':');
                    const value = valueParts.join(':');
                    return `<div class="http-header-line">` +
                           `<span class="http-header-name">${escapeHtml(key.trim())}</span>: ` +
                           `<span class="http-header-value">${escapeHtml(value.trim())}</span>` +
                           `</div>`;
                }

                // Line without colon (might be continuation or malformed)
                return `<div class="http-header-line">` +
                       `<span class="http-header-value">${escapeHtml(line.trim())}</span>` +
                       `</div>`;
            });

            // Combine everything in a structured container
            return `<div class="plain-content">` +
                   `<div class="http-message">` +
                   `<div class="http-start-line">${formattedFirstLine}</div>` +
                   `<div class="http-headers">${formattedHeaders.join('')}</div>` +
                   `</div></div>`;

        } catch (e) {
            console.error('Error formatting HTTP headers:', e);
            return `<div class="plain-content">${escapeHtml(headerString)}</div>`;
        }
    }

    // XML formatting function
    function formatAndColorizeXml(xmlString) {
        try {
            // Clean up XML string by removing &#xD; characters
            xmlString = xmlString.replace(/&#xD;/g, '');

            // Use DOMParser for robust XML parsing
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(xmlString, "application/xml");

            // Check for parsing errors
            const parserError = xmlDoc.querySelector('parsererror');
            if (parserError) {
                return `<div class="formatted-payload">${escapeHtml(xmlString)}</div>`;
            }

            // Function to calculate indentation
            const getIndent = (depth) => '  '.repeat(depth);

            // Function to clean up namespace prefixes from tag names for display purposes only
            const cleanTagName = (tagName) => {
                // We still want to show the full tagName including namespace for clarity
                return tagName;
            };

            // Recursive function to process nodes
            function processNode(node, depth = 0) {
                let result = '';

                switch (node.nodeType) {
                    case Node.ELEMENT_NODE:
                        const tagName = node.tagName;
                        result += `${getIndent(depth)}<span class="xml-tag">&lt;${tagName}</span>`;

                        // Process attributes including namespace declarations for complete representation
                        Array.from(node.attributes).forEach(attr => {
                            let attrName = attr.name;
                            let attrValue = attr.value;

                            // Format namespace declarations in a different color but keep them
                            if (attr.name.startsWith('xmlns:') || attr.name === 'xmlns') {
                                result += ` <span class="xml-attr-name">${attrName}</span>=` +
                                         `<span class="xml-attr-value">"${escapeHtml(attrValue)}"</span>`;
                            } else {
                                result += ` <span class="xml-attr-name">${attrName}</span>=` +
                                         `<span class="xml-attr-value">"${escapeHtml(attrValue)}"</span>`;
                            }
                        });

                        if (node.childNodes.length === 0) {
                            result += '<span class="xml-tag">/&gt;</span>';
                        } else {
                            result += '<span class="xml-tag">&gt;</span>';

                            // Check if child nodes include only a single text node
                            if (node.childNodes.length === 1 && node.childNodes[0].nodeType === Node.TEXT_NODE) {
                                const text = node.childNodes[0].nodeValue.trim();
                                if (text) {
                                    // Special handling for Header elements containing HTTP headers
                                    if (tagName === 'Header' && text.includes('HTTP/')) {
                                        // Simple colorized headers without extra formatting
                                        const coloredText = text.replace(/([A-Za-z0-9-]+):\s*(.*)/g,
                                            '<span class="http-header-name">$1</span>: <span class="http-header-value">$2</span>');
                                        result += ` <span class="xml-text">${coloredText}</span> `;
                                    } else {
                                        // Regular text content
                                        result += ` <span class="xml-text">${escapeHtml(text)}</span> `;
                                    }
                                }
                                result += `<span class="xml-tag">&lt;/${tagName}&gt;</span>`;
                            } else {
                                // For complex children, add proper structure with indentation
                                result += '\n';

                                // Process child nodes
                                node.childNodes.forEach(child => {
                                    result += processNode(child, depth + 1);
                                });

                                result += `${getIndent(depth)}<span class="xml-tag">&lt;/${tagName}&gt;</span>`;
                            }
                        }

                        // Add a newline after this element unless it's inside a text node
                        if (node.parentNode && node.parentNode.nodeType !== Node.TEXT_NODE) {
                            result += '\n';
                        }
                        break;

                    case Node.TEXT_NODE:
                        const text = node.nodeValue.trim();
                        if (text) {
                            // Check if text content appears to be escaped XML
                            if (text.includes('<?xml') || (text.includes('<') && text.includes('/>'))) {
                                try {
                                    // Attempt to format as XML if it looks like XML content
                                    return formatAndColorizeXml(text);
                                } catch (e) {
                                    // Add spaces before and after text content
                                    result += `${getIndent(depth)} <span class="xml-text">${escapeHtml(text)}</span> `;
                                }
                            } else {
                                // Add spaces before and after text content
                                result += `${getIndent(depth)} <span class="xml-text">${escapeHtml(text)}</span> `;
                            }

                            // Only add newline if text is followed by other content
                            if (node.nextSibling) {
                                result += '\n';
                            }
                        }
                        break;

                    case Node.COMMENT_NODE:
                        result += `${getIndent(depth)}<span class="xml-comment">&lt;!--${escapeHtml(node.nodeValue)}--&gt;</span>\n`;
                        break;

                    case Node.CDATA_SECTION_NODE:
                        result += `${getIndent(depth)}<span class="xml-cdata">&lt;![CDATA[${escapeHtml(node.nodeValue)}]]&gt;</span>\n`;
                        break;

                    case Node.DOCUMENT_NODE:
                        // Handle XML declaration
                        result += `<span class="xml-tag">&lt;?xml version="1.0" encoding="UTF-8"?&gt;</span>\n`;

                        // Process child nodes (should be the root element)
                        node.childNodes.forEach(child => {
                            result += processNode(child, depth);
                        });
                        break;
                }

                return result;
            }

            // Start processing from the root node
            let result;
            try {
                // Add XML declaration if it appears to be in the original string
                let hasXmlDeclaration = xmlString.trim().startsWith('<?xml');

                // Try parsing and processing as valid XML
                if (xmlDoc.documentElement) {
                    // Process the document with the root element
                    if (hasXmlDeclaration) {
                        result = `<span class="xml-tag">&lt;?xml version="1.0" encoding="UTF-8"?&gt;</span>\n`;
                        result += processNode(xmlDoc.documentElement);
                    } else {
                        result = processNode(xmlDoc.documentElement);
                    }

                    return `<div class="formatted-payload">${result}</div>`;
                } else {
                    // Fall back to plain text if no document element is found
                    throw new Error('No document element');
                }
            } catch (e) {
                // If XML processing failed, return the escaped original string
                console.warn('Error formatting XML content:', e);
                return `<div class="formatted-payload">${escapeHtml(xmlString)}</div>`;
            }
        } catch (e) {
            // If anything goes wrong, return the escaped original string
            console.warn('Error in XML formatting:', e);
            return `<div class="formatted-payload">${escapeHtml(xmlString)}</div>`;
        }
    }

    // Function to format and colorize plain text content
    function formatAndColorizePlainText(text) {
        if (!text || typeof text !== 'string') {
            return '';
        }

        // Split text into lines while preserving empty lines
        const lines = text.split(/\r?\n/);

        // Process each line
        const formattedLines = lines.map(line => {
            const trimmed = line.trim();
            if (!trimmed) {
                return '<br>'; // Preserve empty lines
            }

            // Check if line looks like a timestamp
            const timestampPattern = /^\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:[+-]\d{2}:?\d{2}|Z)?/;
            if (timestampPattern.test(trimmed)) {
                return `<span class="plain-timestamp">${escapeHtml(trimmed)}</span>`;
            }

            // Check if line contains HTTP headers (more specific than generic key-value)
            const httpHeaderPattern = /^([A-Z][a-zA-Z0-9-]+):\s*(.*)$/;
            const httpHeaderMatch = trimmed.match(httpHeaderPattern);
            if (httpHeaderMatch && httpHeaderMatch[1] && httpHeaderMatch[2] !== undefined) {
                const headerName = escapeHtml(httpHeaderMatch[1].trim());
                const headerValue = escapeHtml(httpHeaderMatch[2].trim());
                if (headerName && headerValue !== undefined) {
                    return `<span class="http-header-name">${headerName}</span>: ` +
                           `<span class="http-header-value">${headerValue}</span>`;
                }
            }

            // Fall back to generic key-value pairs (e.g., "key: value")
            const keyValuePattern = /^([^:]+):\s*(.+)$/;
            const keyValueMatch = trimmed.match(keyValuePattern);
            if (keyValueMatch) {
                return `<span class="plain-key">${escapeHtml(keyValueMatch[1])}</span>: ` +
                       `<span class="plain-value">${escapeHtml(keyValueMatch[2])}</span>`;
            }

            // Regular text line
            return `<span class="plain-text">${escapeHtml(trimmed)}</span>`;
        });

        // Join lines and wrap in a container
        return `<div class="plain-content">${formattedLines.join('\n')}</div>`;
    }


    // Helper function to escape HTML
    function escapeHtml(text) {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }

    // Function to copy content to clipboard
    function copyToClipboard(text) {
        try {
            // Parse the JSON to get the original data without HTML formatting
            const jsonData = typeof text === 'string' ? JSON.parse(text) : text;

            // If the JSON contains a message field with XML, decode HTML entities
            if (jsonData.message && typeof jsonData.message === 'string') {
                // Decode HTML entities in the message field
                const decodeHtmlEntities = (str) => {
                    const textarea = document.createElement('textarea');
                    textarea.innerHTML = str;
                    return textarea.value;
                };

                jsonData.message = decodeHtmlEntities(jsonData.message);
            }

            // Convert back to a clean, formatted JSON string with indentation
            const cleanText = JSON.stringify(jsonData, null, 2);

            // Create a temporary textarea to hold the clean text
            const textarea = document.createElement('textarea');
            textarea.value = cleanText;
            textarea.setAttribute('readonly', '');
            textarea.style.position = 'absolute';
            textarea.style.left = '-9999px'; // Move outside the screen
            document.body.appendChild(textarea);

            // Select the text and copy it
            textarea.select();
            document.execCommand('copy');

            // Remove the textarea
            document.body.removeChild(textarea);

            console.log('Clean content copied to clipboard');
        } catch (e) {
            // Fallback: if parsing fails, try to remove HTML tags and decode entities
            console.error('Error parsing JSON for clean copy:', e);

            // Function to decode HTML entities
            const decodeHtmlEntities = (str) => {
                const textarea = document.createElement('textarea');
                textarea.innerHTML = str;
                return textarea.value;
            };

            // First remove HTML tags, then decode entities
            let plainText = text.replace(/<[^>]+>/g, '');
            plainText = decodeHtmlEntities(plainText);

            const textarea = document.createElement('textarea');
            textarea.value = plainText;
            textarea.setAttribute('readonly', '');
            textarea.style.position = 'absolute';
            textarea.style.left = '-9999px';
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);

            console.log('Fallback: HTML-stripped content copied to clipboard');
        }
    }

    // Function to export logs based on the selected option
    function exportLogs(logs, type) {
        try {
            // Clean up logs for export - remove HTML formatting
            const cleanLogs = logs.map(log => {
                try {
                    // If we have the original JSON, use that
                    if (log.originalJson) {
                        return log.originalJson;
                    }
                    // Otherwise, try to clean up the content
                    else if (typeof log.content === 'string') {
                        // Remove HTML tags and parse the JSON
                        const plainContent = log.content.replace(/<[^>]+>/g, '');
                        return JSON.parse(plainContent);
                    }
                    // Fallback to the raw content
                    return log.content;
                } catch (e) {
                    console.warn('Error cleaning log for export:', e);
                    return {
                        level: log.level,
                        content: typeof log.content === 'string' ? log.content.replace(/<[^>]+>/g, '') : log.content,
                        error: "Unable to parse log content"
                    };
                }
            });

            // Create filename with timestamp
            const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '');
            const filename = `okd-logs-export-${type}-${timestamp}.json`;

            // Create JSON blob
            const jsonContent = JSON.stringify(cleanLogs, null, 2);
            const blob = new Blob([jsonContent], { type: 'application/json' });

            // Create download link and trigger download
            const downloadLink = document.createElement('a');
            downloadLink.href = URL.createObjectURL(blob);
            downloadLink.download = filename;
            downloadLink.style.display = 'none';
            document.body.appendChild(downloadLink);
            downloadLink.click();
            document.body.removeChild(downloadLink);

            // Show success message
            showToast(`Successfully exported ${cleanLogs.length} log entries to ${filename}`, 'success');
            console.log(`Exported ${cleanLogs.length} logs to ${filename}`);

        } catch (error) {
            console.error('Error exporting logs:', error);
            showToast('Error exporting logs: ' + error.message);
        }
    }

    document.getElementById('prevLog').addEventListener('click', function() {
        navigateToPreviousLog();
    });

             document.getElementById('nextLog').addEventListener('click', function() {
        navigateToNextLog();
    });

    // Add keyboard navigation with arrow keys
    document.addEventListener('keydown', function(e) {
        // Only handle arrow keys if we're not in an input field
        if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
            return;
        }

        // Left arrow for previous log
        if (e.key === 'ArrowLeft') {
            navigateToPreviousLog();
            e.preventDefault();
        }

        // Right arrow for next log
        if (e.key === 'ArrowRight') {
            navigateToNextLog();
            e.preventDefault();
        }
    });

    // Extract navigation logic to reusable functions
    function navigateToPreviousLog() {
        // Search configuration object
        const searchConfig = {
            global: document.getElementById('globalSearchToggle')?.checked || false
        };

        // Save search state
        const searchInput = document.getElementById('logSearchInput');
        const wasSearchFocused = searchInput && document.activeElement === searchInput;
        const searchValue = searchInput ? searchInput.value : '';
        const selectionStart = searchInput ? searchInput.selectionStart : 0;
        const selectionEnd = searchInput ? searchInput.selectionEnd : 0;

        // Get selected log levels
        const selectedLevels = getSelectedLogLevels();

        // Filter logs based on all active filters
        const filteredLogs = logEntries.filter(log => {
            const logLevel = log.level ? log.level.toLowerCase() : '';
            const levelMatches = selectedLevels.includes(logLevel);

            let jobIdMatches = allJobIds || selectedJobIds.length === 0;
            if (!allJobIds && selectedJobIds.length > 0) {
                const logContent = typeof log.content === 'string' ? log.content : JSON.stringify(log.content);
                jobIdMatches = selectedJobIds.some(jobId => logContent.includes(jobId));
            }

            let globalSearchMatches = true;
            if (globalSearchActive && globalSearchTerm) {
                const searchableContent = typeof log.content === 'string'
                    ? log.content.replace(/<[^>]+>/g, '')
                    : JSON.stringify(log.content);

                // Use the same parsing and evaluation functions as in displayCurrentLogEntry
                // Parse search terms for AND/OR operators
                const terms = parseSearchTerms(globalSearchTerm);

                // Apply the search logic based on parsed terms
                globalSearchMatches = evaluateSearchTerms(searchableContent.toLowerCase(), terms);
            }

            return levelMatches && jobIdMatches && globalSearchMatches;
        });

        if (filteredIndex > 0) {
            filteredIndex--;
            displayCurrentLogEntry(false);

            // Restore search input focus if it was focused before
            if (wasSearchFocused) {
                setTimeout(() => {
                    const newSearchInput = document.getElementById('logSearchInput');
                    if (newSearchInput) {
                        newSearchInput.value = searchValue;
                        newSearchInput.focus();
                        try {
                            newSearchInput.setSelectionRange(selectionStart, selectionEnd);
                        } catch (e) {
                            console.warn('Error restoring selection range:', e);
                        }
                    }
                }, 0);
            }
        }
    }

    function navigateToNextLog() {
        // Save search input and its focus state if it exists
        const searchInput = document.getElementById('logSearchInput');
        const wasSearchFocused = searchInput && document.activeElement === searchInput;
        const searchValue = searchInput ? searchInput.value : '';
        const selectionStart = searchInput ? searchInput.selectionStart : 0;
        const selectionEnd = searchInput ? searchInput.selectionEnd : 0;

        const selectedLevels = getSelectedLogLevels();

        // Filter logs based on all active filters
        const filteredLogs = logEntries.filter(log => {
            const logLevel = log.level ? log.level.toLowerCase() : '';
            const levelMatches = selectedLevels.includes(logLevel);

            let jobIdMatches = allJobIds || selectedJobIds.length === 0;
            if (!allJobIds && selectedJobIds.length > 0) {
                const logContent = typeof log.content === 'string' ? log.content : JSON.stringify(log.content);
                jobIdMatches = selectedJobIds.some(jobId => logContent.includes(jobId));
            }

            let globalSearchMatches = true;
            if (globalSearchActive && globalSearchTerm) {
                const searchableContent = typeof log.content === 'string'
                    ? log.content.replace(/<[^>]+>/g, '')
                    : JSON.stringify(log.content);

                // Use the same parsing and evaluation functions as in displayCurrentLogEntry
                // Parse search terms for AND/OR operators
                const terms = parseSearchTerms(globalSearchTerm);

                // Apply the search logic based on parsed terms
                globalSearchMatches = evaluateSearchTerms(searchableContent.toLowerCase(), terms);
            }

            return levelMatches && jobIdMatches && globalSearchMatches;
        });

        if (filteredIndex < filteredLogs.length - 1) {
            filteredIndex++;
            displayCurrentLogEntry(false);

            // Restore search input focus if it was focused before
            if (wasSearchFocused) {
                setTimeout(() => {
                    const newSearchInput = document.getElementById('logSearchInput');
                    if (newSearchInput) {
                        newSearchInput.value = searchValue;
                        newSearchInput.focus();
                        try {
                            newSearchInput.setSelectionRange(selectionStart, selectionEnd);
                        } catch (e) {
                            console.warn('Error restoring selection range:', e);
                        }
                    }
                }, 0);
            }
        } else {
            // If we can't navigate (at the beginning with Shift+Enter or at the end with Enter)
            // Just refresh the current entry to ensure highlighting is applied
            displayCurrentLogEntry(false);

            // Restore focus to search input
            setTimeout(() => {
                newSearchInput.focus();
                try {
                    newSearchInput.setSelectionRange(selectionStart, selectionEnd);
                } catch (e) {
                    console.warn('Error restoring selection range:', e);
                }
            }, 0);
        }
    }
    /**
     * Format and colorize a log entry for display
     * @param {Object} jsonData - The log entry to format
     * @returns {string} HTML formatted string with syntax highlighting
     */
    // Function to parse message content and determine its type and structure
    function parseMessageContent(message) {
        // Initialize result with default values
        let result = {
            prefix: '',
            payload: message,
            type: 'plain'
        };

        // Check if the message contains a Row/Column suffix and remove it
        const rowColSuffix = /Row: \d+, Column: \d+:$/;
        if (rowColSuffix.test(message)) {
            message = message.replace(rowColSuffix, '').trim();
            result.payload = message;
        }

        // Special case for TIBCO error messages with CausedBy - keep as single formatted message
        if (message.includes('TIBCO-BW-CORE-') && message.includes('<CausedBy>')) {
            // Classify as special type 'tibco-error' without splitting into prefix/payload
            result.type = 'tibco-error';
            result.prefix = '';
            result.payload = message;
            return result;
        }

        // Check for Response sent/received patterns
        if (message.includes('Response sent:') || message.includes('Response received:')) {
            const responseType = message.includes('Response sent:') ? 'Response sent:' : 'Response received:';
            const [prefix, ...rest] = message.split(responseType);
            result.prefix = (prefix + responseType).trim();
            result.payload = rest.join(responseType).trim();  // Handle if response type appears in payload

            // Determine payload type
            if (result.payload.startsWith('<?xml') ||
                result.payload.includes('<ns2:') ||
                result.payload.includes('<tns:') ||
                result.payload.includes(':Envelope') ||
                result.payload.startsWith('<')) {
                result.type = 'xml';
            } else if (result.payload.startsWith('{')) {
                result.type = 'json';
            } else if (/^HTTP\/[12]\.\d\s+\d{3}\s+[A-Za-z\s]+/.test(result.payload)) {
                result.type = 'http';
            }
        }
        // Special handling for "Request to Apigee" messages
        else if (message.includes('Request to Apigee:')) {
            // Split into prefix and rest content
            const [prefix, ...rest] = message.split('Request to Apigee:');
            result.prefix = (prefix + 'Request to Apigee:').trim();

            // Get full content after prefix
            const fullContent = rest.join('Request to Apigee:').trim();

            // Check if we have a payload marker
            if (fullContent.includes('payload:')) {
                const payloadIndex = fullContent.indexOf('payload:');
                const beforePayload = fullContent.substring(0, payloadIndex).trim();
                const payload = fullContent.substring(payloadIndex + 'payload:'.length).trim();

                // Format RequestURI and payload separately
                result.payload = beforePayload + '\npayload: ' + payload;
                result.type = payload.startsWith('{') ? 'json' : 'plain';
            } else {
                // No payload marker, treat as plain text
                result.payload = fullContent;
                result.type = 'plain';
            }
        }
        // Check for other Request patterns
        else if (message.includes('Request to') || message.includes('Request from') || message.includes('Request received:')) {
            const requestType = message.includes('Request received:') ? 'Request received:' :
                              (message.includes('Request to') ? 'Request to' : 'Request from');

            // Find the payload marker
            const payloadIndex = message.includes('payload:') ? message.indexOf('payload:') : -1;

            if (payloadIndex !== -1) {
                result.prefix = message.substring(0, payloadIndex + 'payload:'.length).trim();
                result.payload = message.substring(payloadIndex + 'payload:'.length).trim();
            } else {
                // Handle other request types without payload marker
                const [prefix, ...rest] = message.split(requestType);
                result.prefix = (prefix + requestType).trim();
                result.payload = rest.join(requestType).trim();
            }

            result.type = result.payload.startsWith('{') ? 'json' :
                         (result.payload.startsWith('<') ||
                          result.payload.includes('<ns2:') ||
                          result.payload.includes('<tns:')) ? 'xml' : 'plain';
        }
        // Special handling for HTTP messages with JSON payload
        else if (/^(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS)\s+\S+\s+HTTP\/\d\.\d/i.test(message)) {
            // Split into request line and JSON payload
            const jsonStart = message.indexOf('{');
            if (jsonStart > -1) {
                result.prefix = message.substring(0, jsonStart).trim();
                result.payload = message.substring(jsonStart).trim();

                // Verify if payload is valid JSON
                try {
                    JSON.parse(result.payload);
                    result.type = 'json';
                } catch (e) {
                    result.type = 'plain';
                }
            } else {
                result.type = 'http';
            }
        }
        // Check for HTTP response messages
        else if (/^HTTP\/[12]\.\d\s+\d{3}\s+[A-Za-z\s]+/.test(message)) {
            result.type = 'http';
        }
        // Check for XML content
        else if (message.startsWith('<?xml') ||
                 message.includes('<ns2:') ||
                 message.includes('<tns:') ||
                 message.includes('<tns2:') ||
                 message.includes('<ns3:') ||
                 message.includes('<Resultsets>') ||
                 /^\s*<[a-z][a-z0-9]*(?:\s+[a-z][a-z0-9]*:[a-z][a-z0-9]*\s*=\s*"[^"]*")*\s*>/i.test(message) ||
                 message.includes('<soap:Envelope') ||
                 message.includes(':Envelope') ||
                 message.includes('<soapenv:Envelope')) {
            result.type = 'xml';
        }
        // Check for JSON content
        else if (message.startsWith('{') && message.endsWith('}')) {
            result.type = 'json';
        }
        // If we have payload marker without Request prefix
        else if (message.includes('payload:')) {
            const payloadIndex = message.indexOf('payload:');

            result.prefix = message.substring(0, payloadIndex + 'payload:'.length).trim();
            result.payload = message.substring(payloadIndex + 'payload:'.length).trim();
            result.type = result.payload.startsWith('{') ? 'json' :
                         (result.payload.startsWith('<') ||
                          result.payload.includes('<ns2:') ||
                          result.payload.includes('<tns:')) ? 'xml' : 'plain';
        }
        // Special case for GETPAYMENTREQUESTDATA and similar patterns with XML
        else if (message.includes('invocation response:') && message.includes('<?xml')) {
            const xmlIndex = message.indexOf('<?xml');
            if (xmlIndex !== -1) {
                result.prefix = message.substring(0, xmlIndex).trim();
                result.payload = message.substring(xmlIndex).trim();
                result.type = 'xml';
            }
        }

        return result;
    }

    function formatLogEntryToJsonHtml(jsonData) {
        if (!jsonData || typeof jsonData !== 'object') {
            return '';
        }

        try {
            const orderedData = {};
            let messageContent = '';

            // Format timestamp if present
            const formatTimestamp = ts => {
                if (!ts || typeof ts !== 'string') return ts;
                const isoPattern = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:[+-]\d{2}:?\d{2}|Z)?$/;
                return isoPattern.test(ts)
                    ? ts.replace('T', ' ').replace(/([+-])(\d{2}):?(\d{2})$/, ' $1$2:$3')
                    : ts;
            };

            // Process timestamps
            if (jsonData['timestamp']) {
                orderedData['Timestamp'] = formatTimestamp(jsonData['timestamp']);
            } else if (jsonData['@timestamp']) {
                orderedData['Timestamp'] = formatTimestamp(jsonData['@timestamp']);
            }

            // Add non-timestamp, non-message fields
            Object.entries(jsonData).forEach(([key, value]) => {
                if (!['timestamp', '@timestamp', 'message'].includes(key)) {
                    orderedData[key] = value;
                }
            });

            // Process message field
            if ('message' in jsonData) {
                const message = jsonData.message;
                if (typeof message !== 'string' || !message.trim()) {
                    messageContent = '<em class="empty-message">Empty message</em>';
                } else {
                    const { prefix, payload, type } = parseMessageContent(message.trim());

                    try {
                        let formattedPayload = '';

                        // Special handling for TIBCO error messages - display as simple plain text
                        if (type === 'tibco-error') {
                            // Simply format as plain text, no special structure
                            formattedPayload = `<div class="formatted-payload">${escapeHtml(payload)}</div>`;
                            messageContent = formattedPayload;
                            return generateHtml(orderedData, messageContent);
                        }

                        switch (type) {
                            case 'xml':
                                formattedPayload = formatAndColorizeXml(payload);
                                break;
                            case 'json':
                                // For "Request to Apigee" messages, only format the part after "payload:"
                                if (prefix.includes('Request to Apigee:') && payload.includes('\npayload: ')) {
                                    const [requestUri, jsonPayload] = payload.split('\npayload: ');
                                    formattedPayload = formatAndColorizePlainText(requestUri) +
                                                     '\npayload: ' + formatAndColorizeJsonPayload(jsonPayload);
                                } else {
                                    formattedPayload = formatAndColorizeJsonPayload(payload);
                                }
                                break;
                            case 'http':
                                formattedPayload = formatAndColorizeHttpHeaders(payload);
                                break;
                            default:
                                formattedPayload = formatAndColorizePlainText(payload);
                        }

                        // Remove any outer formatted-payload div from the formatter's output
                        formattedPayload = formattedPayload.replace(/^<div class="(?:plain-content|formatted-payload)">/, '')
                                                          .replace(/<\/div>$/, '');

                        if (prefix) {
                            messageContent = `<div class="log-message-content">` +
                                           `<span class="message-prefix">${escapeHtml(prefix)}</span>` +
                                           `<div class="formatted-payload">${formattedPayload}</div>` +
                                           `</div>`;
                        } else {
                            messageContent = `<div class="formatted-payload">${formattedPayload}</div>`;
                        }
                    } catch (e) {
                        console.warn(`Error formatting ${type} message:`, e);
                        messageContent = `<div class="formatted-payload">${formatAndColorizePlainText(message)}</div>`;
                    }
                }
            }

            return generateHtml(orderedData, messageContent);

        } catch (e) {
            console.error('Error in formatLogEntryToJsonHtml:', e);
            // Provide basic formatting as fallback
            try {
                const fallback = JSON.stringify(jsonData, null, 2);
                // Basic regex highlighting for fallback
                return fallback
                    .replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;') // Basic escape
                    .replace(/"([^"]+)":/g, '<span class="log-key">$1</span>:')
                    .replace(/: "([^"]*)"/g, ': <span class="log-string">"$1"</span>')
                    .replace(/: ([-+]?\d+(\.\d+)?([eE][-+]?\d+)?)/g, ': <span class="log-number">$1</span>')
                    .replace(/: (true|false)/g, ': <span class="log-boolean">$1</span>')
                    .replace(/: (null)/g, ': <span class="log-null">$1</span>');
            } catch (fallbackError) {
                return `<pre>Error formatting log entry: ${escapeHtml(e.toString())}\nFallback error: ${escapeHtml(fallbackError.toString())}</pre>`;
            }
        }

        // Helper function to generate the final HTML output
        function generateHtml(orderedData, messageContent) {
            // Build the HTML structure iteratively without the enclosing curly braces
            let html = '';

            // Add ordered fields (non-message)
            Object.entries(orderedData).forEach(([key, value], index, arr) => {
                html += `<span class="log-key">${escapeHtml(key)}</span>: `;

                if (typeof value === 'string') {
                    html += `<span class="log-string">"${escapeHtml(value)}"</span>`;
                } else if (typeof value === 'number') {
                    html += `<span class="log-number">${value}</span>`;
                } else if (typeof value === 'boolean') {
                    html += `<span class="log-boolean">${value}</span>`;
                } else if (value === null) {
                    html += `<span class="log-null">null</span>`;
                } else {
                    html += `<span class="log-string">"${escapeHtml(JSON.stringify(value))}"</span>`;
                }

                // Don't add any commas anywhere
                html += '<br>';
            });

            // Add formatted message content
            if (messageContent) {
                html += `<span class="log-key">message</span>: ${messageContent}`; // Append pre-formatted message
            }

            return html;
        }
    }

    // Function to populate the JobID filter with checkbox-based dropdown
    function populateJobIdFilter(logs) {
        const jobIds = new Set();

        console.log("Extracting JobIDs from", logs.length, "log entries...");

        // First pass: Look for any JobID in all logs
        logs.forEach(log => {
            try {
                let jsonData = null;

                // Check for originalJson first (direct access)
                if (log.originalJson) {
                    jsonData = log.originalJson;

                    // Try various formats of JobId
                    const jobId = jsonData.JobId || jsonData.JobID || jsonData.jobId || jsonData.jobID || jsonData.jobid;
                    if (jobId) {
                        jobIds.add(jobId);
                        console.log("Found JobID in originalJson:", jobId);
                    }
                }
                // Then try parsing content if it's a string
                else if (typeof log.content === 'string') {
                    // Try direct regex extraction first (more reliable with HTML content)
                    const jobIdMatch = log.content.match(/[jJ]ob[iI][dD]"?\s*:\s*"?([^",\s]+)"?/);
                    if (jobIdMatch && jobIdMatch[1]) {
                        jobIds.add(jobIdMatch[1]);
                        console.log("Found JobID via regex:", jobIdMatch[1]);
                    }

                    // Try JSON parsing as fallback
                    try {
                        const cleanContent = log.content.replace(/<[^>]*>/g, '');
                        jsonData = JSON.parse(cleanContent);
                        const jobId = jsonData.JobId || jsonData.JobID || jsonData.jobId || jsonData.jobID || jsonData.jobid;
                        if (jobId) {
                            jobIds.add(jobId);
                            console.log("Found JobID in parsed content:", jobId);
                        }
                    } catch (parseError) {
                        // Expected error when content has HTML formatting
                    }
                }
            } catch (e) {
                // Silently ignore extraction errors
            }
        });

        console.log("Found", jobIds.size, "unique JobIDs");

        // Setup the dropdown with found JobIDs
        setupJobIdDropdown(Array.from(jobIds).sort((a, b) => String(a).localeCompare(String(b))));
    }

    // Setup the JobID dropdown with checkboxes
    function setupJobIdDropdown(jobIds) {
        console.log("Setting up JobID dropdown with", jobIds.length, "items");

        // Get references to dropdown elements
        const dropdown = document.querySelector('.jobid-dropdown');
        const dropdownButton = document.querySelector('.jobid-dropdown-button');
        const jobIdList = document.getElementById('jobIdList');
        const selectAllCheckbox = document.getElementById('selectAllJobIds');
        const dropdownButtonText = dropdownButton.querySelector('span');

        if (!dropdown || !dropdownButton || !jobIdList || !selectAllCheckbox || !dropdownButtonText) {
            console.error("JobID dropdown elements not found");
            return;
        }

        // Clear existing options
        jobIdList.innerHTML = '';

        // Remove any existing event listeners to prevent duplicates
        const newDropdownButton = dropdownButton.cloneNode(true);
        dropdownButton.parentNode.replaceChild(newDropdownButton, dropdownButton);

        // Check if there are any JobIDs
        if (jobIds.length === 0) {
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'jobid-checkbox-item empty-message';
            emptyMessage.textContent = 'No JobId found in logs';
            jobIdList.appendChild(emptyMessage);
            newDropdownButton.querySelector('span').textContent = "No JobId found";
            return;
        }

        // Set initial button text based on selection count
        const updateDropdownText = () => {
            if (allJobIds) {
                newDropdownButton.querySelector('span').textContent = "All JobId";
            } else if (selectedJobIds.length === 1) {
                newDropdownButton.querySelector('span').textContent = selectedJobIds[0]; // Show the single selected JobID
            } else if (selectedJobIds.length > 1) {
                newDropdownButton.querySelector('span').textContent = `${selectedJobIds.length} JobId selected`; // Show the count
            } else {
                newDropdownButton.querySelector('span').textContent = "Filter by JobId";
            }
        };

        updateDropdownText();

        // Track if we're in multiple selection mode
        let multipleSelectionMode = false;

        // Add JobIDs to the list with checkboxes
        jobIds.forEach(jobId => {
            const jobIdItem = document.createElement('label');
            jobIdItem.className = 'jobid-checkbox-item';

            // Create the checkbox for multi-select
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.value = jobId;
            checkbox.id = `jobid-${jobId}`;
            checkbox.checked = selectedJobIds.includes(jobId);

            // Create a container for checkbox and label
            const checkboxContainer = document.createElement('div');
            checkboxContainer.style.display = 'flex';
            checkboxContainer.style.alignItems = 'center';
            checkboxContainer.style.width = '100%';

            // Create the label for the checkbox
            const labelText = document.createElement('span');
            labelText.textContent = jobId;

            // Add checkbox and label to the container
            checkboxContainer.appendChild(checkbox);
            checkboxContainer.appendChild(labelText);

            // Define a handler for checkbox changes
            const checkboxChangeHandler = function(e) {
                e.stopPropagation();

                // If clicking a checkbox, uncheck "All JobIDs"
                selectAllCheckbox.checked = false;
                allJobIds = false;

                // Update selected JobIDs list
                selectedJobIds = Array.from(jobIdList.querySelectorAll('input[type="checkbox"]:checked'))
                    .map(cb => cb.value);

                console.log("Selected JobIds:", selectedJobIds);

                // Update button text
                updateDropdownText();

                // Keep dropdown open for checkbox interactions
                // Only close if "All JobIDs" checkbox is checked
                if (e.target.id === 'selectAllJobIds' && e.target.checked) {
                    dropdown.classList.remove('open');
                }

                displayCurrentLogEntry(true);
            };

            // Double click handler to select only this JobID
            const doubleClickHandler = function(e) {
                console.log("Double click detected on JobID:", jobId);
                e.preventDefault();
                e.stopPropagation();

                // Uncheck "All JobIDs"
                selectAllCheckbox.checked = false;
                allJobIds = false;

                // Force uncheck all checkboxes except this one
                jobIdList.querySelectorAll('input[type="checkbox"]').forEach(cb => {
                    cb.checked = cb.value === jobId;
                });

                // Make sure this checkbox is checked
                checkbox.checked = true;

                // Update selected JobIDs list with only this JobID
                selectedJobIds = [jobId];

                // Force update button text immediately
                newDropdownButton.querySelector('span').textContent = jobId;

                // Explicitly close dropdown for double-click
                dropdown.classList.remove('open');

                // Update display
                displayCurrentLogEntry(true);
            };

            // Add change event listener to checkbox
            checkbox.addEventListener('change', checkboxChangeHandler);

            // Add double click event to the entire item
            jobIdItem.addEventListener('dblclick', doubleClickHandler);

            // Also add double click to the label text for better UX
            labelText.addEventListener('dblclick', doubleClickHandler);

            // Add elements to the item
            jobIdItem.appendChild(checkboxContainer);
            jobIdList.appendChild(jobIdItem);
        });

        // Toggle dropdown visibility on button click
        newDropdownButton.addEventListener('click', function(e) {
            e.stopPropagation();
            dropdown.classList.toggle('open');
        });

        // Create a new "All JobIDs" checkbox with event listeners
        const newSelectAllCheckbox = selectAllCheckbox.cloneNode(true);
        selectAllCheckbox.parentNode.replaceChild(newSelectAllCheckbox, selectAllCheckbox);

        // Set up "All JobIDs" checkbox initial state
        newSelectAllCheckbox.checked = allJobIds;

        newSelectAllCheckbox.addEventListener('change', function() {
            allJobIds = this.checked;

            if (this.checked) {
                // Clear individual selections when "All JobIDs" is checked
                selectedJobIds = [];
                jobIdList.querySelectorAll('input[type="checkbox"]').forEach(cb => {
                    cb.checked = false;
                });
                newDropdownButton.querySelector('span').textContent = "All JobID";

                // Close dropdown when selecting "All JobIDs"
                dropdown.classList.remove('open');
            } else {
                newDropdownButton.querySelector('span').textContent = "Filter by JobId";
            }

            displayCurrentLogEntry(true);
        });

        // Close dropdown when clicking outside
        document.removeEventListener('click', documentClickHandler);
        document.addEventListener('click', documentClickHandler);

        // Add keyboard shortcut support
        document.removeEventListener('keydown', keydownHandler);
        document.addEventListener('keydown', keydownHandler);
    }

    // Handler for closing dropdown when clicking outside
    function documentClickHandler(e) {
        const dropdown = document.querySelector('.jobid-dropdown');
        if (dropdown && dropdown.classList.contains('open') && !dropdown.contains(e.target)) {
            dropdown.classList.remove('open');
        }
    }

    // Handler for Escape key to close dropdown
    function keydownHandler(e) {
        const dropdown = document.querySelector('.jobid-dropdown');
        if (e.key === 'Escape' && dropdown && dropdown.classList.contains('open')) {
            dropdown.classList.remove('open');
        }
    }

    // Update the JobID filter label based on selections
    function updateJobIdFilterLabel(forceAllState) {
        const filterLabel = document.querySelector('.jobid-selections');
        const filterContainer = document.querySelector('.jobid-filter-container');
        const selectAllCheckbox = document.getElementById('selectAllJobIds');
        const dropdownButtonText = document.querySelector('.jobid-dropdown-button span');

        if (!filterLabel || !filterContainer || !dropdownButtonText) return;

        if (allJobIds) {
            dropdownButtonText.textContent = "All JobIDs";
            filterContainer.setAttribute('data-has-selection', 'false');
        } else if (selectedJobIds.length > 0) {
            // Show the actual selected JobIDs
            dropdownButtonText.textContent = selectedJobIds.join(', ');
            filterContainer.setAttribute('data-has-selection', 'true');
        } else {
            dropdownButtonText.textContent = "Filter by JobId";
            filterContainer.setAttribute('data-has-selection', 'false');
        }
    }

    // Set up search functionality for logs
    function setupSearchFunctionality() {
        // Get search elements
        // Get search elements
        const searchInput = document.getElementById('logSearchInput');
        const prevMatchBtn = document.getElementById('prevMatchBtn');
        const nextMatchBtn = document.getElementById('nextMatchBtn');
        const searchMatchCount = document.getElementById('searchMatchCount');
        const clearSearchBtn = document.getElementById('clearSearchBtn');
        const globalSearchToggle = document.getElementById('globalSearchToggle');
        const searchHelpText = document.getElementById('searchHelpText');
        const logContent = document.querySelector('#currentLogEntry pre');

        // Check if all required elements exist
        if (!searchInput || !prevMatchBtn || !nextMatchBtn || !searchMatchCount || !clearSearchBtn || !globalSearchToggle || !searchHelpText) {
            console.warn('Core search functionality elements not found, skipping search setup');
            return;
        }

        // Note: logContent might not exist if there are no matches, which is okay

        // Current search state
        let searchMatches = [];
        let currentMatchIndex = -1;
        let lastFilteredIndex = filteredIndex;
        const searchState = {
            searchTerm: '',
            currentValue: ''  // Track current input value
        };

        let searchTimeout = null;
        const SEARCH_DELAY = 300; // Reduced delay for faster feedback

        // Remove any existing event listeners to prevent duplicates
        const newSearchInput = searchInput.cloneNode(true);
        searchInput.parentNode.replaceChild(newSearchInput, searchInput);

        const newPrevMatchBtn = prevMatchBtn.cloneNode(true);
        prevMatchBtn.parentNode.replaceChild(newPrevMatchBtn, prevMatchBtn);

        const newNextMatchBtn = nextMatchBtn.cloneNode(true);
        nextMatchBtn.parentNode.replaceChild(newNextMatchBtn, nextMatchBtn);

        const newClearSearchBtn = clearSearchBtn.cloneNode(true);
        clearSearchBtn.parentNode.replaceChild(newClearSearchBtn, clearSearchBtn);

        const newGlobalSearchToggle = globalSearchToggle.cloneNode(true);
        globalSearchToggle.parentNode.replaceChild(newGlobalSearchToggle, globalSearchToggle);


        // Function to perform search with debouncing for global search
        function performSearch(immediate = false) {
            // Get and store the search term
            const searchTerm = newSearchInput.value;
            searchState.currentValue = searchTerm;  // Always keep track of current value

            // Check if global search is active
            if (globalSearchActive) {
                // Cancel any pending search
                if (searchTimeout) {
                    clearTimeout(searchTimeout);
                }

                // For global search, store the term but delay the actual search
                if (!immediate && searchTerm) {
                    // Update placeholder text to indicate global search mode
                    newSearchInput.placeholder = 'Search across all logs (use AND/OR operators, "error AND timeout")';

                    // Get the current selection and active element before search
                    const wasInputFocused = document.activeElement === newSearchInput;
                    const selectionStart = newSearchInput.selectionStart;
                    const selectionEnd = newSearchInput.selectionEnd;

                    // Set a timeout to perform the search
                    searchTimeout = setTimeout(() => {
                        // Store the search term globally and in search state
                        globalSearchTerm = searchTerm;
                        searchState.currentValue = searchTerm;

                        // Store current input state using actual searchTerm
                        const currentInput = {
                            value: searchTerm,  // Use the actual search term
                            focused: wasInputFocused,
                            selectionStart,
                            selectionEnd
                        };

                        // Update both global and state values
                        globalSearchTerm = searchTerm;
                        searchState.currentValue = searchTerm;

                        // Refresh display
                        displayCurrentLogEntry(false);

                        // Restore search input state after display update
                        setTimeout(() => {
                            const searchInput = document.getElementById('logSearchInput');
                            if (searchInput) {
                                searchInput.value = currentInput.value;
                                if (currentInput.focused) {
                                    searchInput.focus();
                                    searchInput.setSelectionRange(
                                        currentInput.selectionStart,
                                        currentInput.selectionEnd
                                    );
                                }
                            }
                        }, 0);

                        // Enable/disable navigation buttons based on results
                        if (searchTerm && globalSearchMatches > 0) {
                            newPrevMatchBtn.disabled = filteredIndex === 0;
                            newNextMatchBtn.disabled = filteredIndex >= globalSearchMatches - 1;
                        } else {
                            newPrevMatchBtn.disabled = true;
                            newNextMatchBtn.disabled = true;
                        }
                    }, immediate ? 0 : SEARCH_DELAY);
                } else if (!searchTerm) {
                    // Clear global search if term is empty
                    globalSearchTerm = '';
                    globalSearchMatches = 0;
                    displayCurrentLogEntry(true);

                    // Ensure focus is restored to the search input
                    setTimeout(() => {
                        newSearchInput.focus();
                    }, 0);
                }

                return;
            }

            // For in-entry search, process immediately (no debounce)
            // Store current state
            searchState.currentValue = searchTerm;
            lastFilteredIndex = filteredIndex;

            // Clear previous highlights
            clearSearchHighlights();

            // Restore the current log position
            filteredIndex = lastFilteredIndex;

            if (searchTerm === '') {
                // Reset search UI when search is empty
                searchMatches = [];
                currentMatchIndex = -1;
                updateSearchMatchCount();
                newPrevMatchBtn.disabled = true;
                newNextMatchBtn.disabled = true;
                return;
            }

            try {
                // Check if logContent exists before proceeding
                if (!logContent) {
                    console.warn('No log content element found to search in');
                    searchMatches = [];
                    currentMatchIndex = -1;
                    updateSearchMatchCount();
                    newPrevMatchBtn.disabled = true;
                    newNextMatchBtn.disabled = true;
                    return;
                }

                // Get text content
                const textContent = logContent.innerText;

                // Create a regex for the search term (case-insensitive)
                const flags = 'ig';
                const searchValue = escapeRegExp(searchTerm.trim());
                const regex = new RegExp(searchValue, flags);

                // Create an array of match indices
                searchMatches = [];
                let match;
                while ((match = regex.exec(textContent)) !== null) {
                    searchMatches.push({
                        index: match.index,
                        length: match[0].length
                    });
                }

                console.log(`Found ${searchMatches.length} matches for "${searchTerm}" in current log entry`);

                // Update UI
                currentMatchIndex = searchMatches.length > 0 ? 0 : -1;
                newPrevMatchBtn.disabled = searchMatches.length === 0 || currentMatchIndex <= 0;
                newNextMatchBtn.disabled = searchMatches.length === 0 || currentMatchIndex >= searchMatches.length - 1;

                // Highlight matches
                highlightMatches();
                updateSearchMatchCount();

                // Ensure search input value is preserved
                if (newSearchInput.value !== searchState.currentValue) {
                    newSearchInput.value = searchState.currentValue;
                }

                // Scroll to the first match if there is one
                if (searchMatches.length > 0) {
                    scrollToCurrentMatch();
                }

                // Keep the log at current position without triggering another display update
                filteredIndex = lastFilteredIndex;
            } catch (error) {
                console.error('Error performing search:', error);
            }
        }

        // Add event listener for global search toggle if it exists
        if (newGlobalSearchToggle) {
            newGlobalSearchToggle.addEventListener('change', function() {
                globalSearchActive = this.checked;

                // Update UI to reflect search mode
                newSearchInput.placeholder = globalSearchActive ? 'Search across all logs (use AND/OR operators, "error AND timeout")' : 'Search in current log...';

                if (globalSearchActive) {
                    // When switching to global search, use the current search term if any
                    const currentSearchTerm = newSearchInput.value.trim();
                    // Always set the global search term, even if empty
                    globalSearchTerm = currentSearchTerm || '';

                    // Clear local search highlights if there are any
                    clearSearchHighlights();

                    // Cancel any pending searches to prevent race conditions
                    if (searchTimeout) {
                        clearTimeout(searchTimeout);
                        searchTimeout = null;
                    }

                    // Save the current search state
                    searchState.currentValue = currentSearchTerm;

                    // Update display with global search term applied
                    const savedIndex = filteredIndex;
                    displayCurrentLogEntry(false);
                    filteredIndex = savedIndex;
                } else {
                    // When switching back to in-entry search
                    // Keep the search term, but clear the global search term
                    const currentSearchTerm = newSearchInput.value.trim();
                    globalSearchTerm = '';
                    globalSearchMatches = 0;

                    // Refresh the display without global search filtering
                    displayCurrentLogEntry(true);

                    // Focus back on search input
                    newSearchInput.focus();

                    // If there's a search term, re-run in-entry search
                    if (currentSearchTerm) {
                        // Preserve cursor position and focus state
                        const searchState = {
                            value: currentSearchTerm,
                            focused: document.activeElement === newSearchInput,
                            selectionStart: newSearchInput.selectionStart,
                            selectionEnd: newSearchInput.selectionEnd
                        };

                        // Perform the search immediately
                        performSearch(true);

                        // Restore search input state
                        setTimeout(() => {
                            newSearchInput.value = searchState.value;
                            if (searchState.focused) {
                                newSearchInput.focus();
                                newSearchInput.setSelectionRange(
                                    searchState.selectionStart,
                                    searchState.selectionEnd
                                );
                            }
                        }, 0);
                    }
                }
            });
        }

        // Function to escape special regex characters
        function escapeRegExp(string) {
            return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        }

        // Function to highlight matches
       function highlightMatches() {
           const content = logContent.innerHTML;
           const searchTerm = newSearchInput.value.trim();

           if (!searchTerm) return;

           // Store the original content before applying any highlighting
           if (!logContent.hasAttribute('data-original-content')) {
               logContent.setAttribute('data-original-content', content);
           }

           try {
               // First restore to the clean version without any highlights
               clearSearchHighlights();

               // Parse search terms using our enhanced parser
               const searchTerms = parseSearchTerms(searchTerm);
               if (!searchTerms) return;

               // Get the current clean content
               const cleanContent = logContent.innerHTML;

               // Function to highlight a single term with specific class
               function highlightTerm(content, term, className) {
                   try {
                       let pattern;
                       let value = term.value || term;
                       const flags = 'gi';

                       if (term.exact || term.quoted) {
                           // Case-sensitive exact match for quoted terms
                           return content.split(value).join(`<span class="${className}">${value}</span>`);
                       } else {
                           // Regular terms with case-insensitive matching
                           pattern = new RegExp(`(?<!<[^>]*)(${escapeRegExp(value)})`, flags);
                       }
                       return content.replace(pattern, (_, match) =>
                           `<span class="${className}">${match}</span>`);
                    } catch (e) {
                        // Fallback for browsers that don't support lookbehind
                        const parts = content.split(/(<[^>]*>)/);
                        return parts.map((part, i) => {
                            if (i % 2 === 0) { // Text content, not HTML tag
                                let pattern;
                                if (term.exact || term.quoted) {
                                    // Case-sensitive for exact/quoted matches
                                    pattern = new RegExp(escapeRegExp(term.value), 'g');
                                } else {
                                    // Case-insensitive for regular searches
                                    pattern = new RegExp(escapeRegExp(term.value), 'gi');
                                }
                                return part.replace(pattern, match =>
                                    `<span class="${className}">${match}</span>`);
                            }
                            return part;
                        }).join('');
                    }
                }

                // Process terms based on their operator type
                function processTerms(terms) {
                    if (!terms) return cleanContent;

                    if (terms.type === 'AND' || terms.type === 'OR') {
                        let result = cleanContent;
                        terms.terms.forEach(term => {
                            const className = terms.type === 'AND' ? 'search-highlight-and' : 'search-highlight-or';
                            result = highlightTerm(result, term, className);
                        });
                        return result;
                    } else {
                        // Single term
                        return highlightTerm(cleanContent, terms, 'search-highlight');
                    }
                }

                // Apply highlighting
                const highlightedContent = processTerms(searchTerms);
                logContent.innerHTML = highlightedContent;

                // Recalculate matches after highlighting
                const allHighlights = logContent.querySelectorAll('.search-highlight, .search-highlight-and, .search-highlight-or');
                searchMatches = Array.from(allHighlights).map((el, idx) => ({
                    index: idx,
                    element: el,
                    length: el.textContent.length
                }));

                // Add active highlight to the current match
                if (currentMatchIndex >= 0 && searchMatches.length > 0) {
                    // Ensure currentMatchIndex is within bounds
                    currentMatchIndex = Math.min(currentMatchIndex, searchMatches.length - 1);

                    if (searchMatches[currentMatchIndex] && searchMatches[currentMatchIndex].element) {
                        searchMatches[currentMatchIndex].element.classList.add('search-highlight-active');
                    }
                }

                // Update match count
                updateSearchMatchCount();
            } catch (err) {
                console.error('Error highlighting matches:', err);
            }
        }

        // Function to navigate to the next match
        function nextMatch() {
            if (globalSearchActive) {
                // For global search, navigate to next log entry
                if (filteredIndex < globalSearchMatches - 1) {
                    filteredIndex++;
                    displayCurrentLogEntry(false);
                }
                return;
            }

            // For in-entry search
            if (searchMatches.length === 0 || currentMatchIndex >= searchMatches.length - 1) return;

            currentMatchIndex++;
            highlightMatches();
            updateSearchMatchCount();
            scrollToCurrentMatch();

            // Update button states
            newPrevMatchBtn.disabled = currentMatchIndex <= 0;
            newNextMatchBtn.disabled = currentMatchIndex >= searchMatches.length - 1;
        }

        // Function to navigate to the previous match
        function prevMatch() {
            if (globalSearchActive) {
                // For global search, navigate to previous log entry
                if (filteredIndex > 0) {
                    filteredIndex--;
                    displayCurrentLogEntry(false);
                }
                return;
            }

            // For in-entry search
            if (searchMatches.length === 0 || currentMatchIndex <= 0) return;

            currentMatchIndex--;
            highlightMatches();
            updateSearchMatchCount();
            scrollToCurrentMatch();

            // Update button states
            newPrevMatchBtn.disabled = currentMatchIndex <= 0;
            newNextMatchBtn.disabled = currentMatchIndex >= searchMatches.length - 1;
        }

        // Function to scroll to the current match
        function scrollToCurrentMatch() {
            const highlights = logContent.querySelectorAll('.search-highlight');
            if (highlights.length > currentMatchIndex) {
                const activeHighlight = highlights[currentMatchIndex];
                activeHighlight.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                });
            }
        }

        // Function to update the match count display
        function updateSearchMatchCount() {
            if (globalSearchActive) {
                if (globalSearchMatches === 0) {
                    searchMatchCount.textContent = '0 matches';
                } else {
                    searchMatchCount.textContent = `${filteredIndex + 1} of ${globalSearchMatches}`;
                }
            } else {
                if (searchMatches.length === 0) {
                    searchMatchCount.textContent = '0 matches';
                } else {
                    searchMatchCount.textContent = `${currentMatchIndex + 1} of ${searchMatches.length}`;
                }
            }
        }

        // Function to clear search highlights
        function clearSearch() {
            // Cancel any pending search
            if (searchTimeout) {
                clearTimeout(searchTimeout);
                searchTimeout = null;
            }

            newSearchInput.value = '';

            // Reset all search state
            clearSearchHighlights();
            searchMatches = [];
            currentMatchIndex = -1;

            // If we stored the original content, remove the attribute to save memory
            const logContent = document.querySelector('#currentLogEntry pre');
            if (logContent && logContent.hasAttribute('data-original-content')) {
                logContent.removeAttribute('data-original-content');
            }

            // Clear global search term if global search is active
            if (globalSearchActive) {
                globalSearchTerm = '';
                globalSearchMatches = 0;
                // Refresh the display without the global search filter
                displayCurrentLogEntry(true);
            }

            updateSearchMatchCount();
            newPrevMatchBtn.disabled = true;
            newNextMatchBtn.disabled = true;
        }

        // Add event listeners
        newSearchInput.addEventListener('input', () => performSearch(false));

        // Handle keydown events
        newSearchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();

                if (globalSearchActive) {
                    // For global search, execute the search immediately on Enter
                    if (searchTimeout) {
                        clearTimeout(searchTimeout);
                        searchTimeout = null;
                    }

                    // Save the current selection position
                    const selectionStart = newSearchInput.selectionStart;
                    const selectionEnd = newSearchInput.selectionEnd;

                    // Update global search term and perform search
                    const searchTerm = newSearchInput.value.trim();
                    globalSearchTerm = searchTerm;

                    // Perform a function to navigate
                    const navigate = () => {
                        if (e.shiftKey && filteredIndex > 0) {
                            filteredIndex--;

                            // Store current input state
                            const currentInput = {
                                value: newSearchInput.value,
                                selectionStart,
                                selectionEnd
                            };

                            // Display new log entry
                            displayCurrentLogEntry(false);

                            // Use requestAnimationFrame to ensure DOM updates are complete
                            requestAnimationFrame(() => {
                                const searchInput = document.getElementById('logSearchInput');
                                if (searchInput) {
                                    // First restore the value
                                    searchInput.value = currentInput.value;

                                    // Then handle focus and selection in next tick
                                    setTimeout(() => {
                                        searchInput.focus();
                                        try {
                                            searchInput.setSelectionRange(
                                                currentInput.selectionStart,
                                                currentInput.selectionEnd
                                            );
                                        } catch (e) {
                                            console.warn('Error restoring selection range:', e);
                                        }
                                    }, 0);
                                }
                            });

                        } else if (!e.shiftKey && filteredIndex < globalSearchMatches - 1) {
                            filteredIndex++;

                            // Store search input value before navigation
                            const inputValue = newSearchInput.value;

                            // Display new log entry
                            displayCurrentLogEntry(false);

                            // Restore focus and selection to search input
                            setTimeout(() => {
                                newSearchInput.value = inputValue;
                                newSearchInput.focus();
                                try {
                                    newSearchInput.setSelectionRange(selectionStart, selectionEnd);
                                } catch (e) {
                                    console.warn('Error restoring selection range:', e);
                                }
                            }, 0);
                        } else {
                            // If we can't navigate (at the beginning with Shift+Enter or at the end with Enter)
                            // Just refresh the current entry to ensure highlighting is applied
                            displayCurrentLogEntry(false);

                            // Restore focus to search input
                            setTimeout(() => {
                                newSearchInput.focus();
                                try {
                                    newSearchInput.setSelectionRange(selectionStart, selectionEnd);
                                } catch (e) {
                                    console.warn('Error restoring selection range:', e);
                                }
                            }, 0);
                        }
                    };

                    // Perform search with immediate flag, then navigate
                    performSearch(true);

                    // Navigation needs a slight delay to process the search results
                    setTimeout(navigate, 50);
                    return;
                }

                // For in-entry search, navigate between matches
                if (e.shiftKey) {
                    prevMatch();
                } else {
                    nextMatch();
                }
            } else if (e.key === 'Escape') {
                e.preventDefault();
                clearSearch();
            }
        });

        newPrevMatchBtn.addEventListener('click', prevMatch);
        newNextMatchBtn.addEventListener('click', nextMatch);
        newClearSearchBtn.addEventListener('click', clearSearch);

        // Initialize button states
        if (globalSearchActive && globalSearchMatches > 0) {
            newPrevMatchBtn.disabled = filteredIndex === 0;
            newNextMatchBtn.disabled = filteredIndex >= globalSearchMatches - 1;
        } else {
            newPrevMatchBtn.disabled = true;
            newNextMatchBtn.disabled = true;
        }

        // Update match count on initial setup
        updateSearchMatchCount();

        // Set initial value if global search is active
        if (globalSearchActive && globalSearchTerm) {
            newSearchInput.value = globalSearchTerm;
        }
    }

    // Function to clear any search highlights from the content
    function clearSearchHighlights() {
        const logContent = document.querySelector('#currentLogEntry pre');
        if (!logContent) return;

        // If we stored the original content, restore it
        if (logContent.hasAttribute('data-original-content')) {
            logContent.innerHTML = logContent.getAttribute('data-original-content');
            return;
        }

        // Otherwise, just remove all highlight spans
        try {
            const content = logContent.innerHTML;
            // Remove both regular and active highlights
            const cleanContent = content
                .replace(/<span class="search-highlight search-highlight-active">(.*?)<\/span>/g, '$1')
                .replace(/<span class="search-highlight">(.*?)<\/span>/g, '$1')
                .replace(/<span class="global-search-highlight">(.*?)<\/span>/g, '$1');
            logContent.innerHTML = cleanContent;
        } catch (e) {
            console.error('Error clearing search highlights:', e);
        }
    }

    // Now add some CSS to style the no results message
    const style = document.createElement('style');
    style.textContent = `
    .no-results-message {
        padding: 20px;
        text-align: center;
        background-color: #f8f9fa;
        border-radius: 0 0 8px 8px;
        color: #6c757d;
        font-size: 16px;
    }

    .dark-theme .no-results-message {
        background-color: #2d3748;
        color: #a0aec0;
    }
    `;
    document.head.appendChild(style);

    // Add CSS for empty message
    const emptyMessageStyle = document.createElement('style');
    emptyMessageStyle.textContent = `
        .empty-message {
            font-style: italic;
            color: #666;
            padding: 5px 10px;
        }
        .dark-theme .empty-message {
            color: #999;
        }
    `;
    document.head.appendChild(emptyMessageStyle);

    window.processData = processData;
    window.clearText = clearText;
})();
