/**
 * UI Manager for Apigee Log Processor
 * Handles UI updates and interactions
 */
import { stateManager } from './state.js';
import { logProcessor } from './processor.js';

export class UIManager {
  constructor() {
    // Toast instance
    this.toast = null;

    // Offcanvas instance
    this.offcanvasInstance = null;
  }

  /**
   * Initialize the UI manager
   */
  init() {
    // Initialize offcanvas
    const offcanvasElement = document.getElementById('entryDetailsOffcanvas');
    if (offcanvasElement) {
      this.offcanvasInstance = new bootstrap.Offcanvas(offcanvasElement);
      this.setupOffcanvasEvents(offcanvasElement);
    }

    // Initialize back to top button
    this.initBackToTopButton();

    // Initialize dropdowns
    this.initDropdowns();

    // Set initial view type button text
    const currentViewType = stateManager.getState().currentViewType;
    this.updateViewTypeButtonText(currentViewType);

    // Initialize filter button indicators
    this.updateFilterButtonIndicators();

    // Subscribe to state changes
    stateManager.subscribe(this.handleStateChange.bind(this));

    // Listen for custom events from processor
    document.addEventListener('entriesProcessed', (event) => {
      const viewType = event.detail.viewType;
      if (viewType === 'flows') {
        this.updateDisplayedEntries();
      } else if (viewType === 'calls') {
        this.showCallSequenceView();
      }

      // No scrolling after entries are processed
    });

    // Listen for filter state reset event
    document.addEventListener('filterStateReset', () => {
      // Reset UI elements for filters
      this.resetFilterUI();
    });
  }

  /**
   * Handle state changes
   * @param {Object} state - The current state
   */
  handleStateChange(state) {
    // Update UI based on state changes
    document.body.setAttribute('data-view-type', state.currentViewType);

    // Update view type button text
    this.updateViewTypeButtonText(state.currentViewType);

    // Update column controls when view type changes
    this.initializeColumnControls();

    // Update column visibility
    this.applyColumnVisibility();

    // Update filter button indicators
    this.updateFilterButtonIndicators();
  }

  /**
   * Update filter button indicators based on current filter state
   */
  updateFilterButtonIndicators() {
    const state = stateManager.getState();
    const currentViewType = state.currentViewType;
    const filterState = state.filterState[currentViewType];
    const columnVisibilityState = state.columnVisibilityState[currentViewType];

    // Message ID filter button
    const messageIdButton = document.getElementById('messageIdButton');
    if (messageIdButton) {
      if (!filterState.allMessageIds && filterState.selectedMessageIds.length > 0) {
        messageIdButton.classList.add('filter-active');
      } else {
        messageIdButton.classList.remove('filter-active');
      }
    }

    // Status code filter button
    const statusCodeButton = document.getElementById('statusCodeButton');
    if (statusCodeButton) {
      if (!filterState.allStatusCodes && filterState.selectedStatusCodes.length > 0) {
        statusCodeButton.classList.add('filter-active');
      } else {
        statusCodeButton.classList.remove('filter-active');
      }
    }

    // Flow filter button
    const flowButton = document.getElementById('flowButton');
    if (flowButton) {
      if (!filterState.allFlows && filterState.selectedFlows.length > 0) {
        flowButton.classList.add('filter-active');
      } else {
        flowButton.classList.remove('filter-active');
      }
    }

    // Column filter button
    const columnButton = document.getElementById('columnButton');
    if (columnButton) {
      // Check if "All columns" is unchecked and there are selected columns
      if (!columnVisibilityState.allColumns && columnVisibilityState.selectedColumns.length > 0) {
        columnButton.classList.add('filter-active');
      } else {
        columnButton.classList.remove('filter-active');
      }
    }
  }

  /**
   * Update view type button text
   * @param {string} viewType - The current view type
   */
  updateViewTypeButtonText(viewType) {
    const viewTypeButton = document.getElementById('viewTypeButton');
    if (viewTypeButton) {
      const span = viewTypeButton.querySelector('span');
      if (span) {
        span.textContent = viewType === 'flows' ? 'API Flows' : 'API Calls';
      }
    }

    // Update export button text to match the current view
    const exportTypeSpan = document.querySelector('#exportXLSX .export-type');
    if (exportTypeSpan) {
      exportTypeSpan.textContent = viewType;
    }
  }

  /**
   * Initialize back to top button
   */
  initBackToTopButton() {
    const backToTopButton = document.getElementById('backToTopButton');
    if (!backToTopButton) return;

    // Show or hide the button based on scroll position
    window.addEventListener('scroll', () => {
      if (window.scrollY > 300) {
        backToTopButton.classList.remove('hidden');
        backToTopButton.classList.add('visible');
      } else {
        backToTopButton.classList.remove('visible');
        backToTopButton.classList.add('hidden');
      }
    });

    // Scroll to the top when the button is clicked
    backToTopButton.addEventListener('click', () => {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    });
  }

  /**
   * Initialize dropdowns
   */
  initDropdowns() {
    // Initialize column dropdown
    const columnButton = document.getElementById('columnButton');
    const columnControls = document.getElementById('columnControls');

    if (columnButton && columnControls) {
      columnButton.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        this.toggleDropdown(columnControls);
      });

      // Initialize column checkboxes
      this.initializeColumnControls();
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', (e) => {
      // Don't close dropdowns if the click is on a checkbox or label inside a dropdown
      if (e.target.closest('.dropdown-menu')) {
        return;
      }

      document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
        menu.classList.remove('show');
      });
    });
  }

  /**
   * Toggle dropdown visibility
   * @param {HTMLElement} dropdown - The dropdown element
   */
  toggleDropdown(dropdown) {
    // Close all other dropdowns
    document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
      if (menu !== dropdown) {
        menu.classList.remove('show');
      }
    });

    // Toggle this dropdown
    dropdown.classList.toggle('show');
  }

  /**
   * Initialize column controls
   */
  initializeColumnControls() {
    const columnControls = document.getElementById('columnControls');
    if (!columnControls) return;

    // Clear existing checkboxes
    columnControls.innerHTML = '';

    // Get current view type and column visibility state
    const currentViewType = stateManager.getState().currentViewType;
    const columnVisibilityState = stateManager.getState().columnVisibilityState;
    const currentColumns = columnVisibilityState[currentViewType];
    const allColumnsChecked = currentColumns.allColumns;
    const selectedColumns = currentColumns.selectedColumns || [];

    // Define column labels for each view type
    const columnLabels = {
      flows: {
        'time': 'Time',
        'env-org': 'Env-Org',
        'message-id': 'Message ID',
        'flow': 'Flow',
        'app-name': 'App Name',
        'uri': 'URI',
        'status-code': 'Status Code'
      },
      calls: {
        'time': 'Time',
        'env-org': 'Env-Org',
        'message-id': 'Message ID',
        'method': 'HTTP Method',
        'uri': 'URI',
        'response-time': 'Response Time',
        'status-code': 'Status Code'
      }
    };

    // Add "All columns" checkbox at the top
    const allColumnsLabel = document.createElement('label');
    allColumnsLabel.className = 'dropdown-item';

    const allColumnsCheckbox = document.createElement('input');
    allColumnsCheckbox.type = 'checkbox';
    allColumnsCheckbox.id = 'selectAllColumns';
    allColumnsCheckbox.checked = allColumnsChecked;

    allColumnsLabel.appendChild(allColumnsCheckbox);
    allColumnsLabel.appendChild(document.createTextNode(' All columns'));

    columnControls.appendChild(allColumnsLabel);

    // Add divider
    const divider = document.createElement('div');
    divider.className = 'dropdown-divider';
    columnControls.appendChild(divider);

    // Get the columns that are relevant to the current view type
    const relevantColumns = Object.keys(columnLabels[currentViewType]);
    const columnCheckboxes = [];

    // Create checkboxes for each relevant column
    relevantColumns.forEach(column => {
      // Skip row-number column from the UI
      if (column === 'row-number') return;

      // All individual checkboxes should be checked by default
      const label = document.createElement('label');
      label.className = 'dropdown-item';

      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.className = 'column-checkbox';
      checkbox.setAttribute('data-column', column);

      // Always check individual checkboxes when "All columns" is checked
      // Otherwise, check based on selectedColumns
      checkbox.checked = allColumnsChecked || currentColumns.selectedColumns.includes(column);

      // Get column label from the defined labels
      const columnText = columnLabels[currentViewType][column] ||
                         column.charAt(0).toUpperCase() + column.slice(1).replace(/-/g, ' ');

      label.appendChild(checkbox);
      label.appendChild(document.createTextNode(' ' + columnText));

      columnControls.appendChild(label);
      columnCheckboxes.push(checkbox);

      // Add event listener to checkbox
      checkbox.addEventListener('change', () => {
        // When unchecking an individual column
        if (!checkbox.checked) {
          // Uncheck "All columns" checkbox
          allColumnsCheckbox.checked = false;

          // Get all currently checked columns
          const checkedColumns = [];
          columnCheckboxes.forEach(cb => {
            if (cb.checked) {
              const colName = cb.getAttribute('data-column');
              if (colName) checkedColumns.push(colName);
            }
          });

          // Update state with only the checked columns
          stateManager.updateColumnVisibility(currentViewType, 'allColumns', false, checkedColumns);
        } else {
          // When checking a column

          // Check if all individual columns are now checked
          const allChecked = columnCheckboxes.every(cb => cb.checked);

          if (allChecked) {
            // If all individual columns are checked, also check the "All columns" checkbox
            allColumnsCheckbox.checked = true;
            stateManager.updateColumnVisibility(currentViewType, 'allColumns', true, []);
          } else {
            // Otherwise, update the selected columns list
            const checkedColumns = [];
            columnCheckboxes.forEach(cb => {
              if (cb.checked) {
                const colName = cb.getAttribute('data-column');
                if (colName) checkedColumns.push(colName);
              }
            });

            stateManager.updateColumnVisibility(currentViewType, 'allColumns', false, checkedColumns);
          }
        }

        // Apply visibility changes
        this.applyColumnVisibility();

        // Update filter button indicators
        this.updateFilterButtonIndicators();
      });

      // Add double-click handler to select only this column
      label.addEventListener('dblclick', (e) => {
        e.preventDefault();
        e.stopPropagation();

        // Uncheck "All columns"
        allColumnsCheckbox.checked = false;

        // Uncheck all column checkboxes
        columnCheckboxes.forEach(cb => {
          cb.checked = false;
        });

        // Check only this checkbox
        checkbox.checked = true;

        // Update state
        stateManager.updateColumnVisibility(currentViewType, 'allColumns', false, [column]);

        // Apply visibility changes
        this.applyColumnVisibility();

        // Update filter button indicators
        this.updateFilterButtonIndicators();
      });
    });

    // Add event listener to "All columns" checkbox
    allColumnsCheckbox.addEventListener('change', () => {
      const isChecked = allColumnsCheckbox.checked;

      // Update all column checkboxes - always check them when "All columns" is checked
      columnCheckboxes.forEach(checkbox => {
        checkbox.checked = isChecked;
      });

      if (isChecked) {
        // When "All columns" is checked, show all columns
        stateManager.updateColumnVisibility(currentViewType, 'allColumns', true, []);
      } else {
        // When "All columns" is unchecked, get the list of checked columns
        const checkedColumns = [];
        columnCheckboxes.forEach(cb => {
          if (cb.checked) {
            const colName = cb.getAttribute('data-column');
            if (colName) checkedColumns.push(colName);
          }
        });

        // Update state with only the checked columns
        stateManager.updateColumnVisibility(currentViewType, 'allColumns', false, checkedColumns);
      }

      // Apply visibility changes
      this.applyColumnVisibility();

      // Update filter button indicators
      this.updateFilterButtonIndicators();
    });

    // Apply current visibility state
    this.applyColumnVisibility();
  }

  /**
   * Toggle column visibility
   * @param {string} column - The column name
   * @param {boolean} isVisible - Whether the column is visible
   */
  toggleColumnVisibility(column, isVisible) {
    const currentViewType = stateManager.getState().currentViewType;

    // Update state
    stateManager.updateColumnVisibility(currentViewType, column, isVisible);

    // Apply visibility change
    this.applyColumnVisibility();

    // Update filter button indicators
    this.updateFilterButtonIndicators();
  }

  /**
   * Apply column visibility to the table
   */
  applyColumnVisibility() {
    const table = document.getElementById('logTable');
    if (!table) return;

    const currentViewType = stateManager.getState().currentViewType;
    const columnVisibilityState = stateManager.getState().columnVisibilityState;
    const currentColumns = columnVisibilityState[currentViewType];
    const allColumnsChecked = currentColumns.allColumns;
    const selectedColumns = currentColumns.selectedColumns || [];

    // Skip special properties
    const columnsToProcess = Object.entries(currentColumns).filter(
      ([key]) => key !== 'allColumns' && key !== 'selectedColumns'
    );

    columnsToProcess.forEach(([column, _]) => {
      // Determine if this column should be visible
      // If "All columns" is checked, show all columns
      // Otherwise, only show columns in the selectedColumns array
      const isVisible = allColumnsChecked || selectedColumns.includes(column);

      const header = table.querySelector(`th[data-column="${column}"]`);
      if (header) {
        header.style.display = isVisible ? '' : 'none';
      }

      const cells = table.querySelectorAll(`td[data-column="${column}"]`);
      cells.forEach(cell => {
        cell.style.display = isVisible ? '' : 'none';
      });
    });

    // Update the column checkboxes to match the current state
    const columnCheckboxes = document.querySelectorAll('.column-checkbox');
    const allColumnsCheckbox = document.getElementById('selectAllColumns');

    if (columnCheckboxes.length > 0 && allColumnsCheckbox) {
      if (allColumnsChecked) {
        // If "All columns" is checked, all individual checkboxes should be checked
        columnCheckboxes.forEach(checkbox => {
          checkbox.checked = true;
        });
        allColumnsCheckbox.checked = true;
      } else {
        // Otherwise, check only the selected columns
        columnCheckboxes.forEach(checkbox => {
          const column = checkbox.getAttribute('data-column');
          checkbox.checked = selectedColumns.includes(column);
        });

        // Check if all individual checkboxes are checked
        const allIndividualChecked = Array.from(columnCheckboxes).every(cb => cb.checked);
        allColumnsCheckbox.checked = allIndividualChecked;
      }
    }
  }

  /**
   * Setup offcanvas events
   * @param {HTMLElement} offcanvasElement - The offcanvas element
   */
  setupOffcanvasEvents(offcanvasElement) {
    // Bind the handleLogNavigation method to this instance
    this.boundHandleLogNavigation = this.handleLogNavigation.bind(this);

    // Clean up and reset state on hide
    offcanvasElement.addEventListener('hidden.bs.offcanvas', () => {
      // Remove any stray backdrops
      const backdrops = document.getElementsByClassName('offcanvas-backdrop');
      Array.from(backdrops).forEach(backdrop => backdrop.remove());

      // Remove keyboard navigation
      document.removeEventListener('keydown', this.boundHandleLogNavigation);

      // Remove active row highlight
      const previousActive = document.querySelector('.log-row-active');
      if (previousActive) {
        previousActive.classList.remove('log-row-active');
      }

      // Reset scroll position for next open
      const offcanvasContent = document.getElementById('offcanvasEntryContent');
      if (offcanvasContent) {
        offcanvasContent.scrollTop = 0;
      }
    });

    // Add keyboard navigation when offcanvas is shown
    offcanvasElement.addEventListener('shown.bs.offcanvas', () => {
      document.addEventListener('keydown', this.boundHandleLogNavigation);
    });
  }

  /**
   * Handle log navigation with keyboard
   * @param {KeyboardEvent} event - The keyboard event
   */
  handleLogNavigation(event) {
    const offcanvasElement = document.getElementById('entryDetailsOffcanvas');
    if (!offcanvasElement || !offcanvasElement.classList.contains('show')) {
      return;
    }

    const currentViewType = stateManager.getState().currentViewType;
    const offcanvasContent = document.getElementById('offcanvasEntryContent');
    const { pagination } = stateManager.getState();
    const { pageSize, currentPage } = pagination;

    if (currentViewType === 'calls') {
      // Handle calls view navigation
      const callIndex = parseInt(offcanvasContent.getAttribute('data-call-index'));
      if (isNaN(callIndex)) return;

      const visibleEntries = stateManager.getVisibleEntries();
      const calls = logProcessor.aggregateEntriesByCall(visibleEntries);

      switch(event.key) {
        case 'ArrowLeft':
          event.preventDefault();
          if (callIndex > 0) {
            // Calculate which page the previous call is on
            const targetPage = Math.floor(callIndex / pageSize) + 1;

            // If it's on a different page, update pagination first
            if (targetPage !== currentPage) {
              stateManager.setPagination({ currentPage: targetPage });
              this.showCallSequenceView();
            }

            this.navigateToCall(callIndex - 1);
          }
          break;
        case 'ArrowRight':
          event.preventDefault();
          if (callIndex < calls.length - 1) {
            // Calculate which page the next call is on
            const nextCallIndex = callIndex + 1;
            const targetPage = Math.floor(nextCallIndex / pageSize) + 1;

            // If it's on a different page, update pagination first
            if (targetPage !== currentPage) {
              stateManager.setPagination({ currentPage: targetPage });
              this.showCallSequenceView();
            }

            this.navigateToCall(nextCallIndex);
          }
          break;
      }
    } else {
      // Handle flows view navigation
      const rowIndex = parseInt(offcanvasContent.getAttribute('data-row-index'));
      if (isNaN(rowIndex)) return;

      const visibleEntries = stateManager.getVisibleEntries();

      switch(event.key) {
        case 'ArrowLeft':
          event.preventDefault();
          if (rowIndex > 0) {
            // Calculate which page the previous entry is on
            const targetPage = Math.floor((rowIndex - 1) / pageSize) + 1;

            // If it's on a different page, update pagination first
            if (targetPage !== currentPage) {
              stateManager.setPagination({ currentPage: targetPage });
              this.updateDisplayedEntries();
            }

            this.navigateToLog(rowIndex - 1);
          }
          break;
        case 'ArrowRight':
          event.preventDefault();
          if (rowIndex < visibleEntries.length - 1) {
            // Calculate which page the next entry is on
            const nextRowIndex = rowIndex + 1;
            const targetPage = Math.floor(nextRowIndex / pageSize) + 1;

            // If it's on a different page, update pagination first
            if (targetPage !== currentPage) {
              stateManager.setPagination({ currentPage: targetPage });
              this.updateDisplayedEntries();
            }

            this.navigateToLog(nextRowIndex);
          }
          break;
      }
    }
  }

  /**
   * Show toast notification
   * @param {string} message - The message to show
   * @param {boolean} isError - Whether it's an error message
   */
  showToast(message, isError = false) {
    const toastElement = document.getElementById('liveToast');
    const toastBody = document.getElementById('toastMessage');

    if (!toastElement || !toastBody) return;

    toastBody.textContent = message;

    // Set the border color based on whether it's an error or a notification
    if (isError) {
      toastElement.classList.remove('border-success');
      toastElement.classList.add('border-danger');
    } else {
      toastElement.classList.remove('border-danger');
      toastElement.classList.add('border-success');
    }

    const toast = new bootstrap.Toast(toastElement);
    toast.show();
  }

  /**
   * Display content in the file content area
   * @param {string} content - The content to display
   */
  displayContent(content) {
    const fileContent = document.getElementById('fileContent');
    if (!fileContent) return;

    // Use innerHTML instead of textContent when content is plain text
    fileContent.innerHTML = content;
    fileContent.classList.remove('hidden');

    // No scrolling after content is displayed
  }

  /**
   * Initialize pagination controls
   */
  initPagination() {
    const { pagination } = stateManager.getState();
    const { pageSize, pageSizeOptions } = pagination;

    // Create pagination container
    const paginationContainer = document.createElement('div');
    paginationContainer.className = 'pagination-container';
    paginationContainer.setAttribute('title', 'Keyboard navigation: Alt+← (previous page), Alt+→ (next page), Alt+Home (first page), Alt+End (last page)');

    // Create page size selector
    const pageSizeSelector = document.createElement('div');
    pageSizeSelector.className = 'page-size-selector';

    // Add label
    const pageSizeLabel = document.createElement('span');
    pageSizeLabel.className = 'page-size-label';
    pageSizeLabel.textContent = 'Show:';
    pageSizeSelector.appendChild(pageSizeLabel);

    // Create select element
    const select = document.createElement('select');
    select.className = 'form-select form-select-sm page-size-select';
    select.id = 'pageSizeSelect';

    // Add options
    pageSizeOptions.forEach(size => {
      const option = document.createElement('option');
      option.value = size;
      option.textContent = size;
      option.selected = size === pageSize;
      select.appendChild(option);
    });

    // Add event listener
    select.addEventListener('change', () => {
      const newPageSize = parseInt(select.value, 10);
      stateManager.setPagination({ pageSize: newPageSize, currentPage: 1 });

      // Check the current view type and update the appropriate view
      const currentViewType = stateManager.getState().currentViewType;
      if (currentViewType === 'calls') {
        this.showCallSequenceView();
      } else {
        this.updateDisplayedEntries();
      }
    });

    pageSizeSelector.appendChild(select);

    // Create pagination controls
    const paginationControls = document.createElement('div');
    paginationControls.className = 'pagination-controls';
    paginationControls.id = 'paginationControls';

    // Create a placeholder element for the right side to balance the layout
    // This ensures the pagination controls stay centered
    const rightPlaceholder = document.createElement('div');
    rightPlaceholder.className = 'page-size-selector';
    rightPlaceholder.style.visibility = 'hidden'; // Make it invisible but keep the space

    // Add pagination controls to container
    paginationContainer.appendChild(pageSizeSelector);
    paginationContainer.appendChild(paginationControls);
    paginationContainer.appendChild(rightPlaceholder);



    return paginationContainer;
  }

  /**
   * Update pagination controls
   */
  updatePaginationControls() {
    const { pagination } = stateManager.getState();
    const { currentPage } = pagination;
    const totalPages = stateManager.getTotalPages();

    const paginationControls = document.getElementById('paginationControls');
    if (!paginationControls) return;

    // Clear existing controls
    paginationControls.innerHTML = '';

    // Create pagination buttons
    const createPageButton = (page, text, isActive = false, isDisabled = false) => {
      const button = document.createElement('button');
      button.type = 'button';
      button.className = `page-btn ${isActive ? 'active' : ''} ${isDisabled ? 'disabled' : ''}`;
      button.textContent = text;

      if (!isDisabled) {
        button.addEventListener('click', () => {
          stateManager.setPagination({ currentPage: page });

          // Check the current view type and update the appropriate view
          const currentViewType = stateManager.getState().currentViewType;
          if (currentViewType === 'calls') {
            this.showCallSequenceView();
          } else {
            this.updateDisplayedEntries();
          }
        });
      }

      return button;
    };

    // First page button
    paginationControls.appendChild(createPageButton(
      1,
      '««',
      false,
      currentPage <= 1
    ));

    // Previous button
    paginationControls.appendChild(createPageButton(
      currentPage - 1,
      '«',
      false,
      currentPage <= 1
    ));

    // First page number
    if (currentPage > 3) {
      paginationControls.appendChild(createPageButton(1, '1'));

      // Ellipsis if needed
      if (currentPage > 4) {
        const ellipsis = document.createElement('span');
        ellipsis.className = 'page-ellipsis';
        ellipsis.textContent = '...';
        paginationControls.appendChild(ellipsis);
      }
    }

    // Pages around current page
    const startPage = Math.max(1, currentPage - 1);
    const endPage = Math.min(totalPages, currentPage + 1);

    for (let i = startPage; i <= endPage; i++) {
      paginationControls.appendChild(createPageButton(i, i.toString(), i === currentPage));
    }

    // Ellipsis and last page if needed
    if (currentPage < totalPages - 2) {
      if (currentPage < totalPages - 3) {
        const ellipsis = document.createElement('span');
        ellipsis.className = 'page-ellipsis';
        ellipsis.textContent = '...';
        paginationControls.appendChild(ellipsis);
      }

      paginationControls.appendChild(createPageButton(totalPages, totalPages.toString()));
    }

    // Next button
    paginationControls.appendChild(createPageButton(
      currentPage + 1,
      '»',
      false,
      currentPage >= totalPages
    ));

    // Last page button
    paginationControls.appendChild(createPageButton(
      totalPages,
      '»»',
      false,
      currentPage >= totalPages
    ));
  }

  /**
   * Update displayed entries based on current filters and pagination
   */
  updateDisplayedEntries() {
    const allFilteredEntries = stateManager.getFilteredEntries();
    const paginatedEntries = stateManager.getPaginatedEntries();
    const statusCodeStyles = logProcessor.statusCodeStyles;
    const { pagination } = stateManager.getState();
    const { currentPage, pageSize } = pagination;

    // Generate table rows
    let tableRows = '';
    paginatedEntries.forEach((entry, index) => {
      // Calculate the global index (for the entire filtered dataset)
      const globalIndex = (currentPage - 1) * pageSize + index;

      const statusCodePrefix = entry.statusCode.charAt(0);
      const badgeStyle = statusCodeStyles[statusCodePrefix] || '';
      const statusCodeDisplay = entry.statusCode !== '-' ?
        `<span class="badge rounded-pill" style="${badgeStyle}">${entry.statusCode}</span>` :
        '<span class="dash">-</span>';

      // Create a custom click handler that will handle both normal and search-filtered navigation
      const clickHandler = `onclick="
        console.log('Row clicked:', ${globalIndex});
        console.log('visibleRowsMap exists:', !!window.visibleRowsMap);
        if (window.visibleRowsMap && window.visibleRowsMap.length > 0) {
          console.log('visibleRowsMap:', JSON.stringify(window.visibleRowsMap));
          // For search-filtered rows, find the visible index
          const visibleIndex = window.visibleRowsMap.indexOf(${globalIndex});
          console.log('Found visibleIndex:', visibleIndex);
          if (visibleIndex !== -1) {
            console.log('Calling toggleRow with:', ${globalIndex}, visibleIndex);
            window.toggleRow(${globalIndex}, visibleIndex);
          } else {
            console.log('Calling toggleRow with just rowIndex:', ${globalIndex});
            window.toggleRow(${globalIndex});
          }
        } else {
          console.log('No visibleRowsMap, calling toggleRow with just rowIndex:', ${globalIndex});
          window.toggleRow(${globalIndex});
        }
      "`;

      tableRows += `
        <tr ${clickHandler} style="cursor: pointer;" data-row-index="${globalIndex}">
          <td class="text-center" data-column="row-number">${globalIndex + 1}</td>
          <td data-column="time">${entry.time !== '-' ? entry.time : '<span class="dash">-</span>'}</td>
          <td class="text-center" data-column="env-org">${entry.env}</td>
          <td class="text-center" data-column="message-id">${entry.messageId !== '-' ? entry.messageId : '<span class="dash">-</span>'}</td>
          <td class="text-center" data-column="flow">${entry.flow}</td>
          <td class="text-center" data-column="app-name">${entry.appName !== '-' ? entry.appName : '<span class="dash">-</span>'}</td>
          <td data-column="uri">${entry.uri !== '-' ? entry.uri : '<span class="dash">-</span>'}</td>
          <td class="text-center" data-column="status-code">${statusCodeDisplay}</td>
        </tr>
      `.trim();
    });

    // Update the table content
    const tableHTML = `<div class="table-responsive">
      <table class="table table-striped table-bordered" style="width: 100%;" id="logTable">
        <thead class="table-dark">
          <tr>
            <th class="text-center" data-column="row-number">#</th>
            <th class="text-center" data-column="time">Time</th>
            <th class="text-center" data-column="env-org">Env-Org</th>
            <th class="text-center" data-column="message-id">Message ID</th>
            <th class="text-center" data-column="flow">Flow</th>
            <th class="text-center" data-column="app-name">App Name</th>
            <th class="text-center" data-column="uri">URI</th>
            <th class="text-center" data-column="status-code">Status Code</th>
          </tr>
        </thead>
        <tbody>
          ${tableRows}
        </tbody>
      </table>
    </div>`.trim();

    const fileContent = document.getElementById('fileContent');
    if (fileContent) {
      // Clear existing content
      fileContent.innerHTML = '';

      // Add table
      fileContent.innerHTML = tableHTML;

      // Add pagination
      const paginationContainer = this.initPagination();
      fileContent.appendChild(paginationContainer);

      // Update pagination controls
      this.updatePaginationControls();
    }

    // Update entry counter to show pagination info
    const entryCounter = document.getElementById('entryCounter');
    if (entryCounter) {
      const start = (currentPage - 1) * pageSize + 1;
      const end = Math.min(currentPage * pageSize, allFilteredEntries.length);
      entryCounter.textContent = `${start}-${end} of ${allFilteredEntries.length} records`;
    }

    // Apply column visibility
    this.applyColumnVisibility();
  }

  /**
   * Show call sequence view with pagination
   */
  showCallSequenceView() {
    const allFilteredEntries = stateManager.getFilteredEntries();
    const allCalls = logProcessor.aggregateEntriesByCall(allFilteredEntries);
    const statusCodeStyles = logProcessor.statusCodeStyles;
    const { pagination } = stateManager.getState();
    const { currentPage, pageSize } = pagination;

    // Calculate pagination for calls
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = Math.min(startIndex + pageSize, allCalls.length);
    const paginatedCalls = allCalls.slice(startIndex, endIndex);

    // Generate the table HTML
    const tableHTML = `<div class="table-responsive">
      <table class="table table-striped table-bordered" style="width: 100%;" id="logTable">
        <thead class="table-dark">
          <tr>
            <th class="text-center" data-column="row-number">#</th>
            <th class="text-center" data-column="time">Time</th>
            <th class="text-center" data-column="env-org">Env-Org</th>
            <th class="text-center" data-column="message-id">Message ID</th>
            <th class="text-center" data-column="method">HTTP Method</th>
            <th class="text-center" data-column="uri">URI</th>
            <th class="text-center" data-column="response-time">Response Time</th>
            <th class="text-center" data-column="status-code">Status Code</th>
          </tr>
        </thead>
        <tbody>
          ${paginatedCalls.map((call, index) => {
            // Calculate the global index (for the entire filtered dataset)
            const globalIndex = startIndex + index;

            // Determine status code class
            const statusCodePrefix = call.statusCode && call.statusCode !== '-' ? call.statusCode.charAt(0) : '';
            const statusCodeStyle = statusCodePrefix ? statusCodeStyles[statusCodePrefix] || '' : '';

            // Determine response time class
            let responseTimeClass = '';
            if (call.responseTime && call.responseTime !== '-') {
              const time = parseFloat(call.responseTime);
              if (time < 1.0) {
                responseTimeClass = 'fast';
              } else if (time < 3.0) {
                responseTimeClass = 'medium';
              } else {
                responseTimeClass = 'slow';
              }
            }

            // Create a custom click handler that will handle both normal and search-filtered navigation
            const clickHandler = `onclick="
              console.log('Call row clicked:', ${globalIndex});
              console.log('visibleCallsMap exists:', !!window.visibleCallsMap);
              if (window.visibleCallsMap && window.visibleCallsMap.length > 0) {
                console.log('visibleCallsMap:', JSON.stringify(window.visibleCallsMap));
                // For search-filtered calls, find the visible index
                const visibleIndex = window.visibleCallsMap.indexOf(${globalIndex});
                console.log('Found visibleIndex:', visibleIndex);
                if (visibleIndex !== -1) {
                  console.log('Calling showCallDetails with:', ${globalIndex}, visibleIndex);
                  window.showCallDetails(${globalIndex}, visibleIndex);
                } else {
                  console.log('Calling showCallDetails with just index:', ${globalIndex});
                  window.showCallDetails(${globalIndex});
                }
              } else {
                console.log('No visibleCallsMap, calling showCallDetails with just index:', ${globalIndex});
                window.showCallDetails(${globalIndex});
              }
            "`;

            return `
              <tr style="cursor: pointer;" data-message-id="${call.messageId}" data-call-index="${globalIndex}" ${clickHandler}>
                <td class="text-center" data-column="row-number">${globalIndex + 1}</td>
                <td data-column="time">${call.time !== '-' ? call.time : '<span class="dash">-</span>'}</td>
                <td class="text-center" data-column="env-org">${call.env}</td>
                <td class="text-center" data-column="message-id">${call.messageId !== '-' ? call.messageId : '<span class="dash">-</span>'}</td>
                <td class="text-center" data-column="method">${call.httpMethod || '<span class="dash">-</span>'}</td>
                <td data-column="uri">${call.uri !== '-' ? call.uri : '<span class="dash">-</span>'}</td>
                <td class="text-center" data-column="response-time">
                  <span class="response-time ${responseTimeClass}">${call.responseTime}</span>
                </td>
                <td class="text-center" data-column="status-code">
                  ${call.statusCode && call.statusCode !== '-' ?
                    `<span class="badge rounded-pill" style="${statusCodeStyle}">${call.statusCode}</span>` :
                    '<span class="dash">-</span>'}
                </td>
              </tr>
            `;
          }).join('')}
        </tbody>
      </table>
    </div>`.trim();

    // Update the content
    const fileContent = document.getElementById('fileContent');
    if (fileContent) {
      // Clear existing content
      fileContent.innerHTML = '';

      // Add table
      fileContent.innerHTML = tableHTML;

      // Add pagination
      const paginationContainer = this.initPagination();
      fileContent.appendChild(paginationContainer);

      // Update pagination controls
      this.updatePaginationControls();
    }

    // Update entry counter to show pagination info
    const entryCounter = document.getElementById('entryCounter');
    if (entryCounter) {
      const start = startIndex + 1;
      const end = Math.min(endIndex, allCalls.length);

      entryCounter.textContent = `${start}-${end} of ${allCalls.length} API calls`;
    }

    // Apply column visibility
    this.applyColumnVisibility();
  }

  /**
   * Navigate to a specific log entry
   * @param {number} rowIndex - The index of the log entry
   */
  navigateToLog(rowIndex) {
    // Simply call the global toggleRow function
    window.toggleRow(rowIndex);
  }

  /**
   * Navigate to a specific call
   * @param {number} callIndex - The index of the call
   */
  navigateToCall(callIndex) {
    // Simply call the global showCallDetails function
    window.showCallDetails(callIndex);
  }

  /**
   * Reset filter UI elements to their default state
   */
  resetFilterUI() {
    // Reset message ID filter UI
    const selectAllMessageIds = document.getElementById('selectAllMessageIds');
    if (selectAllMessageIds) {
      selectAllMessageIds.checked = true;
    }

    // Reset flow filter UI
    const selectAllFlows = document.getElementById('selectAllFlows');
    if (selectAllFlows) {
      selectAllFlows.checked = true;
    }

    // Reset column filter UI
    const selectAllColumns = document.getElementById('selectAllColumns');
    if (selectAllColumns) {
      selectAllColumns.checked = true;
    }

    // Reset individual message ID checkboxes
    const messageIdCheckboxes = document.querySelectorAll('.message-id-checkbox');
    messageIdCheckboxes.forEach(checkbox => {
      checkbox.checked = false;
    });

    // Reset individual flow checkboxes
    const flowCheckboxes = document.querySelectorAll('.flow-checkbox');
    flowCheckboxes.forEach(checkbox => {
      checkbox.checked = false;
    });

    // Reset individual column checkboxes
    const columnCheckboxes = document.querySelectorAll('.column-checkbox');
    columnCheckboxes.forEach(checkbox => {
      checkbox.checked = false;
    });

    // Update filter button indicators
    this.updateFilterButtonIndicators();

    // Re-initialize column controls
    this.initializeColumnControls();
  }
}

// Create a singleton instance
export const uiManager = new UIManager();
