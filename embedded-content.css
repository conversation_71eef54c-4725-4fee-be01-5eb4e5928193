/* Styles for embedded content in the main page */

/* Override body styles from loaded pages */
#content body {
    margin: 0 !important;
    padding: 0 !important;
}

/* Ensure theme switcher is visible */
#content .theme-switch-wrapper {
    position: absolute;
    top: 10px;
    right: 20px;
    z-index: 1020;
}

/* Adjust container spacing for embedded content */
#content #inputContainer,
#content #processedContainer,
#content .container {
    margin-top: 20px !important;
    padding-top: 20px !important;
}

/* Ensure proper spacing for headers */
#content h2 {
    margin-top: 0px !important;
    padding-top: 0px !important;
}

/* Position offcanvas below navbar but above other content */
.offcanvas {
    z-index: 1050 !important;
    top: 57.5px !important; /* Start below navbar */
    height: calc(100% - 57.5px) !important; /* Adjust height to account for navbar */
}

/* Adjust offcanvas backdrop to start below navbar */
.offcanvas-backdrop {
    top: 57.5px !important; /* Match the navbar height */
    height: calc(100% - 57.5px) !important; /* Adjust height to start from navbar bottom */
}

/* Ensure toast notifications appear above navbar */
.toast {
    z-index: 1060 !important;
}

/* Ensure dropdowns appear above other elements */
.dropdown-menu.show {
    z-index: 1040 !important;
}

/* Override container styling for footer */
#content footer.container,
#content footer.text-center {
    margin-top: 5px !important;
    padding-top: 5px !important;
}
