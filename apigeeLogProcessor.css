/* Apigee Log Processor CSS */

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, Roboto, Oxygen, Ubuntu, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f7fa;
    font-size: 16px;
    color: #2d3748;
    line-height: 1.6;
}

.hidden {
    display: none;
}

/* Collapsible input section */
.input-section {
    max-height: 1000px;
    overflow: visible;
    opacity: 1;
    transition: max-height 0.3s ease, opacity 0.3s ease, padding 0.3s ease;
    margin-bottom: 0;
    padding: 15px 0;
    display: block;
}

.input-section.collapsed {
    max-height: 0 !important;
    overflow: hidden !important;
    padding: 0 !important;
    margin: 0 !important;
    opacity: 0 !important;
    pointer-events: none !important;
    visibility: hidden !important;
}

#toggleInputButton {
    transition: transform 0.3s ease;
    cursor: pointer;
    margin-left: 10px;
    z-index: 10;
}

#toggleInputButton i {
    display: inline-block;
}

/* Remove the rotation effect to avoid confusion */
/* #toggleInputButton.collapsed i {
    transform: rotate(180deg);
} */

#inputContainer, #processedContainer {
    max-width: 90%;
    text-align: left;
    border-radius: 12px;
    background-color: white;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);
    padding: 5px 30px;
    margin: 0 auto 10px;
    border: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
    z-index: 1;
}

/* Add styles for the hidden state to prevent layout shifts */
#processedContainer.hidden {
    display: none !important;
}

#inputContainer {
    margin-bottom: 20px;
}

/* Main fileContent styles - consolidated all fileContent rules here */
.fileContent {
    max-width: 100%;
    white-space: pre-wrap;
    padding: 10px 0 0 0;
    overflow-x: auto; /* Prevent horizontal overflow */
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.fileContent::-webkit-scrollbar {
    display: none;
}

/* Special handling for fileContent when inside table-responsive */
.fileContent .table-responsive {
    margin-top: -40px !important; /* Counteract the fileContent's margin to eliminate the unwanted gap */
    padding: 0px;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.fileContent .table-responsive::-webkit-scrollbar {
    display: none;
}

h2, h3, h4 {
    color: #1a202c;
    font-weight: 600;
}

h2 {
    position: relative;
    padding-bottom: 10px;
    margin-bottom: 5px;
}

h3 {
    margin-bottom: 5px;
}

h2:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: #e60000;
}

button {
    margin-right: 10px;
    font-weight: 500;
}

#buttons {
    margin-top: 20px;
}

p {
    text-align: center;
}

.table td, .table th {
    vertical-align: middle;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.8rem !important; /* Smaller font size for table cells - original size */
    padding: 0.25rem 0.4rem !important; /* Reduced padding for more compact rows - original size */
}

/* Hide scrollbar for all tables */
.table {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.table::-webkit-scrollbar {
    display: none;
}

.show-flow-active #processedContainer {
    max-width: 100%;
    width: 100%;
}

textarea.form-control {
    border-radius: 8px;
    border-color: #e2e8f0;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

textarea.form-control:focus {
    box-shadow: 0 0 0 3px rgba(230, 0, 0, 0.15);
    border-color: #e60000;
}

input.form-control {
    border-radius: 8px;
    transition: all 0.3s ease;
    border-color: #e2e8f0;
}

input.form-control:focus {
    box-shadow: 0 0 0 3px rgba(230, 0, 0, 0.15);
    border-color: #e60000;
}

/* Button Styles */
.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0b5ed7;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(13, 110, 253, 0.25);
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-danger:hover {
    background-color: #bb2d3b;
    border-color: #bb2d3b;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(220, 53, 69, 0.25);
}

.btn-warning {
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(255, 193, 7, 0.25);
}

.btn-info {
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(13, 202, 240, 0.25);
}

.btn-secondary {
    transition: all 0.3s ease;
    font-weight: 500;
    background-color: #edf2f7;
    border-color: #edf2f7;
    color: #4a5568;
}

.btn-secondary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    background-color: #e2e8f0;
    border-color: #e2e8f0;
    color: #2d3748;
}

.btn-success {
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(25, 135, 84, 0.25);
}

hr {
    opacity: 0.12;
    margin: 15px 0;
}

/* Back to top button */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background-color: #e60000;
    color: white;
    width: 48px;
    height: 48px;
    border: none;
    border-radius: 50%;
    box-shadow: 0 4px 14px rgba(230, 0, 0, 0.3);
    font-size: 20px;
    cursor: pointer;
    z-index: 1000;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    outline: none;
}

.back-to-top:hover {
    background-color: #cc0000;
    transform: translateY(-4px);
    box-shadow: 0 6px 18px rgba(230, 0, 0, 0.35);
}

.back-to-top.hidden {
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* Dropdown menu styling */
.dropdown {
    position: relative;
    display: inline-block;
}

.export-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 26px;
    background-color: #edf2f7;
    border: 1px solid #535353;
    border-radius: 20px;
    padding: 0 10px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #535353;
    white-space: nowrap;
    margin-top: 0;
    margin-bottom: 0;
}

#columnButton, #messageIdButton, #statusCodeButton, #flowButton, #exportButton {
    margin-right: 0;
}

/* Filter active indicator */
.export-button.filter-active {
    border: 2px solid #38a169;
    color: inherit;
}

.export-button.filter-active:hover {
    background-color: #38a169;
    color: white;
}

.export-button:hover {
    background-color: #38a169;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(56, 161, 105, 0.25);
}

/* Hide all buttons by default */
.log-entry-header .header-controls .export-button,
.log-entry-header .header-left .export-button,
.log-entry-header .view-type-selector {
    display: none;
}

/* Show buttons based on process type */
.log-entry-header[data-process-type="flow"] .header-controls .export-button,
.log-entry-header[data-process-type="flow"] .view-type-selector {
    display: inline-flex !important;
}

.log-entry-header[data-process-type="extract"] .header-left .export-button {
    display: inline-flex !important;
}

/* Explicitly hide view-type-selector in extract mode */
.log-entry-header[data-process-type="extract"] .view-type-selector {
    display: none !important;
}

/* Explicitly hide format button in flow mode */
.log-entry-header[data-process-type="flow"] #formatButton {
    display: none !important;
}

/* Style the view type selector */
.view-type-selector {
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    margin: auto 0;
    height: 26px;
    display: flex;
    align-items: center;
}

/* Hide search container when extracting message IDs */
.log-entry-header[data-process-type="extract"] ~ .search-container,
.processedContainer[data-has-header-extract="true"] .search-container {
    display: none;
}

/* Export label styling */
.export-label {
    display: inline-block;
    padding: 0 2px;
}

.export-label .export-type {
    margin: 0 2px;
}

/* Dropdown styling */
.dropdown-menu {
    position: absolute;
    top: calc(100% + 4px);
    right: 0;
    width: 220px; /* Increased from 180px to accommodate longer text */
    max-height: 400px;
    overflow-y: auto;
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    z-index: 1000;
    border: 1px solid #e2e8f0;
    padding: 6px 0;
    font-size: 14px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
    display: none;
}

/* Position the view type dropdown to the left instead of right */
#viewTypeControls.dropdown-menu {
    right: auto;
    left: 0;
}

/* Ensure dropdowns stay within viewport boundaries */
@media (max-width: 768px) {
    /* Make dropdowns responsive on small screens */
    .dropdown-menu {
        position: fixed !important;
        max-width: calc(100vw - 30px);
        max-height: 60vh;
        overflow-y: auto;
    }

    /* Specific positioning for each dropdown to ensure they're visible */
    #viewTypeControls.dropdown-menu {
        left: 15px !important;
        right: auto !important;
    }

    #columnControls.dropdown-menu,
    #messageIdControls.dropdown-menu,
    #flowControls.dropdown-menu,
    #exportControls.dropdown-menu {
        right: 15px !important;
        left: auto !important;
    }

    /* Ensure the dropdown stays in view at the screen edges */
    .dropdown-menu.edge-left {
        left: 5px !important;
        right: auto !important;
    }

    .dropdown-menu.edge-right {
        right: 5px !important;
        left: auto !important;
    }
}

#messageIdControls.dropdown-menu {
    width: 300px;
}

/* Export dropdown specific styles */
#exportControls.dropdown-menu {
    width: 250px; /* Ensure export dropdown is wide enough for longer text */
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    display: block;
}

/* Status code dropdown specific styles */
.status-code-dropdown {
    display: none !important;
}

body[data-view-type="calls"] .status-code-dropdown {
    display: flex !important;
}

#statusCodeControls.dropdown-menu {
    width: 250px;
}

/* Message ID dropdown specific styles */

#messageIdControls.dropdown-menu {
    width: 300px;
}

.dropdown-item {
    display: flex !important;
    align-items: center !important;
    padding: 6px 10px !important;
    font-size: 0.85rem;
    color: #4a5568;
    cursor: pointer;
    transition: background-color 0.1s ease;
    border-bottom: 1px solid #f7f7f7;
    width: 100%;
    margin: 0;
}

.dropdown-item:hover {
    background-color: #f7fafc;
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item input[type="checkbox"] {
    margin-right: 8px;
}

/* Indent utility with improved formatting preservation */
.indent {
    margin-left: 20px;
    max-width: 100%;
    font-family: monospace;
    background-color: rgba(0, 0, 0, 0.03);
    padding: 8px 12px;
    border-radius: 4px;
    overflow-x: hidden;
}

.wrap-content,
.indent.mono {
    white-space: pre-wrap;
    word-break: break-word;
    overflow-wrap: break-word;
}

body.dark-theme .indent {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Collapse button */
.collapse-btn {
    background: none;
    border: none;
    cursor: pointer;
    outline: none;
    font-size: 16px;
    padding: 0;
}

.collapse-icon {
    font-size: 24px;
    line-height: 1;
    transition: transform 0.3s ease;
    cursor: pointer;
}

.collapse-icon.open {
    transform: rotate(90deg);
}

/* Toast styling */
.toast {
    border-radius: 8px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.toast.border-success {
    border-color: #198754 !important;
}

.toast.border-danger {
    border-color: #dc3545 !important;
}

.toast-header {
    border-radius: 8px 8px 0 0;
    font-weight: 600;
    padding: 12px 16px;
}

.toast-body {
    padding: 16px;
    font-size: 0.95rem;
    color: #2d3748;
}

/* Dark theme for toast */
body.dark-theme .toast {
    background-color: #2d2d2d;
    color: #e0e0e0;
}

body.dark-theme .toast.border-success {
    border-color: #198754 !important;
}

body.dark-theme .toast.border-danger {
    border-color: #dc3545 !important;
}

body.dark-theme .toast-header {
    background-color: #1e1e1e;
    color: #e0e0e0;
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

body.dark-theme .toast-body {
    background-color: #2d2d2d;
    color: #e0e0e0;
}

body.dark-theme .btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* Theme Switcher */
.theme-switch-wrapper {
    display: flex;
    align-items: center;
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 100;
}

.theme-switch {
    display: inline-block;
    height: 24px;
    position: relative;
    width: 50px;
}

/* Accordion styles */
.accordion {
    --bs-accordion-bg: #fff;
    --bs-accordion-border-color: #e2e8f0;
    margin-top: 0 !important;
}

/* Remove top padding and margin from accordion headers */
h2.accordion-header {
    margin-top: 0 !important;
    padding-top: 0 !important;
}

.accordion-button {
    padding: 0.4rem 1.25rem;
    min-height: 32px;
    height: 32px;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    background-color: #64a0af;
    color: #fff;
    font-weight: 500;
    /* Remove focus outline and shadow */
    box-shadow: none !important;
    outline: none !important;
}

.accordion-button:not(.collapsed) {
    background-color: #64a0af;
    color: #fff;
    /* Remove focus outline and shadow */
    box-shadow: none !important;
    outline: none !important;
}

/* Remove focus outline when accordion button is clicked */
.accordion-button:focus {
    box-shadow: none !important;
    outline: none !important;
    border-color: #e2e8f0 !important;
}

.accordion-body {
    background-color: #fff;
    border-bottom: 1px solid #e2e8f0;
    padding: 0.75rem;
}

.accordion-item {
    border: 1px solid #e2e8f0;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
    margin-top: 0 !important;
    padding-top: 0 !important;
}

/* Dark theme accordion styles */
body.dark-theme .accordion {
    --bs-accordion-bg: #252525;
    --bs-accordion-border-color: #5b5b5b;
}

body.dark-theme .accordion-button {
    background-color: #64a0af;
    color: #fff;
    /* Remove focus outline and shadow in dark mode */
    box-shadow: none !important;
    outline: none !important;
}

body.dark-theme .accordion-button:not(.collapsed) {
    background-color: #64a0af;
    color: #fff;
    /* Remove focus outline and shadow in dark mode */
    box-shadow: none !important;
    outline: none !important;
}

/* Remove focus outline when accordion button is clicked in dark mode */
body.dark-theme .accordion-button:focus {
    box-shadow: none !important;
    outline: none !important;
    border-color: #5b5b5b !important;
}

body.dark-theme .accordion-button::after {
    filter: invert(1);
}

body.dark-theme .accordion-body {
    background-color: #222;
}

/* body.dark-theme .accordion-header {
    border-bottom: 1px solid #333;
} */

.theme-switch input {
    display: none;
}

.slider {
    background-color: #ccc;
    bottom: 0;
    cursor: pointer;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    background-color: white;
    bottom: 4px;
    content: "";
    height: 16px;
    left: 4px;
    position: absolute;
    transition: .4s;
    width: 16px;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #e60000;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* Theme switcher styles - fixed icon positioning */
.slider-icon {
    color: #fff;
    position: absolute;
    transform: translate(-50%, -50%);
    top: 50%;
    pointer-events: none
}

.slider-icon.sun {
    left: 30%;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.slider-icon.moon {
    left: 70%;
    opacity: 1;
    transition: opacity 0.4s ease;
}

input:checked + .slider .slider-icon.sun {
    opacity: 1;
}

input:checked + .slider .slider-icon.moon {
    opacity: 0;
}

/* Navigation and entry header */
.entry-navigation {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;
}

.log-entry-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 4px 16px;
    background-color: #f5f5f5;
    border-radius: 8px 8px 8px 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    z-index: 2;
    height: 45px;
    position: relative;
    margin-bottom: 0; /* Remove bottom margin from header */
    box-sizing: border-box;
}

/* The show-flow-active view uses the main fileContent definition */

.header-left {
    display: flex;
    align-items: center;
    gap: 8px;
}

.entry-counter {
    font-weight: 500;
    padding: 4px 12px;
    border-radius: 16px;
    background-color: #edf2f7;
    font-size: 12px;
    border: 1px solid #535353;
    color: #535353;
    white-space: nowrap;
    text-align: center;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
}

.header-controls {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 0.5rem;
    position: absolute;
    right: 16px;
    top: 0;
    bottom: 0;
    margin: auto 0;
    height: 26px; /* Match the height of the buttons */
}

/* Ensure consistent alignment for header controls in all views */
body[data-view-type="flows"] .header-controls,
body[data-view-type="calls"] .header-controls,
body[data-view-type="tree"] .header-controls {
    height: 26px;
    margin: auto 0;
    top: 0;
    bottom: 0;
}

/* Ensure dropdown buttons have consistent vertical alignment */
.header-controls .dropdown {
    display: flex;
    align-items: center;
    height: 100%;
}

/* Consistent vertical alignment for buttons in all views */
.export-button {
    margin-top: auto;
    margin-bottom: auto;
}

/* Offcanvas styling */
.offcanvas.offcanvas-end {
    width: 65% !important; /* Custom wider width */
}
.offcanvas-body {
    padding: 5px 10px 10px 10px;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    font-size: 0.9rem;
    line-height: 1.5;
    overflow-x: hidden; /* Prevent horizontal scrolling */
}
.offcanvas-entry-details {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    word-break: break-word;
    max-width: 100%;
    font-size: 14px;
    line-height: 1.4;
    padding: 0.5rem;
}

@media (-webkit-device-pixel-ratio: 1.25) {
    .offcanvas-title {
        font-size: 14px;
    }
    #offcanvas-status-code {
        font-size: 12px;
    }
    .offcanvas-entry-details {
        font-size: 13px;
    }
}
.offcanvas-entry-details pre {
    background-color: #f8f9fa;
    padding: 6px 8px;
    margin: 3px 0 6px 0;
    font-size: 0.85rem;
    border-radius: 5px;
    border: 1px solid #dee2e6;
    white-space: pre-wrap; /* Allow wrapping */
    word-break: break-word;
    overflow-wrap: break-word;
    max-width: 100%;
    font-family: 'Roboto Mono', monospace;
}
.offcanvas-entry-details pre code {
    white-space: pre-wrap !important; /* Force wrapping of code elements */
    word-break: break-word;
    font-family: 'Roboto Mono', monospace !important;
    font-size: 0.85rem;
    letter-spacing: 0.01em; /* Slightly increase letter spacing for better readability */
}
/* Style for property keys in JSON display */
.json-key {
    font-weight: normal;
    color: #0d6efd;  /* Royal blue, commonly used for JSON keys */
    font-family: 'Roboto Mono', monospace;
}

/* Style for HTTP header names to match JSON keys */
.http-header-name, .header-key {
    font-weight: normal;
    color: #0d6efd;  /* Royal blue, same as JSON keys */
    font-family: 'Roboto Mono', monospace;
}

/* Style for copyable fields */
.copyable-field {
    cursor: pointer;
    transition: color 0.2s;
}

.copyable-field:hover {
    color: #02da47;
}

/* body.dark-theme .copyable-field {
} */

body.dark-theme .copyable-field:hover {
    color: #0dc046;
}

/* Dark mode support for JSON keys and HTTP header names */
body.dark-theme .json-key,
body.dark-theme .http-header-name,
body.dark-theme .header-key {
    color: #61afef;  /* Light blue for dark theme, similar to VS Code's default */
}

/* Dark mode styles for the offcanvas panel */
body.dark-theme .offcanvas {
    background-color: #222;
    color: #e0e0e0;
    border-left: 1px solid #444;
}

body.dark-theme .offcanvas-title {
    color: #fff;
}

.offcanvas-header {
    padding: 15px 15px 5px 15px;
}

/* New offcanvas header layout */
.offcanvas-header-content {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    flex-wrap: nowrap;
    min-width: 0; /* Allows text truncation */
}

.offcanvas-title {
    font-size: 15px;
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    gap: 6px;
    overflow: hidden;
    min-width: 0;
}

.offcanvas-title > * {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex-shrink: 1;
}

.separator {
    color: #aaa;
    font-weight: normal;
}

.offcanvas-actions {
    display: flex;
    align-items: center;
}

#offcanvas-message-id {
    font-weight: bold;
    white-space: nowrap;
    overflow: visible;
}

#offcanvas-flow {
    font-weight: 500;
}

/* Response time colors */
.response-time {
    font-weight: 600;
}

.response-time.fast {
    color: #2e7d32;
}

.response-time.medium {
    color: #e65100;
}

.response-time.slow {
    color: #c62828;
}

body.dark-theme .response-time.fast {
    color: #4caf50;
}

body.dark-theme .response-time.medium {
    color: #ff9800;
}

body.dark-theme .response-time.slow {
    color: #f44336;
}

#offcanvas-status-code {
    /* Base styles that apply to all status codes */
    display: inline-block;
    font-size: 13px;
    padding: 0.2em 0.5em;
    border-radius: 1em;
    font-weight: 600;
    background-color: #f8f9fa;
    color: #000;
    line-height: 1;
    vertical-align: middle;
    flex-shrink: 0;
    min-width: fit-content;
}

/* Status code colors - exact match with table styles */
#offcanvas-status-code.code-1xx {
    background-color: #81c3c9;
    color: #000;
}

#offcanvas-status-code.code-2xx {
    background-color: #6ac66b;
    color: #fff;
}

#offcanvas-status-code.code-3xx {
    background-color: #f8d65d;
    color: #000;
}

#offcanvas-status-code.code-4xx {
    background-color: #ffbd59;
    color: #000;
}

#offcanvas-status-code.code-5xx {
    background-color: #ff5757;
    color: #fff;
}

/* Dark mode adjustments for the header */
body.dark-theme .offcanvas-title {
    color: #e0e0e0;
}

body.dark-theme .separator {
    color: #666;
}

body.dark-theme #offcanvas-status-code {
    background-color: #333;
    color: #e0e0e0;
}

/* Dark mode status code colors */
body.dark-theme #offcanvas-status-code.code-1xx {
    background-color: #2a7a7f;
    color: #e0e0e0;
}

body.dark-theme #offcanvas-status-code.code-2xx {
    background-color: #2e7d32;
    color: #fff;
}

body.dark-theme #offcanvas-status-code.code-3xx {
    background-color: #b38f00;
    color: #fff;
}

body.dark-theme #offcanvas-status-code.code-4xx {
    background-color: #e65100;
    color: #fff;
}

body.dark-theme #offcanvas-status-code.code-5xx {
    background-color: #c62828;
    color: #fff;
}

/* Dark mode close button */
body.dark-theme .btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
    opacity: 0.8;
}

body.dark-theme .btn-close:hover {
    opacity: 1;
}

body.dark-theme .offcanvas-body {
    color: #e0e0e0;
}

body.dark-theme .offcanvas-entry-details pre {
    background-color: #333;
    border: 1px solid #555;
    color: #e0e0e0;
    font-family: 'Roboto Mono', monospace;
}

body.dark-theme .offcanvas-entry-details p {
    color: #fff;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
}

body.dark-theme .offcanvas-entry-details .mono {
    color: #e0e0e0;
    font-family: 'Roboto Mono', monospace;
}

body.dark-theme .offcanvas-entry-details .indent {
    background-color: #333;
    border-left: 3px solid #e60000;
    color: #e0e0e0;
    font-family: 'Roboto Mono', monospace;
}


/* Date header styling for dark mode */
body.dark-theme .date-header {
    color: #9ea7b3;
}

/* Dark mode styles for the table */
body.dark-theme .table {
    color: #e0e0e0;
}

body.dark-theme .table-striped > tbody > tr:nth-of-type(odd) > * {
    background-color: #2a2a2a;
    color: #e0e0e0;
}

body.dark-theme .table-striped > tbody > tr:nth-of-type(even) > * {
    background-color: #333;
    color: #e0e0e0;
}

body.dark-theme .table-bordered {
    border-color: #444;
}

body.dark-theme .table-bordered > :not(caption) > * > * {
    border-color: #444;
}

body.dark-theme .table-dark {
    background-color: #222;
}

body.dark-theme .table-dark th {
    background-color: #222;
    color: #fff;
    border-color: #555;
}

.table tr:hover > td {
    background-color: #e3e3e3 !important;
}

body.dark-theme .table tr:hover > td {
    background-color: #414141 !important;
}

body.dark-theme .dash {
    color: #666;
}
.offcanvas-entry-details p {
    margin: 0;
    padding: 15px 0 5px 0;
    text-align: left;
    font-weight: bold;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    font-size: 0.85rem;
}
.offcanvas-entry-details .mono {
    font-family: 'Roboto Mono', monospace;
    white-space: pre-wrap; /* Allow wrapping */
    word-break: break-word;
    font-size: 0.85rem;
    letter-spacing: 0.05em; /* Slightly increase letter spacing for better readability */
}
.offcanvas-entry-details .indent {
    margin-left: 0;
    margin-bottom: 5px;
    background-color: #f8f9fa;
    padding: 6px;
    border-radius: 3px;
    border-left: 3px solid #e60000;
    white-space: pre-wrap; /* Allow wrapping */
    word-break: break-word;
    max-width: 100%;
    font-family: 'Roboto Mono', monospace;
    font-size: 0.85rem;
}

/* X-Error-Source header styling */
.x-error-source {
    color: red !important;
    font-weight: bold !important;
}
/* Enhanced search styling */
.search-container {
    margin: 0 0 10px;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    width: auto;
    height: 30px;
    border-radius: 6px;
    background-color: white;
    border: 1px solid #e2e8f0;
    padding: 0 4px 0 20px;
    margin-top: 5px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font-size: 14px;
    color: #464646;
    padding: 4px 8px;
    min-width: 200px;
}

.search-icon {
    color: #939393;
    font-size: 14px;
    margin-right: 4px;
}

.search-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 8px;
    padding-left: 8px;
    border-left: 1px solid #e2e8f0;
}

.search-options {
    display: flex;
    align-items: center;
    gap: 4px;
}

.global-search-toggle-wrapper {
    display: flex;
    align-items: center;
    margin: 0 10px;
}

.global-search-toggle {
    appearance: none;
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border: 1px solid #999;
    border-radius: 3px;
    margin-right: 5px;
    position: relative;
    cursor: pointer;
    outline: none;
}

.global-search-toggle:checked {
    background-color: #2196F3;
    border-color: #2196F3;
}

.global-search-toggle:checked::after {
    content: '✓';
    color: white;
    position: absolute;
    font-size: 12px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.global-search-label {
    font-size: 12px;
    color: #666;
    cursor: pointer;
    user-select: none;
}

.search-navigation {
    display: flex;
    align-items: center;
    gap: 4px;
}

.search-nav-btn {
    height: 24px;
    width: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background-color: transparent;
    color: #718096;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 0;
    font-size: 12px;
}

.search-nav-btn:hover:not(:disabled) {
    background-color: #edf2f7;
    color: #4a5568;
}

.search-nav-btn:disabled {
    opacity: 0.5;
    cursor: default;
}

.search-match-count {
    font-size: 12px;
    color: #718096;
    margin: 0 8px;
    white-space: nowrap;
}

.search-clear-btn {
    height: 24px;
    width: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background-color: transparent;
    color: #718096;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 0;
    font-size: 12px;
}

.search-clear-btn:hover {
    background-color: #edf2f7;
    color: #4a5568;
}

.search-help-text {
    font-size: 12px;
    color: #718096;
    margin-top: 4px;
    padding: 0 4px;
}

.search-input-wrapper:focus-within {
    border-color: #3182ce;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.15);
}

/* Search highlight styles */
.search-highlight {
    background-color: rgb(255, 255, 0);
    border-radius: 2px;
    padding: 0 2px;
    margin: 0 -2px;
}

.search-highlight-active {
    background-color: rgba(255, 165, 0);
    border-radius: 2px;
    padding: 0 2px;
    margin: 0 -2px;
    box-shadow: 0 0 0 2px rgba(255, 165, 0, 0.3);
    z-index: 1;
}

/* Consistent footer styling */
footer {
    margin-bottom: 0px;
    color: #999999;
    font-size: 0.85rem;
}

/* Dark mode - Exact match with OKD Log Processor */
body.dark-theme {
    background-color: #121212;
    color: #e0e0e0;
}

body.dark-theme #inputContainer,
body.dark-theme #processedContainer {
    background-color: #1e1e1e;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

body.dark-theme h2,
body.dark-theme h3,
body.dark-theme h4 {
    color: #f5f5f5;
}

body.dark-theme .input-section h4 {
    color: #b0b0b0;
}

body.dark-theme textarea.form-control,
body.dark-theme input.form-control {
    background-color: #2d2d2d;
    border-color: #444;
    color: #e0e0e0;
}

body.dark-theme textarea.form-control::placeholder,
body.dark-theme input.form-control::placeholder {
    color: #aaaaaa;
    opacity: 1;
}

body.dark-theme textarea.form-control:focus,
body.dark-theme input.form-control:focus {
    box-shadow: 0 0 0 3px rgba(230, 0, 0, 0.25);
    border-color: #e60000;
}

/* Offcanvas button styles */
.offcanvas-copy-btn,
.offcanvas-mask-btn {
    background: none;
    border: none;
    color: #4a5568;
    font-size: 1.1rem;
    padding: 0.25rem 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    border-radius: 4px;
}

.offcanvas-copy-btn:hover,
.offcanvas-mask-btn:hover {
    background-color: #edf2f7;
}

.offcanvas-mask-btn.active {
    color: #0d6efd;
}

.offcanvas-mask-btn.active:hover {
    background-color: #e1ecff;
}

body.dark-theme .offcanvas-copy-btn,
body.dark-theme .offcanvas-mask-btn {
    color: #cdd3dc;
}

body.dark-theme .offcanvas-copy-btn:hover,
body.dark-theme .offcanvas-mask-btn:hover {
    background-color: #5c5d5e;
        color: #e0e0e0;
}

body.dark-theme .offcanvas-mask-btn.active {
    color: #3d8bfd;
}

body.dark-theme .offcanvas-mask-btn.active:hover {
    background-color: #1a3a6e;
}

.offcanvas-copy-btn:hover {
    background-color: #edf2f7;
    color: #2d3748;
}

.offcanvas-copy-btn .bi-copy {
    font-size: 1rem;
}

body.dark-theme .log-entry-header {
    background-color: #2d2d2d;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

body.dark-theme .entry-counter {
    background-color: #484848;
    color: #ccc;
}

body.dark-theme .search-input-wrapper {
    background-color: #2d2d2d;
    border-color: #404040;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

body.dark-theme .search-input {
    color: #e0e0e0;
}

body.dark-theme .search-input::placeholder {
    color: #bababa;
}

body.dark-theme .search-icon {
    color: #bababa;
}

body.dark-theme .search-controls {
    border-left-color: #404040;
}

body.dark-theme .search-nav-btn {
    color: #bababa;
}

body.dark-theme .search-nav-btn:hover:not(:disabled) {
    background-color: #2d3748;
    color: #e2e8f0;
}

body.dark-theme .search-match-count {
    color: #bababa;
}

body.dark-theme .search-clear-btn {
    color: #bababa;
}

body.dark-theme .search-clear-btn:hover {
    background-color: #2d3748;
    color: #e2e8f0;
}

body.dark-theme .search-help-text {
    color: #bababa;
}

body.dark-theme .global-search-label {
    color: #bababa;
}

body.dark-theme .search-input-wrapper:focus-within {
    border-color: #3182ce;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.25);
}

body.dark-theme .search-highlight {
    background-color: rgb(255, 255, 0);
    color: #000000;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
}

body.dark-theme .search-highlight-active {
    background-color: rgba(255, 165, 0);
    color: #000000;
    box-shadow: 0 0 0 2px rgba(255, 165, 0, 0.2);
}

body.dark-theme .dropdown-menu {
    background-color: #2d2d2d;
    border-color: #444;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

body.dark-theme .dropdown-item {
    color: #e0e0e0;
    border-bottom-color: #444;
}

body.dark-theme .dropdown-item:hover {
    background-color: #383838;
}

body.dark-theme .table {
    color: #e0e0e0;
}

body.dark-theme .table-striped>tbody>tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05);
}

body.dark-theme .table-bordered {
    border-color: #444;
}

body.dark-theme .table-dark {
    background-color: #1a202c;
}

body.dark-theme .table-bordered>:not(caption)>*>* {
    border-color: #444;
}

body.dark-theme .btn-secondary {
    background-color: #333;
    border-color: #333;
    color: #ccc;
}

body.dark-theme .btn-secondary:hover:not(:disabled) {
    background-color: #444;
    border-color: #444;
    color: #fff;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

body.dark-theme .btn-secondary:disabled {
    background-color: #282828;
    border-color: #282828;
    color: #666;
}

body.dark-theme .export-button {
    background-color: #333;
    color: #ccc;
}

body.dark-theme .export-button.filter-active {
    border: 2px solid #38a169;
    color: #ccc;
}

body.dark-theme .export-button:hover,
body.dark-theme .export-button.filter-active:hover {
    background-color: #38a169;
    color: white;
    box-shadow: 0 2px 8px rgba(56, 161, 105, 0.4);
}

body.dark-theme .toast {
    background-color: #2d2d2d;
    border: none;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

body.dark-theme .toast-body {
    color: #e0e0e0;
}

body.dark-theme .global-search-toggle {
    border-color: #777;
}

body.dark-theme .global-search-toggle:checked {
    background-color: #417dc0;
    border-color: #417dc0;
}

body.dark-theme hr {
    border-color: rgba(255, 255, 255, 0.1);
}

/* Table cell text wrapping for better responsiveness */
.table td[data-column="uri"],
.table td[data-column="message-id"] {
    white-space: normal;
    word-break: break-word;
    max-width: 250px;
}

/* Responsive adjustments for different screen sizes */
@media (min-width: 1200px) {
    .table td[data-column="uri"],
    .table td[data-column="message-id"] {
        max-width: 300px;
    }
}

@media (min-width: 1920px) {
    .table td[data-column="uri"],
    .table td[data-column="message-id"] {
        max-width: 400px;
    }
}

@media (min-width: 2560px) {
    .table td[data-column="uri"],
    .table td[data-column="message-id"] {
        max-width: 600px;
    }
}

/* Ensure table is responsive */
.table-responsive {
    overflow-x: auto;
    margin: 0;
    padding: 0;
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* IE and Edge */
}

/* Hide scrollbar for Chrome, Safari and Opera */
.table-responsive::-webkit-scrollbar {
    display: none !important;
}

/* Improve expanded row content formatting */
#logTable tr[id^="messageRow-"] td {
    white-space: normal;
    word-break: break-word;
    padding: 16px;
}

/* Indent content should also wrap properly */
.indent {
    white-space: pre-wrap;
    word-break: break-word;
    max-width: 100%;
}

/* Hide scrollbars for message bodies while maintaining scrolling functionality */
#messageBodyContent, [id^="message-body-"] {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
}

/* Hide scrollbar for Chrome, Safari and Opera */
#messageBodyContent::-webkit-scrollbar,
[id^="message-body-"]::-webkit-scrollbar {
    display: none;
}

/* Ensure app name wraps too */
.table td[data-column="app-name"] {
    white-space: normal;
    word-break: break-word;
    max-width: 150px;
}

/* Offcanvas header layout */
.offcanvas-header-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 0.5rem;
}

.offcanvas-header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.offcanvas-header-bottom {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.offcanvas-header-left {
    min-width: 0;
    flex: 1;
}

/* Navigation styles */
.offcanvas-navigation {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.35rem;
}

/* Reset any inherited margins */
.offcanvas-navigation > * {
    margin: 0;
}

/* Ensure consistent spacing for counter */
.log-nav-counter {
    font-size: 0.75rem;
    color: #414344;
    min-width: 80px;
    text-align: center;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    padding: 0;
    border-radius: 50%;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #414344;
    transition: all 0.2s ease;
    font-size: 0.7rem;
}

.nav-btn:hover:not(:disabled) {
    background-color: #e9ecef;
    color: #212529;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.log-nav-counter {
    font-size: 0.75rem;
    color: #6c757d;
    min-width: 80px;
    text-align: center;
    font-weight: 500;
}

/* Update title styles for better overflow handling */
.offcanvas-title {
    font-size: 0.9rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin: 0;
}

/* Dark theme adaptations */
body.dark-theme .nav-btn {
    background-color: #333333;
    border-color: #4a5568;
    color: #e3e7ec;
}

body.dark-theme .nav-btn:hover:not(:disabled) {
    background-color: #5c5d5e;
    color: #e3e7ec;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Active row highlight */
.log-row-active {
    background-color: rgba(13, 110, 253, 0.15) !important;
    border-left: 3px solid #0d6efd !important;
    box-shadow: 0 0 12px rgba(13, 110, 253, 0.25) !important;
    position: relative;
}

body.dark-theme .log-nav-counter {
    color: #dbe1ea;
}

body.dark-theme .log-row-active {
    background-color: rgba(66, 153, 225, 0.25) !important;
    border-left: 3px solid #3182ce !important;
    box-shadow: 0 0 12px rgba(66, 153, 225, 0.35) !important;
}

/* Response time highlighting */
.response-time {

    font-weight: 500;
    white-space: nowrap;
}

.response-time.fast {
    color: #38a169; /* Green for fast responses */
}

.response-time.medium {
    color: #dd6b20; /* Orange for medium responses */
}

.response-time.slow {
    color: #e53e3e; /* Red for slow responses */
}

body.dark-theme .response-time.fast {
    color: #68d391;
}

body.dark-theme .response-time.medium {
    color: #f6ad55;
}

body.dark-theme .response-time.slow {
    color: #fc8181;
}

/* Ensure row heights are consistent across all table views */
#logTable tbody tr {
    height: 32px !important; /* Set a consistent height for all table rows - original size */
}

/* Specific adjustments for Call view's table rows if needed */
#logTable tr[data-message-id] {
    height: 40px; /* Match the height with flow view rows */
}

/* Table spacing improvements - consolidated rules to fix the gap between header and table */
#fileContent table.table {
    margin-top: 0;
}

/* View-specific dropdown visibility control for Calls view */
body[data-view-type="calls"] .header-controls .dropdown:has(#flowButton) {
    display: none !important;
}

/* Make message ID selector visible in all views */
body .header-controls .dropdown:has(#messageIdButton) {
    display: flex !important;
}

/* Ensure columns and export dropdowns are visible in all views */
body[data-view-type="calls"] .header-controls .dropdown:has(#columnButton),
body[data-view-type="calls"] .header-controls .dropdown:has(#exportButton) {
    display: flex !important;
}

/* Override global display at the end of file - keep the cascade priority */
.view-type-selector {
    display: none;
}

/* Only show view switcher in flow process type */
.log-entry-header[data-process-type="flow"] .view-type-selector {
    display: inline-flex !important;
}

/* Pagination styles */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    margin-top: -55px;
}

.page-size-selector {
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 150px;
}

.page-size-label {
    font-size: 14px;
    color: #8f8f8f;
}

.page-size-select {
    width: 80px;
    height: 32px;
    font-size: 14px;
    border-radius: 4px;
    border: 1px solid #e2e8f0;
    background-color: #fff;
    padding: 0 8px;
}

.pagination-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    flex-grow: 1;
}

.page-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    height: 36px;
    padding: 0 8px;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    background-color: #fff;
    color: #4a5568;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin: 0 2px;
}

/* Style for first and last page buttons */
.page-btn:first-child,
.page-btn:last-child {
    font-size: 14px;
    min-width: 40px;
    font-weight: bold;
}

.page-btn:hover:not(.disabled):not(.active) {
    background-color: #f7fafc;
    border-color: #cbd5e0;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.page-btn.active {
    background-color: #38a169;
    border-color: #38a169;
    color: #fff;
    font-weight: 500;
}

.page-btn.disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

.page-ellipsis {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    height: 36px;
    font-size: 14px;
    color: #4a5568;
    margin: 0 2px;
}