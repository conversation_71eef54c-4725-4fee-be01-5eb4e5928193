body.dark-theme .http-headers {
    background-color: #2d2d2d;
    border-color: #444;
}

.header-line {
    color: #b0b0b0;
}
body.dark-theme {
    background-color: #121212;
    color: #e0e0e0;
}

body.dark-theme #inputContainer,
body.dark-theme #processedContainer {
    background-color: #1e1e1e;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

body.dark-theme h2,
body.dark-theme h3,
body.dark-theme h4 {
    color: #f5f5f5;
}

body.dark-theme .input-section h4 {
    color: #b0b0b0;
}

body.dark-theme textarea.form-control,
body.dark-theme input.form-control {
    background-color: #2d2d2d;
    border-color: #444;
    color: #e0e0e0;
}

body.dark-theme textarea.form-control::placeholder,
body.dark-theme input.form-control::placeholder {
    color: #aaaaaa; /* Lighter color for placeholder text */
    opacity: 1;
}

body.dark-theme textarea.form-control:focus,
body.dark-theme input.form-control:focus {
    box-shadow: 0 0 0 3px rgba(230, 0, 0, 0.25);
    border-color: #e60000;
}

body.dark-theme .btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

body.dark-theme .btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0b5ed7;
    box-shadow: 0 4px 10px rgba(13, 110, 253, 0.35);
}

body.dark-theme .btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
}

body.dark-theme .btn-danger:hover {
    background-color: #bb2d3b;
    border-color: #bb2d3b;
    box-shadow: 0 4px 10px rgba(220, 53, 69, 0.35);
}

body.dark-theme .btn-secondary {
    background-color: #333;
    border-color: #333;
    color: #ccc;
}

body.dark-theme .btn-secondary:hover:not(:disabled) {
    background-color: #444;
    border-color: #444;
    color: #fff;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

body.dark-theme .btn-secondary:disabled {
    background-color: #282828;
    border-color: #282828;
    color: #666;
}

body.dark-theme hr {
    border-color: rgba(255, 255, 255, 0.1);
}

body.dark-theme #currentLogEntry {
    background-color: #1e1e1e;
    box-shadow: 0 4px 14px rgba(0, 0, 0, 0.15);
}

body.dark-theme #currentLogEntry pre {
    background-color: #252525;
    color: #e0e0e0;
}

body.dark-theme .log-entry-header {
    background-color: #2d2d2d;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    border-bottom-color: #444;
}

body.dark-theme .entry-counter {
    background-color: #333;
    color: #ccc;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

body.dark-theme .copy-button {
    background-color: #333;
    color: #ccc;
}

body.dark-theme .copy-button:hover {
    background-color: #e60000;
    color: white;
}

body.dark-theme .log-key {
    color: #ff7b7b;
}

body.dark-theme .log-string {
    color: #7ee787;
}

body.dark-theme .log-number {
    color: #79c0ff;
}

body.dark-theme .log-boolean {
    color: #ffa657;
}

body.dark-theme .log-null {
    color: #8b949e;
}

body.dark-theme .log-xml-content {
    color: #79c0ff;
    background-color: #1a202c;
    border-left-color: #79c0ff;
}

/* XML content styling for dark theme */
body.dark-theme .log-xml-content {
    background-color: #1e2430;
    border-left-color: #4dabf7;
    color: #e9ecef;
}

/* XML element styling in dark theme */
body.dark-theme .xml-tag {
    color: #7ee787;
    font-weight: 500;
}

body.dark-theme .xml-attr-name {
    color: #d2a8ff;
}

body.dark-theme .xml-attr-value {
    color: #ffa657;
}

body.dark-theme .xml-text {
    color: #e9ecef;
}

body.dark-theme .xml-comment {
    color: #8b949e;
    font-style: italic;
}

body.dark-theme .xml-cdata {
    color: #a5d6ff;
    background-color: rgba(255, 255, 255, 0.05);
    padding: 1px 3px;
    border-radius: 3px;
}

/* Preserve XML structure spacing in dark theme */
body.dark-theme .log-xml-content .xml-tag + .xml-tag,
body.dark-theme .log-xml-content .xml-text + .xml-tag {
    margin-left: 4px;
}

/* Base64 content formatting in dark theme */
body.dark-theme .log-xml-content .base64-content {
    background-color: #2d3748;
    color: #a0aec0;
    padding: 4px;
    border-radius: 3px;
}

body.dark-theme .log-xml-content .truncated-indicator {
    color: #ff79c6;
    font-style: italic;
}

/* Http header formatting in dark theme */
body.dark-theme .log-xml-content .http-header {
    color: #79c0ff;
    font-weight: 500;
}

body.dark-theme .log-xml-content {
    color: #cbd5e0;
}

body.dark-theme .timestamp-field {
    color: #bb86fc;
    background-color: #2d2d2d;
    border-left: 3px solid #bb86fc;
}

body.dark-theme footer {
    color: #aaaaaa;
}

/* Theme switcher styles */
.theme-switch-wrapper {
    display: flex;
    align-items: center;
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 100;
}

.theme-switch {
    display: inline-block;
    height: 24px;
    position: relative;
    width: 50px;
}

.theme-switch input {
    display: none;
}

.slider {
    background-color: #ccc;
    bottom: 0;
    cursor: pointer;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    background-color: white;
    bottom: 4px;
    content: "";
    height: 16px;
    left: 4px;
    position: absolute;
    transition: .4s;
    width: 16px;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #e60000;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* Theme switcher styles - fixed icon positioning */
.slider-icon {
    color: #fff;
    position: absolute;
    transform: translate(-50%, -50%);
    top: 50%;
    pointer-events: none;
}

.slider-icon.sun {
    left: 30%;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.slider-icon.moon {
    left: 70%;
    opacity: 1;
    transition: opacity 0.4s ease;
}

input:checked + .slider .slider-icon.sun {
    opacity: 1;
}

input:checked + .slider .slider-icon.moon {
    opacity: 0;
}

/* Toast in dark mode */
body.dark-theme .toast {
    background-color: #2d2d2d;
    color: #e0e0e0;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

body.dark-theme .toast.border-success {
    border-color: #198754 !important;
}

body.dark-theme .toast.border-danger {
    border-color: #dc3545 !important;
}

body.dark-theme .toast-header {
    background-color: #1e1e1e;
    color: #e0e0e0;
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

body.dark-theme .toast-body {
    background-color: #2d2d2d;
    color: #e0e0e0;
}

body.dark-theme .btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* Selected labels in dark mode */
body.dark-theme .btn-check:checked + .btn-outline-success {
    box-shadow: 0 2px 10px rgba(56, 161, 105, 0.5);
}

body.dark-theme .btn-check:checked + .btn-outline-primary {
    box-shadow: 0 2px 10px rgba(49, 130, 206, 0.5);
}

body.dark-theme .btn-check:checked + .btn-outline-warning {
    box-shadow: 0 2px 10px rgba(214, 158, 46, 0.5);
}

body.dark-theme .btn-check:checked + .btn-outline-danger {
    box-shadow: 0 2px 10px rgba(229, 62, 62, 0.5);
}

/* JobID Filter dark mode styles */
body.dark-theme .jobid-filter-container select {
    background-color: #2d2d2d;
    border-color: #444;
    color: #e0e0e0;
}

body.dark-theme .jobid-filter-container select:focus {
    background-color: #2d2d2d;
    border-color: #e60000;
    box-shadow: 0 0 0 3px rgba(230, 0, 0, 0.25);
}

body.dark-theme .jobid-filter-container option {
    background-color: #2d2d2d;
    color: #e0e0e0;
    padding: 4px 8px;
}

/* Selected option in dark mode */
body.dark-theme .jobid-filter-container select option:checked {
    background-color: #1a365d;
    color: #63b3ed;
}

body.dark-theme .jobid-filter-container select[data-has-selection="true"] {
    background-color: #1a365d;
    border-color: #63b3ed;
}

body.dark-theme .jobid-filter-container select::-webkit-scrollbar {
    width: 8px;
}

body.dark-theme .jobid-filter-container select::-webkit-scrollbar-track {
    background: #1e1e1e;
}

body.dark-theme .jobid-filter-container select::-webkit-scrollbar-thumb {
    background-color: #444;
    border-radius: 4px;
}

body.dark-theme .jobid-filter-container select::-webkit-scrollbar-thumb:hover {
    background-color: #555;
}

/* Clean and consolidated JobID dark theme styles */
body.dark-theme .jobid-filter-container {
    margin: 0;
}

body.dark-theme .jobid-dropdown-button {
    background-color: #2d2d2d;
    border-color: #444;
    color: #e0e0e0;
}

body.dark-theme .jobid-dropdown-button:hover {
    border-color: #555;
    background-color: #333;
}

body.dark-theme .jobid-dropdown-menu {
    background-color: #2d2d2d;
    border-color: #444;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

body.dark-theme .jobid-dropdown-header {
    background-color: #333;
    border-bottom-color: #444;
}

body.dark-theme .jobid-checkbox-item {
    border-bottom-color: #444;
}

body.dark-theme .jobid-checkbox-item:hover {
    background-color: #383838;
}

/* Dark mode styling for the "Select only" button */
body.dark-theme .select-only-button {
    background-color: #444;
    color: #ccc;
}

body.dark-theme .select-only-button:hover {
    background-color: #555;
    color: #fff;
}

body.dark-theme .jobid-dropdown-content::-webkit-scrollbar {
    width: 6px;
}

body.dark-theme .jobid-dropdown-content::-webkit-scrollbar-track {
    background: #2d2d2d;
}

body.dark-theme .jobid-dropdown-content::-webkit-scrollbar-thumb {
    background-color: #444;
    border-radius: 4px;
}

body.dark-theme .jobid-dropdown-content::-webkit-scrollbar-thumb:hover {
    background-color: #555;
}

/* Response sent styling for dark theme */
/* Unified message content styling for dark theme */
body.dark-theme .log-message-content {
    background-color: #1a1a1a;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

body.dark-theme .message-prefix {
    color: #4583ff;
    background-color: #2d2d2d;
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

body.dark-theme .formatted-payload {
    background-color: #1e1e1e;
    border-left-color: #4a5568;
}

/* When formatted-payload is inside log-message-content */
body.dark-theme .log-message-content .formatted-payload {
    background-color: #1e1e1e;
    border-left: none;
}

/* HTTP Message styling for dark theme */
body.dark-theme .http-message {
    background-color: #1a1a1a;
    border-left-color: #4a5568;
}

body.dark-theme .http-start-line {
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

body.dark-theme .http-method {
    color: #60a5fa;  /* Light Blue */
}

body.dark-theme .http-uri {
    color: #34d399;  /* Light Green */
}

body.dark-theme .http-version {
    color: #9ca3af;  /* Light Gray */
}

body.dark-theme .http-status-code {
    color: #60a5fa;  /* Light Blue */
}

body.dark-theme .http-reason-phrase {
    color: #e5e7eb;  /* Light Gray */
}

body.dark-theme .http-header-name {
    color: #60a5fa;  /* Light Blue */
}

body.dark-theme .http-header-value {
    color: #e5e7eb;  /* Light Gray */
}

/* Selected option in dark mode */
body.dark-theme .jobid-checkbox-item input[type="checkbox"]:checked + span {
    color: #63b3ed;
    font-weight: 500;
}

/* Response sent styling for dark theme */
body.dark-theme .response-sent {
    background-color: #1a1a1a;
}

body.dark-theme .response-prefix {
    color: #a0aec0;
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

body.dark-theme .response-json {
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

/* HTTP Message styling for dark theme */
body.dark-theme .http-message {
    background-color: #1a1a1a;
    border-left-color: #4a5568;
}

body.dark-theme .http-start-line {
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

body.dark-theme .http-method {
    color: #60a5fa;  /* Light Blue */
}

body.dark-theme .http-uri {
    color: #34d399;  /* Light Green */
}

body.dark-theme .http-version {
    color: #9ca3af;  /* Light Gray */
}

body.dark-theme .http-status-code {
    color: #60a5fa;  /* Light Blue */
}

body.dark-theme .http-reason-phrase {
    color: #e5e7eb;  /* Light Gray */
}

/* Plain text content styling for dark theme */
body.dark-theme .plain-content {
    background-color: #1a1a1a;
    border-left-color: #4b5563;
}

body.dark-theme .plain-timestamp {
    color: #818cf8;  /* Light Indigo */
}

body.dark-theme .plain-key {
    color: #3e7efe;  /* Light Purple */
}

body.dark-theme .plain-value {
    color: #e5e7eb;  /* Light Gray */
}

body.dark-theme .plain-text {
    color: #e5e7eb;  /* Light Gray */
    /* Ensure it behaves like other plain text elements */
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    white-space: pre-wrap;
    word-break: break-word;
    line-height: 1.5;
    display: block; /* Or inline-block if preferred */
}

/* Dark mode styling for export button */
body.dark-theme .export-button {
    background-color: #333;
    color: #ccc;
}

body.dark-theme .export-button:hover {
    background-color: #38a169;
    color: white;
    box-shadow: 0 2px 8px rgba(56, 161, 105, 0.4);
}

body.dark-theme .export-success {
    background-color: #38a169;
    color: white;
}

/* Search container dark theme styling */
body.dark-theme .search-container {
    color: #e0e0e0;
}

body.dark-theme .search-input-wrapper {
    background-color: #2d2d2d;
    border-color: #404040;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

body.dark-theme .search-input-wrapper:focus-within {
    border-color: #3182ce;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.25);
}

/* Search input dark styling */
body.dark-theme .search-input {
    color: #e0e0e0;
}

body.dark-theme .search-input::placeholder {
    color: #718096;
}

body.dark-theme .search-icon {
    color: #a0aec0;
}

/* Search controls dark styling */
body.dark-theme .search-controls {
    border-left-color: #404040;
}

body.dark-theme .search-option-label {
    color: #a0aec0;
}

body.dark-theme .search-option-input:checked + .search-option-label {
    background-color: #2d3748;
    color: #e2e8f0;
}

body.dark-theme .search-option-label:hover {
    background-color: #2d3748;
}

/* Search navigation dark styling */
body.dark-theme .search-nav-btn {
    color: #a0aec0;
}

body.dark-theme .search-nav-btn:hover:not(:disabled) {
    background-color: #2d3748;
    color: #e2e8f0;
}

body.dark-theme .search-match-count {
    color: #a0aec0;
}

/* Clear button dark styling */
body.dark-theme .search-clear-btn {
    color: #a0aec0;
}

body.dark-theme .search-clear-btn:hover {
    background-color: #2d3748;
    color: #e2e8f0;
}

/* Help text dark styling */
body.dark-theme .search-help-text {
    color: #a0aec0;
}

/* Global search toggle dark styling */
body.dark-theme .global-search-toggle-wrapper {
    border-color: #404040;
}

body.dark-theme .global-search-label {
    color: #a0aec0;
}

body.dark-theme .global-search-toggle:checked + .global-search-label {
    color: #3182ce;
}

/* Search highlights in dark theme */
body.dark-theme .search-highlight {
    background-color: rgba(255, 255, 0);  /* Darker Yellow */
}

body.dark-theme .search-highlight-and {
    background-color: rgba(72, 187, 120);  /* Darker Green */
    box-shadow: 0 0 0 1px rgba(72, 187, 120);
}

body.dark-theme .search-highlight-or {
    background-color: rgba(66, 153, 225);  /* Darker Blue */
    box-shadow: 0 0 0 1px rgba(66, 153, 225, 0.1);
}

body.dark-theme .search-highlight-active {
    background-color: rgba(255, 165, 0);  /* Darker Orange */
    box-shadow: 0 0 0 2px rgba(255, 165, 0, 0.2);
    z-index: 1;  /* Ensure active highlight is above others */
}

/* Ensure proper contrast for text in highlights */
body.dark-theme .search-highlight,
body.dark-theme .search-highlight-and,
body.dark-theme .search-highlight-or,
body.dark-theme .search-highlight-active {
    color: #000000;  /* Light text for dark theme */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);  /* Improve readability */
}

/* Adding dark theme styles for date filter components at the end of the file */
/* Date filter panel styles for dark theme */
.dark-theme .date-filter-panel {
    background-color: #2d2d2d;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
    border: 1px solid #444444;
}

/* Date time picker text and labels */
.dark-theme .date-time-label {
    color: #e2e8f0;
}

/* Date/time input fields styling */
.dark-theme input[type="date"],
.dark-theme input[type="time"] {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
}

.dark-theme input[type="date"]:focus,
.dark-theme input[type="time"]:focus {
    border-color: #4299e1;
    box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.25);
}

/* Date/time icons in dark mode */
.dark-theme .date-icon,
.dark-theme .time-icon {
    color: #a0aec0;
}

/* Fix date/time picker indicator color in dark mode */
.dark-theme input[type="date"]::-webkit-calendar-picker-indicator,
.dark-theme input[type="time"]::-webkit-calendar-picker-indicator {
    filter: invert(0.8);
}

/* Date filter status styling */
.dark-theme .date-filter-status {
    background-color: rgba(255, 255, 255, 0.05);
}

.dark-theme .date-filter-status.success {
    color: #48bb78;
    background-color: rgba(72, 187, 120, 0.1);
}

.dark-theme .date-filter-status.warning {
    color: #ecc94b;
    background-color: rgba(236, 201, 75, 0.1);
}

/* Active date filter button */
.dark-theme #dateFilterToggleBtn.active {
    background-color: #4299e1;
    color: white;
    border-color: #3182ce;
}

.dark-theme .date-range-input input[type="datetime-local"] {
    background-color: #1e2430;
    border-color: #4b5563;
    color: #e5e7eb;
}

.dark-theme .date-range-input input[type="datetime-local"]::-webkit-calendar-picker-indicator {
    filter: invert(1);
}

/* Global search dark theme styles */
body.dark-theme .global-search-toggle {
    border-color: #777;
}

body.dark-theme .global-search-toggle:checked {
    background-color: #417dc0;
    border-color: #417dc0;
}

body.dark-theme .global-search-info {
    background-color: #264653;
    color: #e0f2f1;
    border-color: #336270;
}

body.dark-theme .global-search-toggle-wrapper {
    border-left-color: #555;
}

body.dark-theme .global-search-label {
    color: #ccc;
}

.dark-theme .empty-message {
    color: #666;
}

/* JSON payload dark theme styling */
body.dark-theme .json-payload {
    background-color: #1e2430;
    border-left-color: #4dabf7;
}

body.dark-theme .json-payload .key {
    color: #ff7b7b;
}

body.dark-theme .json-payload .string {
    color: #7ee787;
}

body.dark-theme .json-payload .number {
    color: #79c0ff;
}

body.dark-theme .json-payload .boolean {
    color: #ffa657;
}

body.dark-theme .json-payload .null {
    color: #8b949e;
}

body.dark-theme .json-payload .brace,
body.dark-theme .json-payload .comma,
body.dark-theme .json-payload .colon {
    color: #c9d1d9;
}

/* Message format dark theme styling */
body.dark-theme .message-block {
    background-color: #1e2430;
    border-left-color: #4dabf7;
}

body.dark-theme .message-key {
    color: #79c0ff;
    font-weight: 600;
}

body.dark-theme .message-value {
    color: #c9d1d9;
}

body.dark-theme .message-response {
    color: #c9d1d9;
}

body.dark-theme .struct-key {
    color: #79c0ff;
    font-weight: 600;
}

body.dark-theme .struct-value {
    color: #c9d1d9;
}

body.dark-theme .struct-array {
    border-left-color: #444d56;
}

body.dark-theme .response-header {
    color: #d2a8ff;
    font-weight: 600;
}

body.dark-theme .response-status {
    color: #7ee787;
}

body.dark-theme .response-body {
    border-top-color: #444d56;
}
