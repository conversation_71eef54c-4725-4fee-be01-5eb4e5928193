/**
 * Credential Manager for OpenSearch Authentication
 * Implements secure credential storage with memory + sessionStorage hybrid approach
 */
import { opensearchClient } from './opensearch-client.js';
import { opensearchGuidance } from './opensearch-guidance.js';
import { stateManager } from './state.js';

class MemoryCredentialManager {
  constructor() {
    this.credentials = null;
    this.connectionTimeout = null;
  }

  storeCredentials(credentials, timeoutMinutes = 30) {
    this.credentials = credentials;
    
    // Auto-clear after timeout
    if (this.connectionTimeout) clearTimeout(this.connectionTimeout);
    this.connectionTimeout = setTimeout(() => {
      this.clearCredentials();
    }, timeoutMinutes * 60 * 1000);
  }

  getCredentials() {
    return this.credentials;
  }

  clearCredentials() {
    this.credentials = null;
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }
  }
}

class PersistentCredentialManager {
  constructor() {
    this.storageKey = 'opensearch_config_encrypted';
    this.expiryKey = 'opensearch_config_expiry';
  }

  storeCredentials(credentials, rememberMe = false) {
    if (!rememberMe) {
      // Use session storage for temporary
      return sessionStorage.setItem(this.storageKey, this.encrypt(credentials));
    }

    // Encrypt and store in localStorage
    const encrypted = this.encrypt(credentials);
    localStorage.setItem(this.storageKey, encrypted);
    
    // Set expiration (7 days)
    const expiry = Date.now() + (7 * 24 * 60 * 60 * 1000);
    localStorage.setItem(this.expiryKey, expiry.toString());
  }

  getCredentials() {
    // Check expiration first
    const expiry = localStorage.getItem(this.expiryKey);
    if (expiry && Date.now() > parseInt(expiry)) {
      this.clearCredentials();
      return null;
    }

    // Try localStorage first, then sessionStorage
    const encrypted = localStorage.getItem(this.storageKey) || 
                     sessionStorage.getItem(this.storageKey);
    
    return encrypted ? this.decrypt(encrypted) : null;
  }

  clearCredentials() {
    localStorage.removeItem(this.storageKey);
    localStorage.removeItem(this.expiryKey);
    sessionStorage.removeItem(this.storageKey);
  }

  encrypt(data) {
    // Simple base64 encoding (use proper encryption in production)
    return btoa(JSON.stringify(data));
  }

  decrypt(encrypted) {
    try {
      return JSON.parse(atob(encrypted));
    } catch (error) {
      console.error('Failed to decrypt credentials:', error);
      return null;
    }
  }
}

export class CredentialManager {
  constructor() {
    this.memoryStore = new MemoryCredentialManager();
    this.persistentStore = new PersistentCredentialManager();
    this.configModal = null;
    this.isInitialized = false;
  }

  /**
   * Initialize the credential manager
   */
  init() {
    if (this.isInitialized) return;

    console.log('Initializing Credential Manager...');

    // Initialize guidance system
    opensearchGuidance.init();

    // Create configuration modal
    this.createConfigModal();

    // Set up event listeners
    this.setupEventListeners();

    // Try to restore saved credentials
    this.restoreCredentials();

    this.isInitialized = true;
    console.log('Credential Manager initialized');
  }

  /**
   * Create the configuration modal
   */
  createConfigModal() {
    const modalHtml = `
      <div class="modal fade" id="opensearchConfigModal" tabindex="-1" aria-labelledby="opensearchConfigModalLabel" aria-hidden="true">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="opensearchConfigModalLabel">
                <i class="bi bi-gear me-2"></i>OpenSearch Configuration
              </h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <form id="opensearchConfigForm">
                <div class="mb-3">
                  <label for="opensearchUrl" class="form-label">OpenSearch URL</label>
                  <input type="url" class="form-control" id="opensearchUrl"
                         value="https://bslogmesprod1.vodafonehu:9200" required readonly>
                  <div class="form-text">
                    <i class="bi bi-info-circle text-info me-1"></i>
                    URL is pre-configured for your environment
                  </div>
                </div>

                <div class="mb-3">
                  <label for="opensearchUsername" class="form-label">Username</label>
                  <input type="text" class="form-control" id="opensearchUsername" required>
                </div>

                <div class="mb-3">
                  <label for="opensearchPassword" class="form-label">Password</label>
                  <input type="password" class="form-control" id="opensearchPassword" required>
                  <div class="form-text">
                    <i class="bi bi-shield-check text-success me-1"></i>
                    Credentials are encrypted and stored securely
                  </div>
                </div>

                <div class="mb-3">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="rememberCredentials">
                    <label class="form-check-label" for="rememberCredentials">
                      Remember credentials (7 days)
                    </label>
                    <div class="form-text">
                      Unchecked: credentials cleared when tab closes
                    </div>
                  </div>
                </div>

                <div class="alert alert-info">
                  <i class="bi bi-exclamation-triangle me-2"></i>
                  <strong>Certificate Note:</strong> If you get certificate errors, please visit
                  <a href="https://bslogmesprod1.vodafonehu:9200" target="_blank">https://bslogmesprod1.vodafone.hu:9200</a>
                  in your browser first and accept the certificate.
                </div>

                <div id="connectionStatus" class="alert" style="display: none;"></div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-outline-secondary" id="testConnectionBtn">
                <i class="bi bi-wifi me-1"></i>Test Connection
              </button>
              <button type="button" class="btn btn-outline-danger" id="clearStoredBtn">
                <i class="bi bi-trash me-1"></i>Clear Stored Data
              </button>
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
              <button type="submit" form="opensearchConfigForm" class="btn btn-primary">
                <i class="bi bi-check-lg me-1"></i>Save Configuration
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // Initialize Bootstrap modal
    this.configModal = new bootstrap.Modal(document.getElementById('opensearchConfigModal'));
  }

  /**
   * Set up event listeners
   */
  setupEventListeners() {
    // Config button
    const configButton = document.getElementById('opensearchConfigButton');
    if (configButton) {
      configButton.addEventListener('click', () => this.showConfigModal());
    }

    // Form submission
    const form = document.getElementById('opensearchConfigForm');
    if (form) {
      form.addEventListener('submit', (e) => this.handleFormSubmit(e));
    }

    // Test connection button
    const testBtn = document.getElementById('testConnectionBtn');
    if (testBtn) {
      testBtn.addEventListener('click', () => this.testConnection());
    }

    // Clear stored data button
    const clearBtn = document.getElementById('clearStoredBtn');
    if (clearBtn) {
      clearBtn.addEventListener('click', () => this.clearStoredCredentials());
    }

    // Retry connection event from guidance modal
    document.addEventListener('retryOpenSearchConnection', () => {
      this.testConnection();
    });

    // Add a test guidance button for debugging (remove in production)
    if (window.location.search.includes('debug')) {
      const debugBtn = document.createElement('button');
      debugBtn.className = 'btn btn-sm btn-outline-info ms-2';
      debugBtn.innerHTML = '<i class="bi bi-bug me-1"></i>Test Guidance';
      debugBtn.onclick = () => {
        opensearchGuidance.showGuidance('CORS', {
          endpoint: 'https://bslogmesprod1.hu:9200',
          error: 'Test CORS error'
        });
      };

      const configButton = document.getElementById('opensearchConfigButton');
      if (configButton && configButton.parentNode) {
        configButton.parentNode.appendChild(debugBtn);
      }
    }
  }

  /**
   * Show configuration modal
   */
  showConfigModal() {
    // Pre-fill form with current credentials
    const credentials = this.getCredentials();
    if (credentials) {
      document.getElementById('opensearchUrl').value = credentials.endpoint || 'https://bslogmesprod1.vodafone.hu:9200';
      document.getElementById('opensearchUsername').value = credentials.username || '';
      // Don't pre-fill password for security

      // Set the index pattern in the dropdown
      const indexPatternSelect = document.getElementById('opensearchIndexPattern');
      if (indexPatternSelect && credentials.indexPattern) {
        indexPatternSelect.value = credentials.indexPattern;
      }
    } else {
      // Set default values
      document.getElementById('opensearchUrl').value = 'https://bslogmesprod1.vodafone.hu:9200';
    }

    this.configModal.show();
  }

  /**
   * Handle form submission
   */
  async handleFormSubmit(event) {
    event.preventDefault();
    console.log('Form submission started');

    const form = event.target;
    const indexPatternSelect = document.getElementById('opensearchIndexPattern');

    const credentials = {
      endpoint: form.opensearchUrl.value,
      indexPattern: indexPatternSelect ? indexPatternSelect.value : 'apigee-prod-*',
      username: form.opensearchUsername.value,
      password: form.opensearchPassword.value,
      useDirectConnection: true // Browser-only solution
    };

    console.log('Form submitted with credentials:', {
      endpoint: credentials.endpoint,
      indexPattern: credentials.indexPattern,
      username: credentials.username,
      hasPassword: !!credentials.password
    });

    if (!credentials.username || !credentials.password) {
      this.showStatus('warning', 'Please enter both username and password');
      return;
    }

    const rememberMe = document.getElementById('rememberCredentials').checked;
    
    try {
      const result = await this.storeCredentials(credentials, { rememberMe });
      
      if (result.success) {
        this.showStatus('success', 'Configuration saved and connection successful!');
        setTimeout(() => {
          this.configModal.hide();
          this.updateConnectionStatus();
        }, 1500);
      } else {
        this.showStatus('danger', `Connection failed: ${result.error}`);
      }
    } catch (error) {
      this.showStatus('danger', `Error: ${error.message}`);
    }
  }

  /**
   * Store credentials and test connection
   */
  async storeCredentials(credentials, options = {}) {
    const { rememberMe = false, sessionOnly = false } = options;

    // Always store in memory for current session
    this.memoryStore.storeCredentials(credentials);

    // Optionally persist based on user choice
    if (!sessionOnly) {
      this.persistentStore.storeCredentials(credentials, rememberMe);
    }

    // Update OpenSearch client configuration
    opensearchClient.updateConfig(credentials);

    // Test connection immediately
    const result = await opensearchClient.testConnection();
    
    // Update state
    stateManager.updateState('opensearchConfig', {
      ...credentials,
      password: '***', // Don't store password in state
      connected: result.success,
      lastConnectionTest: new Date().toISOString()
    });

    return result;
  }

  /**
   * Get stored credentials
   */
  getCredentials() {
    // Try memory first (fastest)
    let credentials = this.memoryStore.getCredentials();
    console.log('Memory credentials:', credentials ? 'found' : 'not found');

    // Fallback to persistent storage
    if (!credentials) {
      credentials = this.persistentStore.getCredentials();
      console.log('Persistent credentials:', credentials ? 'found' : 'not found');

      // Restore to memory if found
      if (credentials) {
        this.memoryStore.storeCredentials(credentials);
      }
    }

    console.log('Final credentials:', credentials ? {
      endpoint: credentials.endpoint,
      username: credentials.username,
      hasPassword: !!credentials.password
    } : 'null');

    return credentials;
  }

  /**
   * Clear all stored credentials
   */
  clearStoredCredentials() {
    this.memoryStore.clearCredentials();
    this.persistentStore.clearCredentials();
    
    // Update state
    stateManager.updateState('opensearchConfig', {
      endpoint: '',
      indexPattern: '',
      username: '',
      password: '',
      connected: false,
      lastConnectionTest: null
    });

    this.updateConnectionStatus();
    this.showStatus('info', 'All stored credentials cleared');
  }

  /**
   * Test connection with current credentials
   */
  async testConnection() {
    console.log('testConnection called');
    const credentials = this.getCredentials();
    if (!credentials) {
      console.log('No credentials found, showing warning');
      this.showStatus('warning', 'Please enter credentials first');
      return;
    }

    this.showStatus('info', 'Testing connection...');

    try {
      opensearchClient.updateConfig(credentials);
      const result = await opensearchClient.testConnection();

      if (result.success) {
        this.showStatus('success', `Connected successfully! Cluster: ${result.cluster.name}`);
      } else {
        this.showStatus('danger', `Connection failed: ${result.error}`);

        // Show guidance based on error type
        if (result.error && result.guidance) {
          setTimeout(() => {
            opensearchGuidance.showGuidance(result.guidance.type, {
              endpoint: credentials.endpoint,
              error: result.error
            });
          }, 1000);
        } else {
          // Determine error type from error message
          let errorType = 'CONNECTION';
          if (result.error.includes('CORS') || result.error.includes('Access to fetch')) {
            errorType = 'CORS';
          } else if (result.error.includes('certificate') || result.error.includes('CERTIFICATE')) {
            errorType = 'CERTIFICATE';
          }

          setTimeout(() => {
            opensearchGuidance.showGuidance(errorType, {
              endpoint: credentials.endpoint,
              error: result.error
            });
          }, 1000);
        }
      }

      return result;
    } catch (error) {
      this.showStatus('danger', `Error: ${error.message}`);

      // Show guidance for unexpected errors
      setTimeout(() => {
        opensearchGuidance.showGuidance('CONNECTION', {
          endpoint: credentials.endpoint,
          error: error.message
        });
      }, 1000);

      return { success: false, error: error.message };
    }
  }

  /**
   * Restore credentials on page load
   */
  restoreCredentials() {
    const credentials = this.getCredentials();
    if (credentials) {
      opensearchClient.updateConfig(credentials);
      this.updateConnectionStatus();
    }
  }

  /**
   * Update connection status in UI
   */
  updateConnectionStatus() {
    const statusElement = document.getElementById('opensearchStatus');
    const searchButton = document.getElementById('opensearchSearchButton');
    const queryInput = document.getElementById('opensearchQuery');
    const helpButton = document.getElementById('opensearchHelpButton');
    const indexPatternSelect = document.getElementById('opensearchIndexPattern');

    const credentials = this.getCredentials();
    const state = stateManager.getState();
    const isConnected = credentials && state.opensearchConfig && state.opensearchConfig.connected;

    if (statusElement) {
      if (isConnected) {
        statusElement.className = 'badge bg-success';
        statusElement.textContent = 'Connected';
      } else if (credentials) {
        statusElement.className = 'badge bg-warning';
        statusElement.textContent = 'Configured';
      } else {
        statusElement.className = 'badge bg-secondary';
        statusElement.textContent = 'Not Connected';
      }
    }

    // Enable/disable OpenSearch controls
    const hasCredentials = !!credentials;
    if (searchButton) searchButton.disabled = !hasCredentials;
    if (queryInput) queryInput.disabled = !hasCredentials;
    if (helpButton) helpButton.disabled = !hasCredentials;
    // Index pattern dropdown should always be enabled in OpenSearch mode
    if (indexPatternSelect) indexPatternSelect.disabled = false;
  }

  /**
   * Show status message in modal
   */
  showStatus(type, message) {
    const statusElement = document.getElementById('connectionStatus');
    if (statusElement) {
      statusElement.className = `alert alert-${type}`;
      statusElement.textContent = message;
      statusElement.style.display = 'block';
      
      // Auto-hide after 5 seconds for non-error messages
      if (type !== 'danger') {
        setTimeout(() => {
          statusElement.style.display = 'none';
        }, 5000);
      }
    }
  }
}

// Create and export singleton instance
export const credentialManager = new CredentialManager();
