/**
 * Search Manager for Apigee Log Processor
 * Handles search functionality
 */
import { stateManager } from './state.js';
import { uiManager } from './ui.js';
import { logProcessor } from './processor.js';

export class SearchManager {
  constructor() {
    this.searchInput = null;
    this.searchTimeout = null;
    this.searchDelay = 300; // ms
    this.isGlobalSearch = false; // Default to search only in message bodies
  }

  /**
   * Initialize the search manager
   */
  init() {
    this.searchInput = document.getElementById('logSearchInput');
    if (!this.searchInput) {
      console.warn('Search input element not found');
      return;
    }

    // Add event listener for search input
    this.searchInput.addEventListener('input', this.handleSearchInput.bind(this));

    // Add event listener for search clear button
    const clearSearchBtn = document.getElementById('clearSearchBtn');
    if (clearSearchBtn) {
      clearSearchBtn.addEventListener('click', () => {
        // Clear the search input
        if (this.searchInput) {
          this.searchInput.value = '';
        }

        // Clear search results but preserve global search toggle state
        this.clearSearchResults();
      });
    }

    // Add event listener for global search toggle
    const globalSearchToggle = document.getElementById('globalSearchToggle');
    if (globalSearchToggle) {
      globalSearchToggle.addEventListener('change', (e) => {
        this.isGlobalSearch = e.target.checked;

        // Update placeholder text
        if (this.searchInput) {
          this.searchInput.placeholder = this.isGlobalSearch ?
            'Search in all fields (use AND/OR)...' :
            'Search in message bodies (use AND/OR)...';
        }

        // Re-run search if there's a search term
        if (this.searchInput && this.searchInput.value.trim()) {
          this.performSearch(this.searchInput.value.trim().toLowerCase());
        }
      });

      // Set initial placeholder text
      if (this.searchInput) {
        this.searchInput.placeholder = 'Search in message bodies (use AND/OR)...';
      }
    }

    // Add event listener for view type changes
    document.addEventListener('viewTypeChanged', () => {
      // Clear the search input when view type changes
      if (this.searchInput) {
        this.searchInput.value = '';
      }

      // Clear search results but preserve global search toggle state
      this.clearSearchResults();
    });
  }

  /**
   * Handle search input
   * @param {Event} event - The input event
   */
  handleSearchInput(event) {
    // Clear previous timeout
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    // Set new timeout to avoid searching on every keystroke
    this.searchTimeout = setTimeout(() => {
      const searchTerm = event.target.value.trim().toLowerCase();

      if (searchTerm === '') {
        // If search term is empty, just clear the results without affecting global search toggle
        this.clearSearchResults();
      } else {
        // Otherwise perform the search normally
        this.performSearch(searchTerm);
      }
    }, this.searchDelay);
  }

  /**
   * Parse search terms with AND/OR operators
   * This is a wrapper around the parseSearchTerms function in state.js
   * @param {string} searchTerm - The search term
   * @returns {Object} - Parsed search terms
   */
  parseSearchTerms(searchTerm) {
    // Get the current state
    const { entries, currentViewType, filterState } = stateManager.getState();

    // Use the same function as in state.js
    // Check if the search term contains AND or OR operators
    const hasAnd = searchTerm.includes(' and ');
    const hasOr = searchTerm.includes(' or ');

    if (hasAnd && !hasOr) {
      // AND search
      const terms = searchTerm.split(' and ').map(term => term.trim()).filter(term => term);
      return { type: 'AND', terms };
    } else if (hasOr && !hasAnd) {
      // OR search
      const terms = searchTerm.split(' or ').map(term => term.trim()).filter(term => term);
      return { type: 'OR', terms };
    } else if (hasAnd && hasOr) {
      // Complex search with both AND and OR - treat as simple search for now
      return { type: 'SIMPLE', terms: [searchTerm] };
    } else {
      // Simple search
      return { type: 'SIMPLE', terms: [searchTerm] };
    }
  }

  /**
   * Check if text matches search terms
   * This is a wrapper around the matchesSearchTerms function in state.js
   * @param {string} text - The text to search in
   * @param {Object} parsedTerms - The parsed search terms
   * @returns {boolean} - Whether the text matches the search terms
   */
  matchesSearchTerms(text, parsedTerms) {
    if (!text || typeof text !== 'string') return false;

    const lowerText = text.toLowerCase();

    if (parsedTerms.type === 'AND') {
      // All terms must match
      return parsedTerms.terms.every(term => lowerText.includes(term));
    } else if (parsedTerms.type === 'OR') {
      // At least one term must match
      return parsedTerms.terms.some(term => lowerText.includes(term));
    } else {
      // Simple search
      return lowerText.includes(parsedTerms.terms[0]);
    }
  }

  /**
   * Perform search
   * @param {string} searchTerm - The search term
   */
  performSearch(searchTerm) {
    if (!searchTerm) {
      // If search term is empty, just clear the results without affecting global search toggle
      this.clearSearchResults();
      return;
    }

    const entries = stateManager.getState().entries;
    const currentViewType = stateManager.getState().currentViewType;

    // Parse search terms
    const parsedTerms = this.parseSearchTerms(searchTerm);

    if (currentViewType === 'flows') {
      this.searchFlowsView(entries, searchTerm, parsedTerms);
    } else if (currentViewType === 'calls') {
      this.searchCallsView(entries, searchTerm, parsedTerms);
    }
  }

  /**
   * Search in flows view
   * @param {Array} entries - The entries to search in
   * @param {string} searchTerm - The search term
   * @param {Object} parsedTerms - The parsed search terms
   */
  searchFlowsView(entries, searchTerm, parsedTerms) {
    // Update the state with the search term
    const currentViewType = stateManager.getState().currentViewType;
    stateManager.updateFilterState(currentViewType, {
      searchTerm: searchTerm,
      isGlobalSearch: this.isGlobalSearch
    });

    // Get the filtered entries based on the updated state
    const filteredEntries = stateManager.getVisibleEntries();
    const matchCount = filteredEntries.length;
    const matchesFound = matchCount > 0;

    // Update the UI to show only the filtered entries
    uiManager.updateDisplayedEntries();

    // Update entry counter
    const entryCounter = document.getElementById('entryCounter');
    if (entryCounter) {
      if (matchesFound) {
        entryCounter.textContent = `${matchCount} records match "${searchTerm}"`;
      } else {
        entryCounter.textContent = `No records match "${searchTerm}"`;
      }
    }

    // Update search match count
    const searchMatchCount = document.getElementById('searchMatchCount');
    if (searchMatchCount) {
      searchMatchCount.textContent = matchesFound ? `${matchCount} matches` : '0 matches';
    }

    // Enable/disable navigation buttons
    const prevMatchBtn = document.getElementById('prevMatchBtn');
    const nextMatchBtn = document.getElementById('nextMatchBtn');
    if (prevMatchBtn) prevMatchBtn.disabled = !matchesFound;
    if (nextMatchBtn) nextMatchBtn.disabled = !matchesFound;

    // Show search clear button
    this.toggleSearchClearButton(true);
  }

  /**
   * Search in calls view
   * @param {Array} entries - The entries to search in
   * @param {string} searchTerm - The search term
   * @param {Object} parsedTerms - The parsed search terms
   */
  searchCallsView(entries, searchTerm, parsedTerms) {
    // Update the state with the search term
    const currentViewType = stateManager.getState().currentViewType;
    stateManager.updateFilterState(currentViewType, {
      searchTerm: searchTerm,
      isGlobalSearch: this.isGlobalSearch
    });

    // Get the filtered entries based on the updated state
    const filteredEntries = stateManager.getVisibleEntries();

    // Aggregate the filtered entries by call
    const calls = logProcessor.aggregateEntriesByCall(filteredEntries);
    const matchCount = calls.length;
    const matchesFound = matchCount > 0;

    // Update the UI to show only the filtered calls
    uiManager.showCallSequenceView();

    // Update entry counter
    const entryCounter = document.getElementById('entryCounter');
    if (entryCounter) {
      if (matchesFound) {
        entryCounter.textContent = `${matchCount} API calls match "${searchTerm}"`;
      } else {
        entryCounter.textContent = `No API calls match "${searchTerm}"`;
      }
    }

    // Update search match count
    const searchMatchCount = document.getElementById('searchMatchCount');
    if (searchMatchCount) {
      searchMatchCount.textContent = matchesFound ? `${matchCount} matches` : '0 matches';
    }

    // Enable/disable navigation buttons
    const prevMatchBtn = document.getElementById('prevMatchBtn');
    const nextMatchBtn = document.getElementById('nextMatchBtn');
    if (prevMatchBtn) prevMatchBtn.disabled = !matchesFound;
    if (nextMatchBtn) nextMatchBtn.disabled = !matchesFound;

    // Show search clear button
    this.toggleSearchClearButton(true);
  }

  /**
   * Clear search results without affecting the global search toggle
   */
  clearSearchResults() {
    // Update the state to clear the search term but preserve isGlobalSearch
    const currentViewType = stateManager.getState().currentViewType;
    const currentIsGlobalSearch = stateManager.getState().filterState[currentViewType].isGlobalSearch;

    stateManager.updateFilterState(currentViewType, {
      searchTerm: '',
      isGlobalSearch: currentIsGlobalSearch // Preserve the current global search state
    });

    // Update the UI to show all entries
    if (currentViewType === 'flows') {
      uiManager.updateDisplayedEntries();
    } else if (currentViewType === 'calls') {
      uiManager.showCallSequenceView();
    }

    // Update entry counter
    const entryCounter = document.getElementById('entryCounter');
    if (entryCounter) {
      const visibleEntries = stateManager.getVisibleEntries();

      if (currentViewType === 'flows') {
        entryCounter.textContent = `${visibleEntries.length} records found`;
      } else if (currentViewType === 'calls') {
        const calls = logProcessor.aggregateEntriesByCall(visibleEntries);
        entryCounter.textContent = `${calls.length} API calls found`;
      }
    }

    // Reset search match count
    const searchMatchCount = document.getElementById('searchMatchCount');
    if (searchMatchCount) {
      searchMatchCount.textContent = '0 matches';
    }

    // Disable navigation buttons
    const prevMatchBtn = document.getElementById('prevMatchBtn');
    const nextMatchBtn = document.getElementById('nextMatchBtn');
    if (prevMatchBtn) prevMatchBtn.disabled = true;
    if (nextMatchBtn) nextMatchBtn.disabled = true;

    // Reset global search state if it exists in window object
    if (window.globalSearchTerm !== undefined) {
      window.globalSearchTerm = '';
      window.globalSearchMatches = 0;
    }

    // Clear any search highlights in the current log entry
    if (window.clearSearchHighlights && typeof window.clearSearchHighlights === 'function') {
      window.clearSearchHighlights();
    }

    // Hide search clear button
    this.toggleSearchClearButton(false);
  }

  /**
   * Clear search completely, including resetting the global search toggle
   */
  clearSearch() {
    // Clear search input
    if (this.searchInput) {
      this.searchInput.value = '';
    }

    // Reset global search toggle
    const globalSearchToggle = document.getElementById('globalSearchToggle');
    if (globalSearchToggle) {
      globalSearchToggle.checked = false;
      this.isGlobalSearch = false;
    }

    // Update the state to clear the search term and reset isGlobalSearch
    const currentViewType = stateManager.getState().currentViewType;
    stateManager.updateFilterState(currentViewType, {
      searchTerm: '',
      isGlobalSearch: false
    });

    // Update the UI to show all entries
    if (currentViewType === 'flows') {
      uiManager.updateDisplayedEntries();
    } else if (currentViewType === 'calls') {
      uiManager.showCallSequenceView();
    }

    // Update entry counter
    const entryCounter = document.getElementById('entryCounter');
    if (entryCounter) {
      const visibleEntries = stateManager.getVisibleEntries();

      if (currentViewType === 'flows') {
        entryCounter.textContent = `${visibleEntries.length} records found`;
      } else if (currentViewType === 'calls') {
        const calls = logProcessor.aggregateEntriesByCall(visibleEntries);
        entryCounter.textContent = `${calls.length} API calls found`;
      }
    }

    // Reset search match count
    const searchMatchCount = document.getElementById('searchMatchCount');
    if (searchMatchCount) {
      searchMatchCount.textContent = '0 matches';
    }

    // Disable navigation buttons
    const prevMatchBtn = document.getElementById('prevMatchBtn');
    const nextMatchBtn = document.getElementById('nextMatchBtn');
    if (prevMatchBtn) prevMatchBtn.disabled = true;
    if (nextMatchBtn) nextMatchBtn.disabled = true;

    // Reset global search state if it exists in window object
    if (window.globalSearchTerm !== undefined) {
      window.globalSearchTerm = '';
      window.globalSearchMatches = 0;
    }

    // Clear any search highlights in the current log entry
    if (window.clearSearchHighlights && typeof window.clearSearchHighlights === 'function') {
      window.clearSearchHighlights();
    }

    // Hide search clear button
    this.toggleSearchClearButton(false);
  }

  /**
   * Toggle search clear button visibility
   * @param {boolean} show - Whether to show the button
   */
  toggleSearchClearButton(show) {
    const clearSearchBtn = document.getElementById('clearSearchBtn');
    if (clearSearchBtn) {
      if (show) {
        clearSearchBtn.classList.remove('hidden');
      } else {
        clearSearchBtn.classList.add('hidden');
      }
    }
  }
}

// Create a singleton instance
export const searchManager = new SearchManager();
