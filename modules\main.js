/**
 * Main entry point for Apigee Log Processor
 * Initializes all modules and sets up global event handlers
 */
import { stateManager } from './state.js';
import { themeManager } from './theme.js';
import { logProcessor } from './processor.js';
import { uiManager } from './ui.js';
import { searchManager } from './search.js';
import { exportManager } from './export.js';
import { modeManager } from './mode-manager.js';
import { credentialManager } from './credential-manager.js';
import { opensearchClient } from './opensearch-client.js';
import { opensearchTransformer } from './opensearch-transformer.js';
import { opensearchGuidance } from './opensearch-guidance.js';
import { maskSensitiveData, maskContent, highlightSearchTerms } from './utilities.js';

// Initialize all modules
export function initializeApp() {
  console.log('Initializing Apigee Log Processor...');

  // Initialize managers
  themeManager.init();
  modeManager.init();
  credentialManager.init();
  uiManager.init();
  searchManager.init();
  exportManager.init();

  // Set up event listeners
  setupEventListeners();

  // Set up offcanvas event listeners
  setupOffcanvasEventListeners();

  // Set up OpenSearch event listeners
  setupOpenSearchEventListeners();

  // Expose necessary functions to window for HTML event handlers
  exposeGlobalFunctions();

  // Expose clearSearchHighlights function to window
  window.clearSearchHighlights = clearSearchHighlights;

  // Expose OpenSearch test functions for debugging
  window.testOpenSearchConnection = testOpenSearchConnection;
  window.acceptOpenSearchCertificate = acceptOpenSearchCertificate;
  window.opensearchClient = opensearchClient;
  window.credentialManager = credentialManager;

  console.log('Apigee Log Processor initialized');
}

/**
 * Set up OpenSearch specific event listeners
 */
function setupOpenSearchEventListeners() {
  // OpenSearch search button
  const searchButton = document.getElementById('opensearchSearchButton');
  if (searchButton) {
    searchButton.addEventListener('click', handleOpenSearchSearch);
  }

  // OpenSearch clear button
  const clearButton = document.getElementById('opensearchClearButton');
  if (clearButton) {
    clearButton.addEventListener('click', handleOpenSearchClear);
  }

  // OpenSearch help button
  const helpButton = document.getElementById('opensearchHelpButton');
  if (helpButton) {
    helpButton.addEventListener('click', showOpenSearchHelp);
  }

  // OpenSearch query input (Enter key)
  const queryInput = document.getElementById('opensearchQuery');
  if (queryInput) {
    queryInput.addEventListener('keypress', (event) => {
      if (event.key === 'Enter' && !queryInput.disabled) {
        handleOpenSearchSearch();
      }
    });
  }
}

/**
 * Handle OpenSearch search
 */
async function handleOpenSearchSearch() {
  const queryInput = document.getElementById('opensearchQuery');
  const searchButton = document.getElementById('opensearchSearchButton');
  const indexPatternSelect = document.getElementById('opensearchIndexPattern');
  const timeRangeSelect = document.getElementById('timeRange');
  const searchSpinner = document.getElementById('searchSpinner');

  if (!queryInput || !searchButton) return;

  const query = queryInput.value.trim();
  const indexPattern = indexPatternSelect ? indexPatternSelect.value : 'apigee-prod-*';
  const timeRange = timeRangeSelect ? timeRangeSelect.value : '1d';

  if (!query) {
    showToast('Please enter a search query', 'warning');
    return;
  }

  try {
    // Show loading spinner
    if (searchSpinner) {
      searchSpinner.classList.remove('d-none');
    }

    // Disable button during search
    searchButton.disabled = true;
    queryInput.disabled = true;

    // Update OpenSearch client with selected index pattern
    opensearchClient.updateConfig({ indexPattern });

    // Execute OpenSearch query using the processor with time range
    const result = await logProcessor.processOpenSearchQuery(query, { timeRange });

    if (result.success) {
      showToast(`Found ${result.totalHits} results from ${indexPattern} in ${result.took}ms`, 'success');

      // Show processed container
      const processedContainer = document.getElementById('processedContainer');
      if (processedContainer) {
        processedContainer.classList.remove('hidden');
      }

      // Hide input container if user preference is set
      const inputContainer = document.getElementById('inputContainer');
      if (inputContainer) {
        // You could add auto-collapse logic here if desired
      }
    } else {
      showToast(`Search failed: ${result.error}`, 'error');
    }

  } catch (error) {
    console.error('OpenSearch search failed:', error);
    showToast('Search failed: ' + error.message, 'error');
  } finally {
    // Hide loading spinner
    if (searchSpinner) {
      searchSpinner.classList.add('d-none');
    }

    // Re-enable UI
    searchButton.disabled = false;
    queryInput.disabled = false;
  }
}

/**
 * Handle OpenSearch clear
 */
function handleOpenSearchClear() {
  const queryInput = document.getElementById('opensearchQuery');
  if (queryInput) {
    queryInput.value = '';
  }

  // Clear any OpenSearch results
  stateManager.setState({
    opensearchResults: [],
    opensearchQuery: '',
    opensearchTotalHits: 0
  });

  // Hide processed container
  const processedContainer = document.getElementById('processedContainer');
  if (processedContainer) {
    processedContainer.classList.add('hidden');
  }

  showToast('OpenSearch results cleared', 'info');
}

/**
 * Show OpenSearch help
 */
function showOpenSearchHelp() {
  const helpContent = `
    <h6>OpenSearch Query Examples:</h6>
    <p><strong>Simple Queries:</strong></p>
    <ul>
      <li><code>error</code> - Search for "error" in all fields</li>
      <li><code>messageId:12345</code> - Search for specific message ID</li>
      <li><code>statusCode:500</code> - Search for 500 status codes</li>
      <li><code>uri:/api/users</code> - Search for specific URI</li>
    </ul>
    <p><strong>OpenSearch DSL (JSON):</strong></p>
    <ul>
      <li><code>{"match": {"messageBody": "error"}}</code></li>
      <li><code>{"term": {"statusCode": "500"}}</code></li>
      <li><code>{"bool": {"must": [{"match": {"messageBody": "error"}}, {"term": {"statusCode": "500"}}]}}</code></li>
    </ul>
    <p><small>You can use either simple queries or full OpenSearch DSL JSON syntax.</small></p>
  `;

  // Create a simple alert for now (could be enhanced with a proper modal)
  const alertDiv = document.createElement('div');
  alertDiv.className = 'alert alert-info alert-dismissible fade show';
  alertDiv.innerHTML = `
    ${helpContent}
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  `;

  // Insert after the query input
  const queryInput = document.getElementById('opensearchQuery');
  if (queryInput && queryInput.parentNode) {
    queryInput.parentNode.insertBefore(alertDiv, queryInput.nextSibling);

    // Auto-remove after 15 seconds
    setTimeout(() => {
      if (alertDiv.parentNode) {
        alertDiv.remove();
      }
    }, 15000);
  }
}

/**
 * Show toast notification
 */
function showToast(message, type = 'info') {
  // Simple console log for now (could be enhanced with actual toast notifications)
  console.log(`[${type.toUpperCase()}] ${message}`);

  // You could implement actual toast notifications here
  // For now, we'll use a simple alert for important messages
  if (type === 'error') {
    alert(message);
  }
}

/**
 * Accept OpenSearch certificate (for debugging)
 */
function acceptOpenSearchCertificate() {
  const endpoint = 'https://bslogmesprod1.vodafone.hu:9200';
  console.log('Opening OpenSearch URL to accept certificate...');
  console.log('Please accept the certificate in the new tab, then return here to test connection.');
  window.open(endpoint, '_blank');
}

/**
 * Test OpenSearch connection (for debugging)
 */
async function testOpenSearchConnection() {
  console.log('Testing OpenSearch connection...');

  try {
    // Test with your provided credentials
    const testConfig = {
      endpoint: 'https://bslogmesprod1.vodafone.hu:9200',
      indexPattern: 'apigee-prod-*',
      username: 'username', // Replace with actual username
      password: 'password'  // Replace with actual password
    };

    opensearchClient.updateConfig(testConfig);
    const result = await opensearchClient.testConnection();

    console.log('Connection test result:', result);

    if (result.success) {
      console.log('✅ OpenSearch connection successful!');
      console.log('Cluster:', result.cluster);
      console.log('Available indices:', result.indices);

      // Test a simple search
      const searchResult = await opensearchClient.search({ match_all: {} }, { size: 1 });
      console.log('Sample search result:', searchResult);

      return result;
    } else {
      console.error('❌ OpenSearch connection failed:', result.error);
      return result;
    }
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    return { success: false, error: error.message };
  }
}

/**
 * Set up event listeners
 */
function setupEventListeners() {
  // Toggle input section button click
  const toggleInputButton = document.getElementById('toggleInputButton');
  if (toggleInputButton) {
    toggleInputButton.addEventListener('click', () => {
      const inputSection = document.getElementById('inputSection');
      if (inputSection) {
        // Toggle collapsed state
        inputSection.classList.toggle('collapsed');
        toggleInputButton.classList.toggle('collapsed');

        // Update button title and icon based on state
        if (inputSection.classList.contains('collapsed')) {
          toggleInputButton.setAttribute('title', 'Expand input section');
          // Change to down arrow when collapsed (indicating "click to expand")
          const icon = toggleInputButton.querySelector('i');
          if (icon) {
            icon.className = 'bi bi-chevron-down';
          }
        } else {
          toggleInputButton.setAttribute('title', 'Collapse input section');
          // Change to up arrow when expanded (indicating "click to collapse")
          const icon = toggleInputButton.querySelector('i');
          if (icon) {
            icon.className = 'bi bi-chevron-up';
          }
        }
      }
    });
  }

  // Process button click
  const processButton = document.getElementById('processButton');
  if (processButton) {
    processButton.addEventListener('click', () => {
      const fileInput = document.getElementById('fileInput');
      const textInput = document.getElementById('textInput');

      if (fileInput.files.length > 0) {
        logProcessor.processData('flow', fileInput.files[0]);
      } else if (textInput.value.trim()) {
        logProcessor.processData('flow', textInput.value);
      } else {
        uiManager.showToast('Please provide a log file or paste log content.', true);
      }
    });
  }

  // Extract button click
  const extractButton = document.getElementById('extractButton');
  if (extractButton) {
    extractButton.addEventListener('click', () => {
      const fileInput = document.getElementById('fileInput');
      const textInput = document.getElementById('textInput');

      if (fileInput.files.length > 0) {
        logProcessor.processData('extract', fileInput.files[0]);
      } else if (textInput.value.trim()) {
        logProcessor.processData('extract', textInput.value);
      } else {
        uiManager.showToast('Please provide a log file or paste log content.', true);
      }
    });
  }

  // Clear button click
  const clearButton = document.getElementById('clearButton');
  if (clearButton) {
    clearButton.addEventListener('click', () => {
      const textInput = document.getElementById('textInput');
      const fileInput = document.getElementById('fileInput');
      const fileContent = document.getElementById('fileContent');
      const processedContainer = document.getElementById('processedContainer');
      const fileLabel = document.getElementById('fileLabel');
      const searchInput = document.getElementById('logSearchInput');
      const searchMatchCount = document.getElementById('searchMatchCount');
      const prevMatchBtn = document.getElementById('prevMatchBtn');
      const nextMatchBtn = document.getElementById('nextMatchBtn');
      const globalSearchToggle = document.getElementById('globalSearchToggle');

      // Clear text input
      if (textInput) {
        textInput.value = '';
      }

      // Clear file input
      if (fileInput) {
        fileInput.value = '';
        if (fileLabel) {
          fileLabel.textContent = 'Choose file';
        }
      }

      // Clear file content
      if (fileContent) {
        fileContent.innerHTML = '';
      }

      // Hide processed container
      if (processedContainer) {
        processedContainer.classList.add('hidden');
      }

      // Clear search input and reset search state
      if (searchInput) {
        searchInput.value = '';
      }

      // Reset search match count
      if (searchMatchCount) {
        searchMatchCount.textContent = '0 matches';
      }

      // Disable search navigation buttons
      if (prevMatchBtn) prevMatchBtn.disabled = true;
      if (nextMatchBtn) nextMatchBtn.disabled = true;

      // Reset global search toggle
      if (globalSearchToggle) {
        globalSearchToggle.checked = false;
      }

      // Reset global search state if it exists in window object
      if (window.globalSearchTerm !== undefined) {
        window.globalSearchTerm = '';
        window.globalSearchMatches = 0;
        window.globalSearchActive = false;
      }

      // Reset state
      stateManager.setState({
        entries: [],
        processedContent: ''
      });

      // Reset filter states
      logProcessor.initializeFilterStates([]);

      // Clear any search highlights
      const clearSearchHighlights = window.clearSearchHighlights;
      if (typeof clearSearchHighlights === 'function') {
        clearSearchHighlights();
      }

      // Expand input section when clearing
      const inputSection = document.getElementById('inputSection');
      const toggleInputButton = document.getElementById('toggleInputButton');
      if (inputSection && toggleInputButton) {
        // Expand the input section
        inputSection.classList.remove('collapsed');
        toggleInputButton.classList.remove('collapsed');
        toggleInputButton.setAttribute('title', 'Collapse input section');

        // Change to up arrow when expanded (indicating "click to collapse")
        const icon = toggleInputButton.querySelector('i');
        if (icon) {
          icon.className = 'bi bi-chevron-up';
          console.log('Setting icon to up arrow for expanded state');
        }
      }
    });
  }

  // Copy button click
  const copyButton = document.getElementById('copyButton');
  if (copyButton) {
    copyButton.addEventListener('click', () => {
      const processedContent = stateManager.getState().processedContent;
      const fileContent = document.getElementById('fileContent');

      if (processedContent) {
        navigator.clipboard.writeText(processedContent)
          .then(() => {
            uiManager.showToast('Content copied to clipboard');
          })
          .catch(err => {
            console.error('Failed to copy content:', err);
            uiManager.showToast('Failed to copy content', true);
          });
      } else if (fileContent && fileContent.innerText) {
        navigator.clipboard.writeText(fileContent.innerText)
          .then(() => {
            uiManager.showToast('Content copied to clipboard');
          })
          .catch(err => {
            console.error('Failed to copy content:', err);
            uiManager.showToast('Failed to copy content', true);
          });
      } else {
        uiManager.showToast('No content to copy', true);
      }
    });
  }

  // Format button click
  const formatButton = document.getElementById('formatButton');
  if (formatButton) {
    formatButton.addEventListener('click', () => {
      const processedContent = stateManager.getState().processedContent;
      const isFormatted = stateManager.getState().isFormattedForOpenSearch;

      if (!processedContent) {
        uiManager.showToast('No content to format', true);
        return;
      }

      if (isFormatted) {
        // Revert to original content
        uiManager.displayContent(processedContent);
        formatButton.querySelector('span').textContent = 'Format for OpenSearch';
        stateManager.updateState('isFormattedForOpenSearch', false);
      } else {
        // Format for OpenSearch
        const messageIds = processedContent.split('\n');
        const formattedOutput = messageIds.map(id => `"${id}"`).join(' OR ');

        uiManager.displayContent(formattedOutput);

        // Copy to clipboard
        navigator.clipboard.writeText(formattedOutput)
          .then(() => {
            uiManager.showToast('Message IDs formatted for OpenSearch and copied to clipboard!', false);
          })
          .catch(err => {
            console.error('Could not copy text: ', err);
            uiManager.showToast('Message IDs formatted for OpenSearch, but failed to copy to clipboard', true);
          });

        formatButton.querySelector('span').textContent = 'Show Original';
        stateManager.updateState('isFormattedForOpenSearch', true);
      }
    });
  }

  // View type controls
  const viewTypeControls = document.getElementById('viewTypeControls');

  if (viewTypeControls) {
    // Get all view type options
    const viewTypeOptions = viewTypeControls.querySelectorAll('[data-view-type]');

    // Add click event to each option
    viewTypeOptions.forEach(option => {
      option.addEventListener('click', () => {
        const viewType = option.getAttribute('data-view-type');

        // Update state and UI
        stateManager.setViewType(viewType);

        // Update UI based on view type
        if (viewType === 'flows') {
          uiManager.updateDisplayedEntries();
        } else if (viewType === 'calls') {
          uiManager.showCallSequenceView();
        }

        // Update message ID checkboxes to reflect the filter state for the new view
        updateMessageIdCheckboxes(viewType);

        // Update flow checkboxes to reflect the filter state for the new view
        updateFlowCheckboxes(viewType);

        // Update status code checkboxes to reflect the filter state for the new view
        updateStatusCodeCheckboxes(viewType);

        // Hide dropdown
        viewTypeControls.classList.remove('show');
      });
    });

    // Toggle dropdown on button click
    const viewTypeButton = document.getElementById('viewTypeButton');
    if (viewTypeButton) {
      viewTypeButton.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        // Use uiManager.toggleDropdown to ensure all other dropdowns are closed
        uiManager.toggleDropdown(viewTypeControls);
      });
    }
  }

  // Filter buttons
  const messageIdButton = document.getElementById('messageIdButton');
  const statusCodeButton = document.getElementById('statusCodeButton');
  const flowButton = document.getElementById('flowButton');

  if (messageIdButton) {
    messageIdButton.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      const dropdown = document.getElementById('messageIdControls');
      if (dropdown) {
        uiManager.toggleDropdown(dropdown);
      }
    });

    // Prevent dropdown from closing when clicking inside it
    const messageIdControls = document.getElementById('messageIdControls');
    if (messageIdControls) {
      messageIdControls.addEventListener('click', (e) => {
        e.stopPropagation();
      });
    }

    // Set up message ID filter checkboxes
    setupMessageIdFilterCheckboxes();
  }

  if (statusCodeButton) {
    statusCodeButton.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      const dropdown = document.getElementById('statusCodeControls');
      if (dropdown) {
        uiManager.toggleDropdown(dropdown);
      }
    });

    // Prevent dropdown from closing when clicking inside it
    const statusCodeControls = document.getElementById('statusCodeControls');
    if (statusCodeControls) {
      statusCodeControls.addEventListener('click', (e) => {
        e.stopPropagation();
      });
    }

    // Set up status code filter checkboxes
    setupStatusCodeFilterCheckboxes();
  }

  if (flowButton) {
    flowButton.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      const dropdown = document.getElementById('flowControls');
      if (dropdown) {
        uiManager.toggleDropdown(dropdown);
      }
    });

    // Prevent dropdown from closing when clicking inside it
    const flowControls = document.getElementById('flowControls');
    if (flowControls) {
      flowControls.addEventListener('click', (e) => {
        e.stopPropagation();
      });
    }

    // Set up flow filter checkboxes
    setupFlowFilterCheckboxes();
  }

  // File input change
  const fileInput = document.getElementById('fileInput');
  if (fileInput) {
    fileInput.addEventListener('change', () => {
      if (fileInput.files.length > 0) {
        const fileName = fileInput.files[0].name;
        const fileLabel = document.getElementById('fileLabel');
        if (fileLabel) {
          fileLabel.textContent = fileName;
        }

        // Clear the text input when a file is selected
        const textInput = document.getElementById('textInput');
        if (textInput) {
          textInput.value = '';
        }
      }
    });
  }

  // Text input change
  const textInput = document.getElementById('textInput');
  if (textInput) {
    textInput.addEventListener('input', () => {
      if (textInput.value.trim()) {
        // Clear the file input when text is entered
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
          fileInput.value = '';
          const fileLabel = document.getElementById('fileLabel');
          if (fileLabel) {
            fileLabel.textContent = 'Choose file';
          }
        }
      }
    });
  }

  // Add keyboard navigation for pagination using Alt+Arrow keys
  document.addEventListener('keydown', (e) => {
    // Only handle pagination navigation when not in an input field or offcanvas
    const isInInput = ['INPUT', 'TEXTAREA', 'SELECT'].includes(document.activeElement.tagName);
    const isInOffcanvas = document.querySelector('.offcanvas.show') !== null;

    if (isInInput || isInOffcanvas) {
      return; // Don't interfere with input fields or offcanvas navigation
    }

    const { pagination } = stateManager.getState();
    const { currentPage } = pagination;
    const totalPages = stateManager.getTotalPages();
    const currentViewType = stateManager.getState().currentViewType;

    // Use Alt key as modifier to avoid conflicts with browser shortcuts
    if (e.altKey) {
      switch (e.key) {
        case 'ArrowLeft':
          // Previous page (Alt + Left Arrow)
          if (currentPage > 1) {
            e.preventDefault(); // Prevent browser back navigation
            stateManager.setPagination({ currentPage: currentPage - 1 });
            if (currentViewType === 'calls') {
              uiManager.showCallSequenceView();
            } else {
              uiManager.updateDisplayedEntries();
            }
          }
          break;

        case 'ArrowRight':
          // Next page (Alt + Right Arrow)
          if (currentPage < totalPages) {
            e.preventDefault();
            stateManager.setPagination({ currentPage: currentPage + 1 });
            if (currentViewType === 'calls') {
              uiManager.showCallSequenceView();
            } else {
              uiManager.updateDisplayedEntries();
            }
          }
          break;

        case 'Home':
          // First page (Alt + Home)
          if (currentPage > 1) {
            e.preventDefault();
            stateManager.setPagination({ currentPage: 1 });
            if (currentViewType === 'calls') {
              uiManager.showCallSequenceView();
            } else {
              uiManager.updateDisplayedEntries();
            }
          }
          break;

        case 'End':
          // Last page (Alt + End)
          if (currentPage < totalPages) {
            e.preventDefault();
            stateManager.setPagination({ currentPage: totalPages });
            if (currentViewType === 'calls') {
              uiManager.showCallSequenceView();
            } else {
              uiManager.updateDisplayedEntries();
            }
          }
          break;
      }
    }
  });
}

/**
 * Format JSON for display
 * @param {Object} obj - The object to format as JSON
 * @returns {string} - The formatted JSON HTML
 */
function formatJSON(obj) {
  try {
    if (!obj) return '';

    // Store original client_id for unmasking
    const originalClientId = obj.client_id;

    // Create a copy of the object to avoid modifying the original
    const objCopy = { ...obj };

    // Replace dash placeholders with empty strings for all properties
    Object.keys(objCopy).forEach(key => {
      if (objCopy[key] === '-') {
        objCopy[key] = '';
      }
    });

    // Apply masking to client_id if needed
    const isMasked = stateManager.getState().maskSensitiveData;
    if (isMasked && objCopy.client_id && objCopy.client_id !== '') {
      objCopy.client_id = "*** masked ***";
    }

    // Convert the object to a single-line JSON string
    const jsonString = JSON.stringify(objCopy);

    // Format the JSON string with commas for better readability but no spaces after colons
    let formattedJson = jsonString
      .replace(/,/g, ', ')
      .replace(/^\{|\}$/g, ''); // Remove outer braces

    // Process the JSON string to add syntax highlighting and make values clickable
    let processedJson = formattedJson;

    // Special handling for client_id to support masking/unmasking
    if (originalClientId && originalClientId !== '-') {
      const clientIdRegex = /"client_id":\s*"([^"]*)"/;
      const clientIdMatch = processedJson.match(clientIdRegex);
      if (clientIdMatch) {
        processedJson = processedJson.replace(
          clientIdRegex,
          `"client_id":"<span class="json-client-id" data-original="${originalClientId}">${clientIdMatch[1]}</span>"`
        );
      }
    }

    // Make specific values clickable for copying
    ['messageid', 'requestid', 'uri'].forEach(field => {
      const fieldRegex = new RegExp(`"${field}":\\s*"([^"]*)"`, 'g');
      let match;

      // We need to use exec in a loop to find all matches since we're using a regex with the 'g' flag
      // This ensures we process all occurrences of each field
      while ((match = fieldRegex.exec(processedJson)) !== null) {
        if (match[1] && match[1] !== '') {
          // Store the original value for highlighting later
          const originalValue = match[1];

          // Check if we need to highlight this value based on search terms
          let displayValue = originalValue;
          const { currentViewType, filterState } = stateManager.getState();
          const currentFilters = filterState[currentViewType];

          if (currentFilters.searchTerm) {
            const searchTerm = currentFilters.searchTerm.toLowerCase();

            // Parse search terms with AND/OR operators
            function parseSearchTerms(searchTerm) {
              const hasAnd = searchTerm.includes(' and ');
              const hasOr = searchTerm.includes(' or ');

              if (hasAnd && !hasOr) {
                // AND search
                return { type: 'AND', terms: searchTerm.split(' and ').map(term => term.trim()).filter(term => term) };
              } else if (hasOr && !hasAnd) {
                // OR search
                return { type: 'OR', terms: searchTerm.split(' or ').map(term => term.trim()).filter(term => term) };
              } else if (hasAnd && hasOr) {
                // Complex search with both AND and OR - treat as simple search for now
                return { type: 'SIMPLE', terms: [searchTerm] };
              } else {
                // Simple search
                return { type: 'SIMPLE', terms: [searchTerm] };
              }
            }

            const parsedTerms = parseSearchTerms(searchTerm);

            // Check if this field matches any of the search terms
            let shouldHighlight = false;

            if (parsedTerms.type === 'AND') {
              shouldHighlight = parsedTerms.terms.every(term =>
                originalValue.toLowerCase().includes(term.toLowerCase())
              );
            } else if (parsedTerms.type === 'OR') {
              shouldHighlight = parsedTerms.terms.some(term =>
                originalValue.toLowerCase().includes(term.toLowerCase())
              );
            } else {
              shouldHighlight = originalValue.toLowerCase().includes(parsedTerms.terms[0].toLowerCase());
            }

            if (shouldHighlight) {
              // Apply highlighting to each term
              parsedTerms.terms.forEach(term => {
                if (!term) return;

                // Create a regex that matches the term
                const regex = new RegExp(`(${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');

                // Replace with highlighted version
                displayValue = displayValue.replace(regex, '<span class="search-highlight">$1</span>');
              });
            }
          }

          // Create the replacement with the clickable field
          const replacement = `"${field}":"<span class="copyable-field" title="Click to copy" onclick="copyFieldValue('${originalValue.replace(/'/g, "\\'")}')" data-original-value="${originalValue}">${displayValue}</span>"`;

          // Replace only this specific match
          const fullMatch = match[0];
          processedJson = processedJson.replace(fullMatch, replacement);

          // Reset the regex index to continue searching from the end of the replacement
          fieldRegex.lastIndex = fieldRegex.lastIndex + (replacement.length - fullMatch.length);
        }
      }
    });

    // Bold all keys after handling the special fields
    processedJson = processedJson.replace(/"([^"]+)":/g, '"<span class="json-key">$1</span>":');

    // Highlight search terms in the JSON values
    const { currentViewType, filterState } = stateManager.getState();
    const currentFilters = filterState[currentViewType];

    if (currentFilters.searchTerm) {
      const searchTerm = currentFilters.searchTerm.toLowerCase();

      // Parse search terms with AND/OR operators using the same function as in state.js
      function parseSearchTerms(searchTerm) {
        const hasAnd = searchTerm.includes(' and ');
        const hasOr = searchTerm.includes(' or ');

        if (hasAnd && !hasOr) {
          // AND search
          return searchTerm.split(' and ').map(term => term.trim()).filter(term => term);
        } else if (hasOr && !hasAnd) {
          // OR search
          return searchTerm.split(' or ').map(term => term.trim()).filter(term => term);
        } else if (hasAnd && hasOr) {
          // Complex search with both AND and OR - treat as simple search for now
          return [searchTerm];
        } else {
          // Simple search
          return [searchTerm];
        }
      }

      const terms = parseSearchTerms(searchTerm);

      // Now apply general highlighting to the rest of the content, but skip already highlighted content
      terms.forEach(term => {
        if (!term) return;

        // Create a regex that matches the term but not inside HTML tags or attributes
        const regex = new RegExp(`(${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})(?![^<]*>|[^<>]*<\/)`, 'gi');

        // Replace with highlighted version
        processedJson = processedJson.replace(regex, '<span class="search-highlight">$1</span>');
      });
    }

    return processedJson;
  } catch (error) {
    console.error('Error formatting JSON:', error);
    if (typeof obj === 'string') return obj;
    return JSON.stringify(obj);
  }
}

/**
 * Format headers for display
 * @param {string} headers - The headers string to format
 * @param {Object} options - Options for formatting
 * @returns {string} - The formatted headers HTML
 */
function formatHeaders(headers, options = {}) {
  if (!headers || headers === '-') return '-';

  try {
    // IMPORTANT: The headers should already be properly formatted with newlines
    // from the processor.js formatHTTPHeaders function

    // Split headers by newline - this is critical
    const headerLines = headers.split('\n');

    // Determine which CSS class to use based on entry format
    // For new formats (csv_with_path, simple_message, raw_json), use http-header-name
    // For old format, use header-key to maintain original styling
    const isNewFormat = options.entry && options.entry.format &&
                       (options.entry.format === 'csv_with_path' ||
                        options.entry.format === 'simple_message' ||
                        options.entry.format === 'raw_json');

    const headerKeyClass = isNewFormat ? "http-header-name" : "header-key";

    // Process each header line individually
    const processedLines = headerLines.map(line => {
      if (!line.trim()) return '';

      // Try to split by colon for header name and value
      const colonIndex = line.indexOf(':');
      if (colonIndex > 0) {
        const name = line.substring(0, colonIndex).trim();
        const value = line.substring(colonIndex + 1).trim();

        // Store the original value for unmasking
        const originalValue = value;

        // Apply masking using the centralized utility
        const nameLower = name.toLowerCase();
        const displayValue = maskSensitiveData(originalValue, nameLower);

        // Special handling for X-Error-Source header
        if (nameLower === 'x-error-source') {
          // Highlight search terms in the header value
          const highlightedValue = highlightSearchTerms(displayValue);
          return `<span class="${headerKeyClass} x-error-source">${name}</span>: <span class="http-header-value x-error-source" data-original="${originalValue}">${highlightedValue}</span>`;
        }

        // Add data attributes to store the original value for unmasking
        // Highlight search terms in the header value
        const highlightedValue = highlightSearchTerms(displayValue);
        return `<span class="${headerKeyClass}">${name}</span>: <span class="http-header-value" data-original="${originalValue}">${highlightedValue}</span>`;
      }

      return line;
    });

    // Join with <br> tags to ensure proper line breaks in HTML
    return processedLines.filter(line => line).join('<br>');
  } catch (error) {
    console.error('Error formatting headers:', error);
    return headers;
  }
}

/**
 * Set up offcanvas event listeners
 */
function setupOffcanvasEventListeners() {
  // Copy log button
  const copyLogBtn = document.getElementById('copyLogBtn');
  if (copyLogBtn) {
    copyLogBtn.addEventListener('click', () => {
      const offcanvasContent = document.getElementById('offcanvasEntryContent');
      if (offcanvasContent) {
        // Handle differently based on view type
        const rowIndex = offcanvasContent.getAttribute('data-row-index');
        const callIndex = offcanvasContent.getAttribute('data-call-index');

        if (callIndex !== null) {
          // For calls view
          const visibleEntries = stateManager.getVisibleEntries();
          const calls = logProcessor.aggregateEntriesByCall(visibleEntries);
          const call = calls[parseInt(callIndex, 10)];

          if (call) {
            copyAllFlowDetails(call.originalEntries);
          }
        } else if (rowIndex !== null) {
          // For flows view
          const visibleEntries = stateManager.getVisibleEntries();
          const entry = visibleEntries[parseInt(rowIndex, 10)];

          if (entry) {
            copyLogDetails(entry);
          }
        }
      }
    });
  }

  // Export log button
  const exportLogBtn = document.getElementById('exportLogBtn');
  if (exportLogBtn) {
    exportLogBtn.addEventListener('click', () => {
      const offcanvasContent = document.getElementById('offcanvasEntryContent');
      if (offcanvasContent) {
        // Handle differently based on view type
        const rowIndex = offcanvasContent.getAttribute('data-row-index');
        const callIndex = offcanvasContent.getAttribute('data-call-index');

        if (callIndex !== null) {
          // For calls view
          const visibleEntries = stateManager.getVisibleEntries();
          const calls = logProcessor.aggregateEntriesByCall(visibleEntries);
          const call = calls[parseInt(callIndex, 10)];

          if (call) {
            exportManager.exportLogDetailsToFile(call.originalEntries);
          }
        } else if (rowIndex !== null) {
          // For flows view
          const visibleEntries = stateManager.getVisibleEntries();
          const entry = visibleEntries[parseInt(rowIndex, 10)];

          if (entry) {
            exportManager.exportLogDetailsToFile(entry);
          }
        }
      }
    });
  }

  // Toggle mask button
  const toggleMaskBtn = document.getElementById('toggleMaskBtn');
  if (toggleMaskBtn) {
    // Set initial state based on stateManager
    const initialMasked = stateManager.getState().maskSensitiveData;

    // Initialize button state based on the current masking state
    if (initialMasked) {
      // If masked, button should show "eye-slash" and have title "Show sensitive data"
      toggleMaskBtn.classList.remove('active');
      const icon = toggleMaskBtn.querySelector('i');
      if (icon) {
        icon.classList.remove('bi-eye');
        icon.classList.add('bi-eye-slash');
      }
      toggleMaskBtn.setAttribute('title', 'Show sensitive data');
    } else {
      // If not masked, button should show "eye" and have title "Hide sensitive data"
      toggleMaskBtn.classList.add('active');
      const icon = toggleMaskBtn.querySelector('i');
      if (icon) {
        icon.classList.remove('bi-eye-slash');
        icon.classList.add('bi-eye');
      }
      toggleMaskBtn.setAttribute('title', 'Hide sensitive data');
    }

    toggleMaskBtn.addEventListener('click', () => {
      // Toggle the active class
      toggleMaskBtn.classList.toggle('active');

      // Update the icon
      const icon = toggleMaskBtn.querySelector('i');
      if (icon) {
        if (toggleMaskBtn.classList.contains('active')) {
          icon.classList.remove('bi-eye-slash');
          icon.classList.add('bi-eye');
          toggleMaskBtn.setAttribute('title', 'Hide sensitive data');
        } else {
          icon.classList.remove('bi-eye');
          icon.classList.add('bi-eye-slash');
          toggleMaskBtn.setAttribute('title', 'Show sensitive data');
        }
      }

      // Toggle masked content
      const isMasked = !toggleMaskBtn.classList.contains('active');

      // Update the state
      stateManager.updateState('maskSensitiveData', isMasked);

      // Try to directly update the header values and client_id without re-rendering
      const headerValues = document.querySelectorAll('.http-header-value');
      const clientIdValues = document.querySelectorAll('.json-client-id');
      let directUpdateSuccessful = false;

      // Process header values
      if (headerValues.length > 0 || clientIdValues.length > 0) {
        directUpdateSuccessful = true;

        // Update HTTP headers
        headerValues.forEach((headerValue, index) => {
          const originalValue = headerValue.getAttribute('data-original');

          if (originalValue) {
            if (isMasked) {
              // Masking - check if we need to mask this value
              const parentElement = headerValue.previousElementSibling;

              // Debug first few elements to see what's happening
              if (index < 3) {
                console.log(`Header value ${index}:`, headerValue.textContent);
                console.log(`  Original value:`, originalValue);
                console.log(`  Parent element:`, parentElement);
                console.log(`  Parent classes:`, parentElement ? parentElement.className : 'No parent');
              }

              // Check for both new format (http-header-name) and old format (header-key) classes
              if (parentElement && (parentElement.classList.contains('http-header-name') || parentElement.classList.contains('header-key'))) {
                const headerName = parentElement.textContent.replace(':', '').trim().toLowerCase();

                // Apply masking logic using the centralized utility
                const maskedValue = maskSensitiveData(originalValue, headerName);

                // Apply highlighting to the masked value
                headerValue.innerHTML = highlightSearchTerms(maskedValue);

                // Debug masking for sensitive headers
                if (headerName === 'authorization' || headerName === 'client_id') {
                  console.log(`Masked ${headerName}:`, headerValue.textContent);
                }
              }
            } else {
              // Unmasking - restore original value with highlighting
              headerValue.innerHTML = highlightSearchTerms(originalValue);

              // Debug unmasking for first few elements
              if (index < 3) {
                console.log(`Unmasked header ${index}:`, headerValue.textContent);
              }
            }
          }
        });

        // Update client_id values
        clientIdValues.forEach(clientIdValue => {
          const originalValue = clientIdValue.getAttribute('data-original');

          if (originalValue) {
            if (isMasked) {
              // Masking - replace with masked value
              clientIdValue.innerHTML = highlightSearchTerms('*** masked ***');
            } else {
              // Unmasking - restore original value with highlighting
              clientIdValue.innerHTML = highlightSearchTerms(originalValue);
            }
          }
        });
      }

      // If direct update failed or there were no header values, fall back to re-rendering
      if (!directUpdateSuccessful) {
        // Force a re-render of the offcanvas content
        const offcanvasContent = document.getElementById('offcanvasEntryContent');
        if (offcanvasContent) {
          // Get the current row or call index
          const rowIndex = offcanvasContent.getAttribute('data-row-index');
          const callIndex = offcanvasContent.getAttribute('data-call-index');

          if (rowIndex !== null) {
            // Re-render the entry with masking toggled
            window.toggleRow(parseInt(rowIndex, 10));
          } else if (callIndex !== null) {
            // Re-render the call with masking toggled
            window.showCallDetails(parseInt(callIndex, 10));
          }
        }
      }

      // Log the current state for debugging
      console.log('Mask state updated:', isMasked);

      // Debug logging for header elements
      const headerKeyElements = document.querySelectorAll('.header-key');
      const httpHeaderNameElements = document.querySelectorAll('.http-header-name');
      console.log('Header elements found - Old format (.header-key):', headerKeyElements.length);
      console.log('Header elements found - New format (.http-header-name):', httpHeaderNameElements.length);
    });
  }

  // Previous log button
  const prevLogBtn = document.getElementById('prevLogBtn');
  if (prevLogBtn) {
    prevLogBtn.addEventListener('click', () => {
      const offcanvasContent = document.getElementById('offcanvasEntryContent');
      if (offcanvasContent) {
        const rowIndex = parseInt(offcanvasContent.getAttribute('data-row-index'), 10);
        const callIndex = parseInt(offcanvasContent.getAttribute('data-call-index'), 10);
        const { pagination } = stateManager.getState();
        const { pageSize, currentPage } = pagination;

        if (!isNaN(rowIndex) && rowIndex > 0) {
          // Calculate which page the previous entry is on
          const targetPage = Math.floor((rowIndex - 1) / pageSize) + 1;

          // If it's on a different page, update pagination first
          if (targetPage !== currentPage) {
            stateManager.setPagination({ currentPage: targetPage });
            uiManager.updateDisplayedEntries();
          }

          window.toggleRow(rowIndex - 1);
        } else if (!isNaN(callIndex) && callIndex > 0) {
          // Calculate which page the previous call is on
          const targetPage = Math.floor((callIndex - 1) / pageSize) + 1;

          // If it's on a different page, update pagination first
          if (targetPage !== currentPage) {
            stateManager.setPagination({ currentPage: targetPage });
            uiManager.showCallSequenceView();
          }

          window.showCallDetails(callIndex - 1);
        }
      }
    });
  }

  // Next log button
  const nextLogBtn = document.getElementById('nextLogBtn');
  if (nextLogBtn) {
    nextLogBtn.addEventListener('click', () => {
      const offcanvasContent = document.getElementById('offcanvasEntryContent');
      if (offcanvasContent) {
        const rowIndex = parseInt(offcanvasContent.getAttribute('data-row-index'), 10);
        const callIndex = parseInt(offcanvasContent.getAttribute('data-call-index'), 10);
        const { pagination } = stateManager.getState();
        const { pageSize, currentPage } = pagination;

        const visibleEntries = stateManager.getVisibleEntries();
        const calls = logProcessor.aggregateEntriesByCall(visibleEntries);

        if (!isNaN(rowIndex) && rowIndex < visibleEntries.length - 1) {
          // Calculate which page the next entry is on
          const nextRowIndex = rowIndex + 1;
          const targetPage = Math.floor(nextRowIndex / pageSize) + 1;

          // If it's on a different page, update pagination first
          if (targetPage !== currentPage) {
            stateManager.setPagination({ currentPage: targetPage });
            uiManager.updateDisplayedEntries();
          }

          window.toggleRow(nextRowIndex);
        } else if (!isNaN(callIndex) && callIndex < calls.length - 1) {
          // Calculate which page the next call is on
          const nextCallIndex = callIndex + 1;
          const targetPage = Math.floor(nextCallIndex / pageSize) + 1;

          // If it's on a different page, update pagination first
          if (targetPage !== currentPage) {
            stateManager.setPagination({ currentPage: targetPage });
            uiManager.showCallSequenceView();
          }

          window.showCallDetails(nextCallIndex);
        }
      }
    });
  }

  // Update navigation counter when offcanvas is shown
  const offcanvas = document.getElementById('entryDetailsOffcanvas');
  if (offcanvas) {
    offcanvas.addEventListener('shown.bs.offcanvas', () => {
      updateNavigationCounter();
    });
  }
}

/**
 * Update the navigation counter in the offcanvas
 */
function updateNavigationCounter() {
  const offcanvasContent = document.getElementById('offcanvasEntryContent');
  const logNavigationCounter = document.getElementById('logNavigationCounter');

  if (offcanvasContent && logNavigationCounter) {
    const rowIndex = parseInt(offcanvasContent.getAttribute('data-row-index'), 10);
    const callIndex = parseInt(offcanvasContent.getAttribute('data-call-index'), 10);

    const visibleEntries = stateManager.getVisibleEntries();
    const calls = logProcessor.aggregateEntriesByCall(visibleEntries);

    if (!isNaN(rowIndex)) {
      // For flows view
      logNavigationCounter.textContent = `Entry ${rowIndex + 1} of ${visibleEntries.length}`;

      // Update prev/next button states
      const prevLogBtn = document.getElementById('prevLogBtn');
      const nextLogBtn = document.getElementById('nextLogBtn');

      if (prevLogBtn) {
        prevLogBtn.disabled = rowIndex <= 0;
      }

      if (nextLogBtn) {
        nextLogBtn.disabled = rowIndex >= visibleEntries.length - 1;
      }
    } else if (!isNaN(callIndex)) {
      // For calls view
      logNavigationCounter.textContent = `Call ${callIndex + 1} of ${calls.length}`;

      // Update prev/next button states
      const prevLogBtn = document.getElementById('prevLogBtn');
      const nextLogBtn = document.getElementById('nextLogBtn');

      if (prevLogBtn) {
        prevLogBtn.disabled = callIndex <= 0;
      }

      if (nextLogBtn) {
        nextLogBtn.disabled = callIndex >= calls.length - 1;
      }
    }
  }
}

/**
 * Copy log details to clipboard in a formatted way
 * @param {Object} entry - The entry to copy
 */
function copyLogDetails(entry) {
  try {
    // Create a JSON object with the entry data
    const jsonData = {
      time: entry.time ? entry.time.includes('+') ? entry.time : `${entry.time}+0000` : '',
      level: entry.level || '',
      messageid: entry.messageId || '',
      flow: entry.flow || '',
      flowstage: entry.flowstage || '',
      requestid: entry.requestId || entry.messageId || '', // Use actual requestId if available
      correlationid: entry.correlationId || "",
      uri: entry.uri || '',
      verb: entry.verb || '',
      client_id: entry.client_id || '',
      app_name: entry.appName || '',
      statuscode: entry.statusCode || ''
    };

    // Replace dash placeholders with empty strings
    Object.keys(jsonData).forEach(key => {
      if (jsonData[key] === '-') {
        jsonData[key] = '';
      }
    });

    // Apply masking to client_id if needed
    const isMasked = stateManager.getState().maskSensitiveData;
    if (isMasked && jsonData.client_id && jsonData.client_id !== '') {
      jsonData.client_id = "*** masked ***";
    }

    // Get other fields
    let queryString = entry.queryString || '';
    let headers = entry.headers || '';
    let messageBody = entry.messageBody || '';

    // Apply masking to headers and message body if needed
    if (isMasked) {
      headers = maskTokens(headers);
      if (messageBody) {
        messageBody = maskTokens(messageBody);
      }
    }

    // Format the JSON string properly with commas but no spaces after colons
    const formattedJson = JSON.stringify(jsonData)
      .replace(/,/g, ', ')
      .replace(/"([^"]+)":/g, '"$1":');

    // Create flow header for the entry
    let spacing;
    switch (entry.flow) {
      case 'TARGET_RESP_FLOW':
        spacing = ['           ', '             '];
        break;
      case 'PROXY_RESP_FLOW':
      case 'TARGET_REQ_FLOW':
        spacing = ['            ', '             '];
        break;
      case 'PROXY_REQ_FLOW':
        spacing = ['             ', '             '];
        break;
      default:
        spacing = ['            ', '             '];
    }

    // Generate the flow header
    const stars = '*'.repeat(60);
    const headerContent = `${stars}\n${'*'.repeat(10)}${spacing[0]}${entry.flow}${spacing[1]}${'*'.repeat(10)}\n${stars}\n\n`;

    // Build the log details with the requested format
    const logDetails = `${headerContent}${formattedJson}

---------- Query string:
${queryString}

---------- HTTPHeaders:
${headers}

---------- Message body:
${messageBody}`;

    // Copy to clipboard
    navigator.clipboard.writeText(logDetails)
      .then(() => {
        // Show visual feedback only (no toast)
        const copyLogBtn = document.getElementById('copyLogBtn');
        if (copyLogBtn) {
          const originalIcon = copyLogBtn.innerHTML;
          copyLogBtn.innerHTML = '<i class="bi bi-check-lg"></i>';
          copyLogBtn.style.color = '#198754';
          setTimeout(() => {
            copyLogBtn.innerHTML = originalIcon;
            copyLogBtn.style.color = '';
          }, 1000);
        }
      })
      .catch(err => {
        console.error('Could not copy text: ', err);
        uiManager.showToast('Failed to copy to clipboard', true);
      });
  } catch (error) {
    console.error('Error copying log details:', error);
    uiManager.showToast('Error copying log details', true);
  }
}

/**
 * Copy all flow details to clipboard
 * @param {Array} entries - The entries to copy
 */
function copyAllFlowDetails(entries) {
  try {
    // Sort entries by flow order
    const flowOrder = ['PROXY_REQ_FLOW', 'TARGET_REQ_FLOW', 'TARGET_RESP_FLOW', 'PROXY_RESP_FLOW'];
    const sortedEntries = [...entries].sort((a, b) => flowOrder.indexOf(a.flow) - flowOrder.indexOf(b.flow));

    // Build the complete log content
    let completeLog = '';

    sortedEntries.forEach((entry, index) => {
      // Create a JSON object with the entry data
      const jsonData = {
        time: entry.time ? entry.time.includes('+') ? entry.time : `${entry.time}+0000` : '',
        level: entry.level || '',
        messageid: entry.messageId || '',
        flow: entry.flow || '',
        flowstage: entry.flowstage || '',
        requestid: entry.requestId || entry.messageId || '',
        correlationid: entry.correlationId || "",
        uri: entry.uri || '',
        verb: entry.verb || '',
        client_id: entry.client_id || '',
        app_name: entry.appName || '',
        statuscode: entry.statusCode || ''
      };

      // Replace dash placeholders with empty strings
      Object.keys(jsonData).forEach(key => {
        if (jsonData[key] === '-') {
          jsonData[key] = '';
        }
      });

      // Apply masking to client_id if needed
      const isMasked = stateManager.getState().maskSensitiveData;
      if (isMasked && jsonData.client_id && jsonData.client_id !== '') {
        jsonData.client_id = "*** masked ***";
      }

      // Format the JSON string properly with commas but no spaces after colons
      const formattedJson = JSON.stringify(jsonData)
        .replace(/,/g, ', ')
        .replace(/"([^"]+)":/g, '"$1":');

      // Create flow header for the entry
      let spacing;
      switch (entry.flow) {
        case 'TARGET_RESP_FLOW':
          spacing = ['           ', '             '];
          break;
        case 'PROXY_RESP_FLOW':
        case 'TARGET_REQ_FLOW':
          spacing = ['            ', '             '];
          break;
        case 'PROXY_REQ_FLOW':
          spacing = ['             ', '             '];
          break;
        default:
          spacing = ['            ', '             '];
      }

      // Generate the flow header
      const stars = '*'.repeat(60);
      const headerContent = `${index > 0 ? '\n' : ''}${stars}\n${'*'.repeat(10)}${spacing[0]}${entry.flow}${spacing[1]}${'*'.repeat(10)}\n${stars}\n\n`;

      // Get and process query string, headers and message body
      let queryString = entry.queryString || '';
      let headers = entry.headers || '';
      let messageBody = entry.messageBody || '';

      // Apply masking if needed
      if (isMasked) {
        headers = maskTokens(headers);
        if (messageBody) {
          messageBody = maskTokens(messageBody);
        }
      }

      // Build the log details
      completeLog += `${headerContent}${formattedJson}\n\n---------- Query string:\n${queryString}\n\n---------- HTTPHeaders:\n${headers}\n\n---------- Message body:\n${messageBody}\n`;
    });

    // Copy to clipboard
    navigator.clipboard.writeText(completeLog)
      .then(() => {
        // Show visual feedback only (no toast)
        const copyLogBtn = document.getElementById('copyLogBtn');
        if (copyLogBtn) {
          const originalIcon = copyLogBtn.innerHTML;
          copyLogBtn.innerHTML = '<i class="bi bi-check-lg"></i>';
          copyLogBtn.style.color = '#198754';
          setTimeout(() => {
            copyLogBtn.innerHTML = originalIcon;
            copyLogBtn.style.color = '';
          }, 1000);
        }
      })
      .catch(err => {
        console.error('Could not copy text: ', err);
        uiManager.showToast('Failed to copy to clipboard', true);
      });
  } catch (error) {
    console.error('Error copying flow details:', error);
    uiManager.showToast('Error copying flow details', true);
  }
}

/**
 * Mask sensitive tokens in text
 * @param {string} content - The content to mask
 * @returns {string} - The masked content
 */
function maskTokens(content) {
  // Use the centralized maskContent utility
  return maskContent(content);
}

/**
 * Format query string for display
 * @param {string} queryString - The query string to format
 * @returns {string} - The formatted query string HTML
 */
function formatQueryString(queryString) {
  if (!queryString || queryString === '-') return '';

  // Highlight search terms
  return highlightSearchTerms(queryString);
}

/**
 * Format message body for display
 * @param {string} messageBody - The message body to format
 * @returns {string} - The formatted message body for display
 */
function formatMessageBody(messageBody) {
  if (!messageBody || messageBody === '-') return '';

  try {
    // Apply masking if needed
    let processedBody = stateManager.getState().maskSensitiveData ?
      maskContent(messageBody) : messageBody;

    // Escape XML content for HTML display
    if (processedBody.trim().startsWith('<?xml') ||
        processedBody.trim().startsWith('<soapenv:Envelope') ||
        processedBody.includes('<') && processedBody.includes('</')) {
      // Escape XML for HTML display
      processedBody = processedBody
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
    }

    // Highlight search terms
    return highlightSearchTerms(processedBody);
  } catch (error) {
    console.error('Error formatting message body:', error);
    return messageBody;
  }
}

/**
 * Clear search highlights from the offcanvas
 */
function clearSearchHighlights() {
  // Get all highlighted elements
  const highlightedElements = document.querySelectorAll('.search-highlight');

  // Replace each highlighted element with its text content
  highlightedElements.forEach(element => {
    const textNode = document.createTextNode(element.textContent);
    element.parentNode.replaceChild(textNode, element);
  });
}

/**
 * Expose necessary functions to window for HTML event handlers
 */
function exposeGlobalFunctions() {
  // Toggle row details
  window.toggleRow = (rowIndex) => {
    const visibleEntries = stateManager.getVisibleEntries();
    const entry = visibleEntries[rowIndex];

    if (!entry) return;

    // Update offcanvas content
    const offcanvasContent = document.getElementById('offcanvasEntryContent');

    if (offcanvasContent) {
      // Update header info
      document.getElementById('offcanvas-message-id').textContent = entry.messageId || '-';
      document.getElementById('offcanvas-flow').textContent = entry.flow || '-';
      document.getElementById('offcanvas-env-org').textContent = entry.env || '-';

      // Update status code with inline styling to match table
      const statusCodeElement = document.getElementById('offcanvas-status-code');
      statusCodeElement.textContent = entry.statusCode || '-';
      statusCodeElement.classList.remove('code-1xx', 'code-2xx', 'code-3xx', 'code-4xx', 'code-5xx');
      statusCodeElement.removeAttribute('style');

      if (entry.statusCode && entry.statusCode !== '-') {
        const firstDigit = entry.statusCode.charAt(0);
        const statusCodeStyles = logProcessor.statusCodeStyles;
        const badgeStyle = statusCodeStyles[firstDigit] || '';
        statusCodeElement.style = badgeStyle;
      }

      // Format entry properties for JSON display
      const entryProps = {
        time: entry.time || '',
        level: entry.level || '',
        messageid: entry.messageId || '',
        flow: entry.flow || '',
        flowstage: entry.flowstage || '',
        requestid: entry.requestId || '',
        correlationid: entry.correlationId || '',
        uri: entry.uri || '',
        verb: entry.verb || '',
        client_id: entry.client_id || '',
        app_name: entry.appName || '',
        statuscode: entry.statusCode || ''
      };

      // Format JSON for display
      const jsonDisplay = formatJSON(entryProps);

      // Process query string
      const queryString = entry.queryString || '-';
      const processedQueryString = formatQueryString(queryString);

      // Process headers
      const headers = entry.headers || '-';
      const processedHeadersHTML = formatHeaders(headers, { entry });

      // Process message body
      const messageBody = entry.messageBody || '-';
      // Format the message body for display using the formatMessageBody function
      const processedMessageBody = formatMessageBody(messageBody);

      // For new structure formats, colorize the headers
      let displayHeaders = entry.format &&
                          (entry.format === 'csv_with_path' ||
                           entry.format === 'simple_message' ||
                           entry.format === 'raw_json')
                          ? logProcessor.colorizeHeaderKeys(processedHeadersHTML, { format: entry.format })
                          : processedHeadersHTML;

      // Create HTML content
      const detailsHTML = `<div class="offcanvas-entry-details">
        <pre><code>${jsonDisplay}</code></pre>
        <p>---------- Query string:</p>
        <div class="indent mono wrap-content">${processedQueryString}</div>
        <p>---------- HTTPHeaders:</p>
        <div class="indent mono wrap-content">${displayHeaders}</div>
        <div style="display: flex; justify-content: space-between; align-items: center;">
          <p>---------- Message body:</p>
          <button type="button" class="offcanvas-copy-btn" id="copyMessageBodyBtn" title="Copy message body">
            <i class="bi bi-copy"></i>
          </button>
        </div>
        <div class="indent mono" id="messageBodyContent" style="white-space: pre-wrap; word-break: break-word; max-width: 100%; line-height: 1.5; max-height: 400px; overflow-y: auto; scrollbar-width: none; -ms-overflow-style: none;">${processedMessageBody}</div>
      </div>`;

      // Set the content
      offcanvasContent.innerHTML = detailsHTML;

      // Add event listener for copy message body button
      const copyMessageBodyBtn = document.getElementById('copyMessageBodyBtn');
      if (copyMessageBodyBtn) {
        copyMessageBodyBtn.addEventListener('click', () => {
          const messageBodyContent = document.getElementById('messageBodyContent');
          if (messageBodyContent) {
            // Get the original entry to copy the raw message body
            const rowIndex = parseInt(offcanvasContent.getAttribute('data-row-index'), 10);
            const visibleEntries = stateManager.getVisibleEntries();

            // Get the original message body from the entry
            const originalMessageBody = !isNaN(rowIndex) && visibleEntries[rowIndex]
              ? visibleEntries[rowIndex].messageBody || ''
              : messageBodyContent.textContent;

            navigator.clipboard.writeText(originalMessageBody)
              .then(() => {
                // Show visual feedback only (no toast)
                const originalIcon = copyMessageBodyBtn.innerHTML;
                copyMessageBodyBtn.innerHTML = '<i class="bi bi-check-lg"></i>';
                copyMessageBodyBtn.style.color = '#198754';
                setTimeout(() => {
                  copyMessageBodyBtn.innerHTML = originalIcon;
                  copyMessageBodyBtn.style.color = '';
                }, 1000);
              })
              .catch(err => {
                console.error('Could not copy text: ', err);
                uiManager.showToast('Failed to copy to clipboard', true);
              });
          }
        });
      }

      // Store row index for keyboard navigation
      offcanvasContent.removeAttribute('data-call-index');
      offcanvasContent.setAttribute('data-row-index', rowIndex);

      // Update navigation counter
      updateNavigationCounter();

      // Highlight active row
      const previousActive = document.querySelector('.log-row-active');
      if (previousActive) {
        previousActive.classList.remove('log-row-active');
      }

      const activeRow = document.querySelector(`tr[data-row-index="${rowIndex}"]`);
      if (activeRow) {
        activeRow.classList.add('log-row-active');
      }

      // Show offcanvas
      const offcanvas = document.getElementById('entryDetailsOffcanvas');
      if (offcanvas) {
        const bsOffcanvas = bootstrap.Offcanvas.getInstance(offcanvas) || new bootstrap.Offcanvas(offcanvas);
        bsOffcanvas.show();
      }
    }
  };

  // Show call details
  window.showCallDetails = (callIndex) => {
    const visibleEntries = stateManager.getVisibleEntries();
    const calls = logProcessor.aggregateEntriesByCall(visibleEntries);
    const call = calls[callIndex];

    if (!call) return;

    // Update offcanvas content
    const offcanvasContent = document.getElementById('offcanvasEntryContent');

    if (offcanvasContent) {
      // Update header info
      document.getElementById('offcanvas-message-id').textContent = call.messageId || '-';

      // Display response time with colored formatting
      const flowElement = document.getElementById('offcanvas-flow');
      let responseTimeClass = '';
      if (call.responseTime && call.responseTime !== '-') {
        const responseTimeValue = parseFloat(call.responseTime);
        if (!isNaN(responseTimeValue)) {
          if (responseTimeValue < 0.5) {
            responseTimeClass = 'fast';
          } else if (responseTimeValue < 2) {
            responseTimeClass = 'medium';
          } else {
            responseTimeClass = 'slow';
          }
        }
      }
      flowElement.innerHTML = `Response time: <span class="response-time ${responseTimeClass}">${call.responseTime || '-'}</span>`;

      document.getElementById('offcanvas-env-org').textContent = call.env || '-';

      // Update status code with inline styling to match table
      const statusCodeElement = document.getElementById('offcanvas-status-code');
      statusCodeElement.textContent = call.statusCode || '-';
      statusCodeElement.classList.remove('code-1xx', 'code-2xx', 'code-3xx', 'code-4xx', 'code-5xx');
      statusCodeElement.removeAttribute('style');

      if (call.statusCode && call.statusCode !== '-') {
        const firstDigit = call.statusCode.charAt(0);
        const statusCodeStyles = logProcessor.statusCodeStyles;
        const badgeStyle = statusCodeStyles[firstDigit] || '';
        statusCodeElement.style = badgeStyle;
      }

      // Get the entries for the call and organize them by flow
      // Group entries by flow type, allowing multiple entries per flow
      const flowEntries = {};
      if (call.originalEntries) {
        call.originalEntries.forEach(entry => {
          if (!flowEntries[entry.flow]) {
            flowEntries[entry.flow] = [];
          }
          flowEntries[entry.flow].push(entry);
        });
      }

      // Create accordion for each flow type
      let detailsHTML = '<div class="offcanvas-entry-details">';

      // Generate a unique ID prefix for each flow's collapsible content
      const collapseId = 'flow-collapse-' + Date.now();

      // No parent container needed - each accordion will operate independently

      // No need to count entries since we're using individual accordions

      // Process each flow type
      const flowOrder = ['PROXY_REQ_FLOW', 'TARGET_REQ_FLOW', 'TARGET_RESP_FLOW', 'PROXY_RESP_FLOW'];
      flowOrder.forEach(flowType => {
        const entries = flowEntries[flowType] || [];

        if (entries.length > 0) {
          entries.forEach((entry, entryIndex) => {
            const uniqueId = `${collapseId}-${flowType}-${entryIndex}`;

            // Determine if this accordion should be open by default based on requirements:
            // - PROXY_REQ_FLOW and PROXY_RESP_FLOW should be open by default
            // - If no PROXY_RESP_FLOW exists, TARGET_RESP_FLOW should be open
            // - For TARGET_REQ_FLOW, open the one containing a status code
            const hasStatusCode = entry.statusCode && entry.statusCode !== '-';
            const shouldBeOpen =
              flowType === 'PROXY_REQ_FLOW' || // Always open Proxy Request
              (flowType === 'PROXY_RESP_FLOW') || // Always open Proxy Response if exists
              (flowType === 'TARGET_RESP_FLOW' && !flowEntries['PROXY_RESP_FLOW']) || // Open Target Response if no Proxy Response
              (flowType === 'TARGET_REQ_FLOW' && hasStatusCode); // Open Target Request if it has a status code

            // Create accordion item for this flow with simplified structure
            detailsHTML += `
              <div class="accordion">
                <div class="accordion-item">
                  <h2 class="accordion-header">
                    <button class="accordion-button ${shouldBeOpen ? '' : 'collapsed'}" type="button"
                            data-bs-target="#collapse-${uniqueId}"
                            aria-expanded="${shouldBeOpen ? 'true' : 'false'}" aria-controls="collapse-${uniqueId}">
                      <strong>${flowType}${entries.length > 1 ? ` (${entryIndex + 1}/${entries.length})` : ''}</strong>
                    </button>
                  </h2>
                  <div id="collapse-${uniqueId}" class="accordion-collapse collapse ${shouldBeOpen ? 'show' : ''}">
                    <div class="accordion-body p-0">
            `;

            // Format entry properties for JSON display
            const entryProps = {
              time: entry.time || '',
              level: entry.level || '',
              messageid: entry.messageId || '',
              flow: entry.flow || '',
              flowstage: entry.flowstage || '',
              requestid: entry.requestId || '',
              correlationid: entry.correlationId || '',
              uri: entry.uri || '',
              verb: entry.verb || '',
              client_id: entry.client_id || '',
              app_name: entry.appName || '',
              statuscode: entry.statusCode || ''
            };

            // Format JSON for display
            const jsonDisplay = formatJSON(entryProps);

            // Process query string
            const queryString = entry.queryString || '-';
            const processedQueryString = formatQueryString(queryString);

            // Process headers
            const headers = entry.headers || '-';
            const processedHeadersHTML = formatHeaders(headers, { entry });

            // Process message body
            const messageBody = entry.messageBody || '-';
            // Format the message body for display using the formatMessageBody function
            const processedMessageBody = formatMessageBody(messageBody);
            const messageBodyId = `message-body-${uniqueId}`;

            // For new structure formats, colorize the headers
            let displayHeaders = entry.format &&
                                (entry.format === 'csv_with_path' ||
                                 entry.format === 'simple_message' ||
                                 entry.format === 'raw_json')
                                ? logProcessor.colorizeHeaderKeys(processedHeadersHTML, { format: entry.format })
                                : processedHeadersHTML;

            // Add entry content
            detailsHTML += `
              <pre><code>${jsonDisplay}</code></pre>
              <p class="mb-1 ps-3 pt-2">---------- Query string:</p>
              <div class="indent mono wrap-content px-3">${processedQueryString}</div>
              <p class="mb-1 ps-3 pt-2">---------- HTTPHeaders:</p>
              <div class="indent mono wrap-content px-3">${displayHeaders}</div>
              <div style="display: flex; justify-content: space-between; align-items: center; padding: 0 16px;">
                <p class="mb-1 ps-0 pt-2">---------- Message body:</p>
                <button type="button" class="offcanvas-copy-btn" id="${messageBodyId}-btn" data-target="${messageBodyId}" title="Copy message body">
                  <i class="bi bi-copy"></i>
                </button>
              </div>
              <div id="${messageBodyId}" class="indent mono px-3" style="white-space: pre-wrap; word-break: break-word; max-width: 100%; line-height: 1.5; max-height: 400px; overflow-y: auto; scrollbar-width: none; -ms-overflow-style: none;">${processedMessageBody}</div>
            `;

            // Close accordion structure
            detailsHTML += `
                    </div>
                  </div>
                </div>
              </div>
            `;
          });
        }
      });

      // Close the details container
      detailsHTML += `</div>`;

      // Set the content
      offcanvasContent.innerHTML = detailsHTML;

      // Initialize manual accordion functionality
      setTimeout(() => {
        const accordionButtons = offcanvasContent.querySelectorAll('.accordion-button');
        accordionButtons.forEach(button => {
          // Remove Bootstrap data attributes that might interfere
          button.removeAttribute('data-bs-toggle');

          // Add our own click handler
          button.onclick = function() {
            const targetId = this.getAttribute('data-bs-target');
            if (targetId) {
              const target = document.querySelector(targetId);
              if (target) {
                // Toggle the 'show' class on the collapse element
                target.classList.toggle('show');

                // Toggle the 'collapsed' class on the button
                this.classList.toggle('collapsed');

                // Update aria-expanded attribute
                const isExpanded = target.classList.contains('show');
                this.setAttribute('aria-expanded', isExpanded ? 'true' : 'false');
              }
            }
          };
        });
      }, 100); // Small delay to ensure DOM is ready

      // Add event listeners for copy message body buttons
      const copyButtons = document.querySelectorAll('.offcanvas-copy-btn');
      copyButtons.forEach(button => {
        button.addEventListener('click', () => {
          const targetId = button.getAttribute('data-target');
          const messageBodyContent = document.getElementById(targetId);

          if (messageBodyContent) {
            // Find the original entry to get the raw message body
            const callIndex = parseInt(offcanvasContent.getAttribute('data-call-index'), 10);
            const visibleEntries = stateManager.getVisibleEntries();

            let originalMessageBody = '';

            if (!isNaN(callIndex)) {
              // For call view, find the specific entry
              const calls = logProcessor.aggregateEntriesByCall(visibleEntries);
              const call = calls[callIndex];
              if (call && call.originalEntries) {
                // Find the entry that matches the message body ID
                const entryId = targetId.split('-').pop();
                const entry = call.originalEntries.find(e => e.uniqueId === entryId);
                if (entry) {
                  originalMessageBody = entry.messageBody || '';
                }
              }
            }

            // If we couldn't find the original entry, fall back to the displayed content
            const textToCopy = originalMessageBody || messageBodyContent.textContent;

            navigator.clipboard.writeText(textToCopy)
              .then(() => {
                // Show visual feedback only (no toast)
                const originalIcon = button.innerHTML;
                button.innerHTML = '<i class="bi bi-check-lg"></i>';
                button.style.color = '#198754';
                setTimeout(() => {
                  button.innerHTML = originalIcon;
                  button.style.color = '';
                }, 1000);
              })
              .catch(err => {
                console.error('Could not copy text: ', err);
                uiManager.showToast('Failed to copy to clipboard', true);
              });
          }
        });
      });

      // Store call index for keyboard navigation
      offcanvasContent.removeAttribute('data-row-index');
      offcanvasContent.setAttribute('data-call-index', callIndex);

      // Update navigation counter
      updateNavigationCounter();

      // Highlight active row
      const previousActive = document.querySelector('.log-row-active');
      if (previousActive) {
        previousActive.classList.remove('log-row-active');
      }

      const activeRow = document.querySelector(`tr[data-call-index="${callIndex}"]`);
      if (activeRow) {
        activeRow.classList.add('log-row-active');
      }

      // Show offcanvas
      const offcanvas = document.getElementById('entryDetailsOffcanvas');
      if (offcanvas) {
        const bsOffcanvas = bootstrap.Offcanvas.getInstance(offcanvas) || new bootstrap.Offcanvas(offcanvas);
        bsOffcanvas.show();
      }
    }
  };

  // Navigate to log entry
  window.navigateToLog = (rowIndex) => {
    window.toggleRow(rowIndex);
  };

  // Navigate to call
  window.navigateToCall = (callIndex) => {
    window.showCallDetails(callIndex);
  };

  // Toggle message ID filter
  window.toggleMessageIdFilter = (messageId) => {
    const currentViewType = stateManager.getState().currentViewType;
    const filterState = stateManager.getState().filterState[currentViewType];

    // Toggle selection
    if (filterState.selectedMessageIds.includes(messageId)) {
      // Remove from selection
      const newSelectedIds = filterState.selectedMessageIds.filter(id => id !== messageId);
      stateManager.updateFilterState(currentViewType, {
        selectedMessageIds: newSelectedIds,
        allMessageIds: newSelectedIds.length === 0
      });
    } else {
      // Add to selection
      const newSelectedIds = [...filterState.selectedMessageIds, messageId];
      stateManager.updateFilterState(currentViewType, {
        selectedMessageIds: newSelectedIds,
        allMessageIds: false
      });
    }

    // Update UI based on view type
    if (currentViewType === 'flows') {
      uiManager.updateDisplayedEntries();
    } else if (currentViewType === 'calls') {
      uiManager.showCallSequenceView();
    }
  };

  // Toggle flow filter
  window.toggleFlowFilter = (flow) => {
    const currentViewType = stateManager.getState().currentViewType;
    const filterState = stateManager.getState().filterState[currentViewType];

    // Toggle selection
    if (filterState.selectedFlows.includes(flow)) {
      // Remove from selection
      const newSelectedFlows = filterState.selectedFlows.filter(f => f !== flow);
      stateManager.updateFilterState(currentViewType, {
        selectedFlows: newSelectedFlows,
        allFlows: newSelectedFlows.length === 0
      });
    } else {
      // Add to selection
      const newSelectedFlows = [...filterState.selectedFlows, flow];
      stateManager.updateFilterState(currentViewType, {
        selectedFlows: newSelectedFlows,
        allFlows: false
      });
    }

    // Update UI based on view type
    if (currentViewType === 'flows') {
      uiManager.updateDisplayedEntries();
    } else if (currentViewType === 'calls') {
      uiManager.showCallSequenceView();
    }
  };

  // Toggle all message IDs
  window.toggleAllMessageIds = () => {
    const currentViewType = stateManager.getState().currentViewType;
    const filterState = stateManager.getState().filterState[currentViewType];

    // Toggle all message IDs
    stateManager.updateFilterState(currentViewType, {
      allMessageIds: !filterState.allMessageIds,
      selectedMessageIds: []
    });

    // Update UI based on view type
    if (currentViewType === 'flows') {
      uiManager.updateDisplayedEntries();
    } else if (currentViewType === 'calls') {
      uiManager.showCallSequenceView();
    }
  };

  // Toggle all flows
  window.toggleAllFlows = () => {
    const currentViewType = stateManager.getState().currentViewType;
    const filterState = stateManager.getState().filterState[currentViewType];

    // Toggle all flows
    stateManager.updateFilterState(currentViewType, {
      allFlows: !filterState.allFlows,
      selectedFlows: []
    });

    // Update UI based on view type
    if (currentViewType === 'flows') {
      uiManager.updateDisplayedEntries();
    } else if (currentViewType === 'calls') {
      uiManager.showCallSequenceView();
    }
  };

  // Function to copy a field value to clipboard
  window.copyFieldValue = (text) => {
    navigator.clipboard.writeText(text)
      .then(() => {
        uiManager.showToast('Field value copied to clipboard', false);
      })
      .catch(err => {
        console.error('Could not copy text: ', err);
        uiManager.showToast('Failed to copy to clipboard', true);
      });
  };
}

/**
 * Set up message ID filter checkboxes
 */
function setupMessageIdFilterCheckboxes() {
  const selectAllMessageIds = document.getElementById('selectAllMessageIds');
  const messageIdControls = document.getElementById('messageIdControls');

  if (!selectAllMessageIds || !messageIdControls) return;

  // Add event listener to "All message IDs" checkbox
  selectAllMessageIds.addEventListener('click', (e) => {
    // Get current filter state for this view
    const currentViewType = stateManager.getState().currentViewType;
    const filterState = stateManager.getState().filterState[currentViewType];

    if (!selectAllMessageIds.checked) {
      // When unchecking "All", don't change anything if there are no selections
      if (filterState.selectedMessageIds.length === 0) {
        // If nothing selected, prevent unchecking
        e.preventDefault();
        selectAllMessageIds.checked = true;
        return;
      }
    }
  });

  selectAllMessageIds.addEventListener('change', () => {
    const isChecked = selectAllMessageIds.checked;

    // Update all message ID checkboxes
    const messageIdCheckboxes = messageIdControls.querySelectorAll('.message-id-checkbox');
    messageIdCheckboxes.forEach(checkbox => {
      checkbox.checked = false;
    });

    // Update state
    const currentViewType = stateManager.getState().currentViewType;
    stateManager.updateFilterState(currentViewType, {
      allMessageIds: isChecked,
      selectedMessageIds: []
    });

    // Update UI
    if (currentViewType === 'flows') {
      uiManager.updateDisplayedEntries();
    } else if (currentViewType === 'calls') {
      uiManager.showCallSequenceView();
    }
  });

  // We'll populate message ID checkboxes when entries are processed
  document.addEventListener('entriesProcessed', () => {
    populateMessageIdCheckboxes();
  });
}

/**
 * Populate message ID checkboxes
 */
function populateMessageIdCheckboxes() {
  const messageIdControls = document.getElementById('messageIdControls');
  const selectAllMessageIds = document.getElementById('selectAllMessageIds');

  if (!messageIdControls || !selectAllMessageIds) return;

  // Get all message IDs from entries
  const entries = stateManager.getState().entries;
  const messageIds = [...new Set(entries.map(entry => entry.messageId))].filter(id => id !== '-');

  // Clear existing message ID checkboxes
  const divider = messageIdControls.querySelector('.dropdown-divider');
  if (divider) {
    // Remove all elements after the divider
    let nextSibling = divider.nextSibling;
    while (nextSibling) {
      const current = nextSibling;
      nextSibling = nextSibling.nextSibling;
      messageIdControls.removeChild(current);
    }
  }

  // Get current view type and filter state
  const currentViewType = stateManager.getState().currentViewType;
  const filterState = stateManager.getState().filterState[currentViewType];

  // Update "All message IDs" checkbox
  selectAllMessageIds.checked = filterState.allMessageIds;

  // Add message ID checkboxes
  messageIds.forEach(messageId => {
    const label = document.createElement('label');
    label.className = 'dropdown-item';

    const checkbox = document.createElement('input');
    checkbox.type = 'checkbox';
    checkbox.className = 'message-id-checkbox';
    checkbox.value = messageId;

    // Set checkbox state based on current filter state
    checkbox.checked = !filterState.allMessageIds && filterState.selectedMessageIds.includes(messageId);

    // Add event listener to checkbox
    checkbox.addEventListener('change', () => {
      // Get all checked message IDs
      const messageIdCheckboxes = messageIdControls.querySelectorAll('.message-id-checkbox');
      const checkedMessageIds = Array.from(messageIdCheckboxes)
        .filter(cb => cb.checked)
        .map(cb => cb.value);

      // Update "All message IDs" checkbox
      selectAllMessageIds.checked = checkedMessageIds.length === 0;

      // Update state
      const currentViewType = stateManager.getState().currentViewType;
      stateManager.updateFilterState(currentViewType, {
        allMessageIds: checkedMessageIds.length === 0,
        selectedMessageIds: checkedMessageIds
      });

      // Update UI
      if (currentViewType === 'flows') {
        uiManager.updateDisplayedEntries();
      } else if (currentViewType === 'calls') {
        uiManager.showCallSequenceView();
      }
    });

    // Add double-click handler to select only this message ID
    label.addEventListener('dblclick', (e) => {
      e.preventDefault();
      e.stopPropagation();

      // Force the checkbox to be checked
      checkbox.checked = true;

      // Uncheck all other message ID checkboxes
      const messageIdCheckboxes = messageIdControls.querySelectorAll('.message-id-checkbox');
      messageIdCheckboxes.forEach(cb => {
        if (cb !== checkbox) {
          cb.checked = false;
        }
      });

      // Update "All message IDs" checkbox
      selectAllMessageIds.checked = false;

      // Update state
      const currentViewType = stateManager.getState().currentViewType;
      stateManager.updateFilterState(currentViewType, {
        allMessageIds: false,
        selectedMessageIds: [messageId]
      });

      // Update UI
      if (currentViewType === 'flows') {
        uiManager.updateDisplayedEntries();
      } else if (currentViewType === 'calls') {
        uiManager.showCallSequenceView();
      }
    });

    label.appendChild(checkbox);
    label.appendChild(document.createTextNode(` ${messageId}`));

    messageIdControls.appendChild(label);
  });
}

/**
 * Set up flow filter checkboxes
 */
function setupFlowFilterCheckboxes() {
  const selectAllFlows = document.getElementById('selectAllFlows');
  const flowCheckboxes = document.querySelectorAll('.flow-checkbox');

  if (!selectAllFlows || flowCheckboxes.length === 0) return;

  // Add event listener to "All flows" checkbox
  selectAllFlows.addEventListener('click', (e) => {
    // Get current filter state for this view
    const currentViewType = stateManager.getState().currentViewType;
    const filterState = stateManager.getState().filterState[currentViewType];

    if (!selectAllFlows.checked) {
      // When unchecking "All", don't change anything if there are no selections
      if (filterState.selectedFlows.length === 0) {
        // If nothing selected, prevent unchecking
        e.preventDefault();
        selectAllFlows.checked = true;
        return;
      }
    }
  });

  selectAllFlows.addEventListener('change', () => {
    const isChecked = selectAllFlows.checked;

    // Update all flow checkboxes
    flowCheckboxes.forEach(checkbox => {
      checkbox.checked = false;
    });

    // Update state
    const currentViewType = stateManager.getState().currentViewType;
    stateManager.updateFilterState(currentViewType, {
      allFlows: isChecked,
      selectedFlows: []
    });

    // Update UI
    if (currentViewType === 'flows') {
      uiManager.updateDisplayedEntries();
    } else if (currentViewType === 'calls') {
      uiManager.showCallSequenceView();
    }
  });

  // Add event listeners to individual flow checkboxes
  flowCheckboxes.forEach(checkbox => {
    // Get the parent label element
    const label = checkbox.closest('label');
    if (!label) return;

    // Get the flow value
    const flow = checkbox.value;

    checkbox.addEventListener('change', () => {
      // Get all checked flow values
      const checkedFlows = Array.from(flowCheckboxes)
        .filter(cb => cb.checked)
        .map(cb => cb.value);

      // Update "All flows" checkbox
      selectAllFlows.checked = checkedFlows.length === 0;

      // Update state
      const currentViewType = stateManager.getState().currentViewType;
      stateManager.updateFilterState(currentViewType, {
        allFlows: checkedFlows.length === 0,
        selectedFlows: checkedFlows
      });

      // Update UI
      if (currentViewType === 'flows') {
        uiManager.updateDisplayedEntries();
      } else if (currentViewType === 'calls') {
        uiManager.showCallSequenceView();
      }
    });

    // Add double-click handler to select only this flow
    label.addEventListener('dblclick', (e) => {
      e.preventDefault();
      e.stopPropagation();

      // Force the checkbox to be checked
      checkbox.checked = true;

      // Uncheck all other flow checkboxes
      flowCheckboxes.forEach(cb => {
        if (cb !== checkbox) {
          cb.checked = false;
        }
      });

      // Update "All flows" checkbox
      selectAllFlows.checked = false;

      // Update state
      const currentViewType = stateManager.getState().currentViewType;
      stateManager.updateFilterState(currentViewType, {
        allFlows: false,
        selectedFlows: [flow]
      });

      // Update UI
      if (currentViewType === 'flows') {
        uiManager.updateDisplayedEntries();
      } else if (currentViewType === 'calls') {
        uiManager.showCallSequenceView();
      }
    });
  });
}

/**
 * Update message ID checkboxes to reflect the filter state for the specified view type
 * @param {string} viewType - The view type ('flows' or 'calls')
 */
function updateMessageIdCheckboxes(viewType) {
  const messageIdControls = document.getElementById('messageIdControls');
  const selectAllMessageIds = document.getElementById('selectAllMessageIds');

  if (!messageIdControls || !selectAllMessageIds) return;

  // Get the filter state for the specified view type
  const filterState = stateManager.getState().filterState[viewType];

  // Update "All message IDs" checkbox
  selectAllMessageIds.checked = filterState.allMessageIds;

  // Update individual message ID checkboxes
  const messageIdCheckboxes = messageIdControls.querySelectorAll('.message-id-checkbox');
  messageIdCheckboxes.forEach(checkbox => {
    const messageId = checkbox.value;
    checkbox.checked = !filterState.allMessageIds && filterState.selectedMessageIds.includes(messageId);
  });
}

/**
 * Update flow checkboxes to reflect the filter state for the specified view type
 * @param {string} viewType - The view type ('flows' or 'calls')
 */
function updateFlowCheckboxes(viewType) {
  const selectAllFlows = document.getElementById('selectAllFlows');
  const flowCheckboxes = document.querySelectorAll('.flow-checkbox');

  if (!selectAllFlows || flowCheckboxes.length === 0) return;

  // Get the filter state for the specified view type
  const filterState = stateManager.getState().filterState[viewType];

  // Update "All flows" checkbox
  selectAllFlows.checked = filterState.allFlows;

  // Update individual flow checkboxes
  flowCheckboxes.forEach(checkbox => {
    const flow = checkbox.value;
    checkbox.checked = !filterState.allFlows && filterState.selectedFlows.includes(flow);
  });
}

/**
 * Set up status code filter checkboxes
 */
function setupStatusCodeFilterCheckboxes() {
  const statusCodeControls = document.getElementById('statusCodeControls');
  const selectAllStatusCodes = document.getElementById('selectAllStatusCodes');

  if (!statusCodeControls || !selectAllStatusCodes) return;

  // Add event listener to "All status codes" checkbox
  selectAllStatusCodes.addEventListener('click', (e) => {
    // Get current filter state for this view
    const currentViewType = stateManager.getState().currentViewType;
    const filterState = stateManager.getState().filterState[currentViewType];

    if (!selectAllStatusCodes.checked) {
      // When unchecking "All", don't change anything if there are no selections
      if (filterState.selectedStatusCodes.length === 0) {
        // If nothing selected, prevent unchecking
        e.preventDefault();
        selectAllStatusCodes.checked = true;
        return;
      }
    }
  });

  selectAllStatusCodes.addEventListener('change', () => {
    const isChecked = selectAllStatusCodes.checked;

    // Update all status code checkboxes
    const statusCodeCheckboxes = statusCodeControls.querySelectorAll('.status-code-checkbox');
    statusCodeCheckboxes.forEach(checkbox => {
      checkbox.checked = false;
    });

    // Update state
    const currentViewType = stateManager.getState().currentViewType;
    stateManager.updateFilterState(currentViewType, {
      allStatusCodes: isChecked,
      selectedStatusCodes: []
    });

    // Update UI
    if (currentViewType === 'flows') {
      uiManager.updateDisplayedEntries();
    } else if (currentViewType === 'calls') {
      uiManager.showCallSequenceView();
    }
  });

  // We'll populate status code checkboxes when entries are processed
  document.addEventListener('entriesProcessed', () => {
    populateStatusCodeCheckboxes();
  });
}

/**
 * Populate status code checkboxes
 */
function populateStatusCodeCheckboxes() {
  const statusCodeControls = document.getElementById('statusCodeControls');
  const selectAllStatusCodes = document.getElementById('selectAllStatusCodes');

  if (!statusCodeControls || !selectAllStatusCodes) return;

  // Get all status codes from entries
  const entries = stateManager.getState().entries;
  const statusCodes = [...new Set(entries.map(entry => entry.statusCode))].filter(code => code !== '-');

  // Sort status codes numerically
  statusCodes.sort((a, b) => parseInt(a) - parseInt(b));

  // Clear existing status code checkboxes
  const divider = statusCodeControls.querySelector('.dropdown-divider');
  if (divider) {
    // Remove all elements after the divider
    let nextSibling = divider.nextSibling;
    while (nextSibling) {
      const current = nextSibling;
      nextSibling = nextSibling.nextSibling;
      statusCodeControls.removeChild(current);
    }
  }

  // Get current view type and filter state
  const currentViewType = stateManager.getState().currentViewType;
  const filterState = stateManager.getState().filterState[currentViewType];

  // Update "All status codes" checkbox
  selectAllStatusCodes.checked = filterState.allStatusCodes;

  // Add status code checkboxes
  statusCodes.forEach(statusCode => {
    const label = document.createElement('label');
    label.className = 'dropdown-item';

    const checkbox = document.createElement('input');
    checkbox.type = 'checkbox';
    checkbox.className = 'status-code-checkbox';
    checkbox.value = statusCode;
    checkbox.checked = !filterState.allStatusCodes && filterState.selectedStatusCodes.includes(statusCode);

    // Add event listener to checkbox
    checkbox.addEventListener('change', () => {
      // Get all checked status codes
      const statusCodeCheckboxes = statusCodeControls.querySelectorAll('.status-code-checkbox');
      const checkedStatusCodes = Array.from(statusCodeCheckboxes)
        .filter(cb => cb.checked)
        .map(cb => cb.value);

      // Update "All status codes" checkbox
      selectAllStatusCodes.checked = checkedStatusCodes.length === 0;

      // Update state
      const currentViewType = stateManager.getState().currentViewType;
      stateManager.updateFilterState(currentViewType, {
        allStatusCodes: checkedStatusCodes.length === 0,
        selectedStatusCodes: checkedStatusCodes
      });

      // Update UI
      if (currentViewType === 'flows') {
        uiManager.updateDisplayedEntries();
      } else if (currentViewType === 'calls') {
        uiManager.showCallSequenceView();
      }
    });

    // Add double-click handler to select only this status code
    label.addEventListener('dblclick', (e) => {
      e.preventDefault();
      e.stopPropagation();

      // Force the checkbox to be checked
      checkbox.checked = true;

      // Uncheck all other status code checkboxes
      const statusCodeCheckboxes = statusCodeControls.querySelectorAll('.status-code-checkbox');
      statusCodeCheckboxes.forEach(cb => {
        if (cb !== checkbox) {
          cb.checked = false;
        }
      });

      // Update "All status codes" checkbox
      selectAllStatusCodes.checked = false;

      // Update state
      const currentViewType = stateManager.getState().currentViewType;
      stateManager.updateFilterState(currentViewType, {
        allStatusCodes: false,
        selectedStatusCodes: [statusCode]
      });

      // Update UI
      if (currentViewType === 'flows') {
        uiManager.updateDisplayedEntries();
      } else if (currentViewType === 'calls') {
        uiManager.showCallSequenceView();
      }
    });

    // Add status code as plain text
    label.appendChild(checkbox);
    label.appendChild(document.createTextNode(' ' + statusCode));
    statusCodeControls.appendChild(label);
  });
}

/**
 * Update status code checkboxes to reflect the filter state for the specified view type
 * @param {string} viewType - The view type ('flows' or 'calls')
 */
function updateStatusCodeCheckboxes(viewType) {
  const statusCodeControls = document.getElementById('statusCodeControls');
  const selectAllStatusCodes = document.getElementById('selectAllStatusCodes');

  if (!statusCodeControls || !selectAllStatusCodes) return;

  // Get the filter state for the specified view type
  const filterState = stateManager.getState().filterState[viewType];

  // Update "All status codes" checkbox
  selectAllStatusCodes.checked = filterState.allStatusCodes;

  // Update individual status code checkboxes
  const statusCodeCheckboxes = statusCodeControls.querySelectorAll('.status-code-checkbox');
  statusCodeCheckboxes.forEach(checkbox => {
    const statusCode = checkbox.value;
    checkbox.checked = !filterState.allStatusCodes && filterState.selectedStatusCodes.includes(statusCode);
  });
}

// Initialize the app when the DOM is loaded
document.addEventListener('DOMContentLoaded', initializeApp);
