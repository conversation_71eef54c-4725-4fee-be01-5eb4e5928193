/**
 * OpenSearch Client for Apigee Log Processor
 * Handles connection, authentication, and search operations with OpenSearch
 */

export class OpenSearchClient {
  constructor(config = {}) {
    this.config = {
      endpoint: config.endpoint || '',
      indexPattern: config.indexPattern || 'apigee-prod-*',
      username: config.username || '',
      password: config.password || '',
      timeout: config.timeout || 30000, // 30 seconds
      useDirectConnection: config.useDirectConnection !== false, // Default to direct connection
      ...config
    };
    this.isConnected = false;
    this.lastError = null;
    this.certificateAccepted = false;
  }

  /**
   * Update client configuration
   * @param {Object} newConfig - New configuration
   */
  updateConfig(newConfig) {
    this.config = { ...this.config, ...newConfig };
    this.isConnected = false; // Reset connection status
  }

  /**
   * Get basic auth header
   * @returns {string} Base64 encoded auth header
   */
  getAuthHeader() {
    if (!this.config.username || !this.config.password) {
      throw new Error('Username and password are required');
    }
    const credentials = btoa(`${this.config.username}:${this.config.password}`);
    return `Basic ${credentials}`;
  }

  /**
   * Make HTTP request to OpenSearch
   * @param {string} path - API path
   * @param {Object} options - Request options
   * @returns {Promise<Object>} Response data
   */
  async makeRequest(path, options = {}) {
    if (!this.config.endpoint) {
      throw new Error('OpenSearch endpoint not configured');
    }

    const url = `${this.config.endpoint}${path}`;
    const requestOptions = {
      method: options.method || 'GET',
      mode: 'cors',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': this.getAuthHeader(),
        ...options.headers
      },
      ...options
    };

    // Add body if provided
    if (options.body && typeof options.body === 'object') {
      requestOptions.body = JSON.stringify(options.body);
    }

    try {
      console.log(`Making OpenSearch request to: ${url}`);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

      const response = await fetch(url, {
        ...requestOptions,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      this.lastError = null;
      this.certificateAccepted = true;
      return data;

    } catch (error) {
      this.lastError = error.message;
      console.error('OpenSearch request failed:', error);

      if (error.name === 'AbortError') {
        throw new Error('Request timeout - OpenSearch did not respond within 30 seconds');
      }

      // Detect error type based on the error and context
      let errorType = 'CONNECTION';
      let errorMessage = error.message;

      // CORS detection: Failed to fetch + cross-origin context
      if (error.message.includes('Failed to fetch')) {
        // Check if this is likely a CORS issue
        const isCrossOrigin = url.includes('://') && !url.includes(window.location.origin);
        if (isCrossOrigin) {
          errorType = 'CORS';
          errorMessage = 'CORS Error: Browser blocked the request due to CORS policy. The OpenSearch server needs to allow cross-origin requests.';
        } else {
          errorType = 'CONNECTION';
          errorMessage = 'Connection Error: Unable to reach OpenSearch server. Please check the URL and network connectivity.';
        }
      }

      // Certificate errors
      if (error.message.includes('ERR_CERT_AUTHORITY_INVALID') ||
          error.message.includes('ERR_CERT_COMMON_NAME_INVALID') ||
          error.message.includes('ERR_CERT_DATE_INVALID')) {
        errorType = 'CERTIFICATE';
        errorMessage = 'Certificate Error: SSL certificate is not trusted. Please accept the certificate in your browser.';
      }

      // Create enhanced error with type information
      const enhancedError = new Error(errorMessage);
      enhancedError.type = errorType;
      enhancedError.originalError = error;

      throw enhancedError;
    }
  }

  /**
   * Open OpenSearch URL in new tab to accept certificate
   */
  openCertificateAcceptance() {
    const url = this.config.endpoint;
    console.log('Opening OpenSearch URL for certificate acceptance:', url);
    window.open(url, '_blank');
    return {
      message: 'Please accept the certificate in the new tab, then return here and try again.',
      url: url
    };
  }

  /**
   * Generate curl command for manual testing
   */
  generateCurlCommand(path = '/') {
    const url = `${this.config.endpoint}${path}`;
    const auth = `${this.config.username}:${this.config.password}`;
    return `curl -k -u "${auth}" "${url}"`;
  }

  /**
   * Test connection to OpenSearch
   * @returns {Promise<Object>} Connection test result
   */
  async testConnection() {
    try {
      console.log('Testing OpenSearch connection...');

      // First test basic connectivity with cluster info
      const clusterInfo = await this.makeRequest('/');

      // Then test index access
      const indexInfo = await this.makeRequest(`/${this.config.indexPattern}/_mapping`, {
        method: 'GET'
      });

      this.isConnected = true;

      return {
        success: true,
        connected: true,
        cluster: {
          name: clusterInfo.cluster_name,
          version: clusterInfo.version?.number,
          tagline: clusterInfo.tagline
        },
        indices: Object.keys(indexInfo),
        message: 'Successfully connected to OpenSearch'
      };

    } catch (error) {
      this.isConnected = false;

      return {
        success: false,
        connected: false,
        error: error.message,
        errorType: error.type || 'CONNECTION',
        originalError: error.originalError,
        curlCommand: this.generateCurlCommand(),
        message: `Failed to connect: ${error.message}`
      };
    }
  }

  /**
   * Perform search query
   * @param {Object} query - OpenSearch query DSL
   * @param {Object} options - Search options
   * @returns {Promise<Object>} Search results
   */
  async search(query, options = {}) {
    const searchOptions = {
      size: options.size || 50,
      from: options.from || 0,
      sort: options.sort || [{ '@timestamp': { order: 'desc' } }],
      ...options
    };

    const searchBody = {
      ...searchOptions,
      query: query || { match_all: {} }
    };

    try {
      console.log('Executing OpenSearch query:', JSON.stringify(searchBody, null, 2));

      const response = await this.makeRequest(`/${this.config.indexPattern}/_search`, {
        method: 'POST',
        body: searchBody
      });

      console.log('OpenSearch raw response:', response);

      return {
        success: true,
        hits: response.hits?.hits || [],
        total: response.hits?.total || { value: 0, relation: 'eq' },
        took: response.took || 0,
        aggregations: response.aggregations,
        rawResponse: response
      };

    } catch (error) {
      console.error('Search failed:', error);
      return {
        success: false,
        error: error.message,
        hits: [],
        total: { value: 0, relation: 'eq' }
      };
    }
  }

  /**
   * Get timeline data using date histogram aggregation
   * @param {Object} query - Base query
   * @param {Object} timeRange - Time range { start, end }
   * @param {string} interval - Time interval (1h, 1d, etc.)
   * @returns {Promise<Object>} Timeline data
   */
  async getTimelineData(query = {}, timeRange = null, interval = '1h') {
    const timelineQuery = {
      size: 0,
      query: query,
      aggs: {
        timeline: {
          date_histogram: {
            field: '@timestamp',
            calendar_interval: interval,
            min_doc_count: 1
          }
        }
      }
    };

    // Add time range filter if provided
    if (timeRange) {
      timelineQuery.query = {
        bool: {
          must: [query],
          filter: [
            {
              range: {
                '@timestamp': {
                  gte: timeRange.start,
                  lte: timeRange.end
                }
              }
            }
          ]
        }
      };
    }

    try {
      const response = await this.makeRequest(`/${this.config.indexPattern}/_search`, {
        method: 'POST',
        body: timelineQuery
      });

      return {
        success: true,
        buckets: response.aggregations?.timeline?.buckets || [],
        totalHits: response.hits.total,
        took: response.took
      };

    } catch (error) {
      console.error('Timeline query failed:', error);
      return {
        success: false,
        error: error.message,
        buckets: [],
        totalHits: { value: 0, relation: 'eq' }
      };
    }
  }

  /**
   * Get available indices matching the pattern
   * @returns {Promise<Array>} List of indices
   */
  async getIndices() {
    try {
      const response = await this.makeRequest(`/_cat/indices/${this.config.indexPattern}?format=json`);
      return response.map(index => index.index);
    } catch (error) {
      console.error('Failed to get indices:', error);
      return [];
    }
  }

  /**
   * Get field mappings for the indices
   * @returns {Promise<Object>} Field mappings
   */
  async getFieldMappings() {
    try {
      const response = await this.makeRequest(`/${this.config.indexPattern}/_mapping`);
      return response;
    } catch (error) {
      console.error('Failed to get field mappings:', error);
      return {};
    }
  }

  /**
   * Check if client is connected
   * @returns {boolean} Connection status
   */
  isClientConnected() {
    return this.isConnected;
  }

  /**
   * Get last error message
   * @returns {string|null} Last error
   */
  getLastError() {
    return this.lastError;
  }

  /**
   * Get current configuration (without sensitive data)
   * @returns {Object} Safe configuration
   */
  getSafeConfig() {
    return {
      endpoint: this.config.endpoint,
      indexPattern: this.config.indexPattern,
      username: this.config.username,
      connected: this.isConnected,
      lastError: this.lastError
    };
  }
}

// Export singleton instance
export const opensearchClient = new OpenSearchClient();
