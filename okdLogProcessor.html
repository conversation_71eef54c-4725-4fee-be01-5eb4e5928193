<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OKD Log Processor</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="okdLogProcessor.css">
    <link rel="stylesheet" href="okdLogProcessor-dark.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Theme Switcher -->
    <div class="theme-switch-wrapper">
        <label class="theme-switch" for="themeSwitch">
            <input type="checkbox" id="themeSwitch" />
            <div class="slider">
                <i class="bi bi-sun-fill slider-icon sun"></i>
                <i class="bi bi-moon-fill slider-icon moon"></i>
            </div>
        </label>
    </div>

    <div class="container" id="inputContainer">
        <div class="d-flex justify-content-between align-items-center">
            <h2 class="text-center flex-grow-1">OKD Log Processor</h2>
            <button class="btn btn-sm btn-outline-secondary d-none" id="toggleInputButton" title="Expand input section">
                <i class="bi bi-chevron-down"></i>
            </button>
        </div>

        <div class="input-section" id="inputSection">
            <h4 class="mb-3">Paste text or Open from file</h4>
            <div class="mb-3">
                <textarea id="textInput" class="form-control shadow-sm" rows="10" cols="100" placeholder="Paste your log content here..."></textarea>
            </div>
            <div class="mb-3">
                <input type="file" id="fileInput" class="form-control shadow-sm" accept=".txt,.log,.json">
            </div>
            <div class="d-flex mt-4">
                <button class="btn btn-primary px-4" onclick="processData()">
                    <i class="bi bi-play-fill me-1"></i> Process logs
                </button>
                <button class="btn btn-danger" onclick="clearText()">
                    <i class="bi bi-x-lg me-1"></i> Clear All
                </button>
            </div>
        </div>
    </div>

    <div class="container hidden" id="processedContainer">
        <div class="text-center">
            <h3 class="">Processed logs</h3>

            <!-- Improved navigation and filter controls -->
            <div class="entry-navigation ">
                <div class="d-flex justify-content-center align-items-center flex-wrap">
                    <div class="">
                        <button id="prevLog" class="btn btn-secondary" disabled>
                            <i class="bi bi-arrow-left"></i> Previous
                        </button>
                    </div>
                    <div class="btn-group">
                        <input type="checkbox" class="btn-check" id="infoBtn" autocomplete="off" checked>
                        <label class="btn btn-outline-success fw-bold" for="infoBtn" id="infoLabel">INFO</label>

                        <input type="checkbox" class="btn-check" id="debugBtn" autocomplete="off" checked>
                        <label class="btn btn-outline-primary fw-bold" for="debugBtn" id="debugLabel">DEBUG</label>

                        <input type="checkbox" class="btn-check" id="traceBtn" autocomplete="off" checked>
                        <label class="btn btn-outline-warning fw-bold" for="traceBtn" id="traceLabel">TRACE</label>

                        <input type="checkbox" class="btn-check" id="errorBtn" autocomplete="off" checked>
                        <label class="btn btn-outline-danger fw-bold" for="errorBtn" id="errorLabel">ERROR</label>
                    </div>
                    <div class="">
                        <button id="nextLog" class="btn btn-secondary" disabled>
                            Next <i class="bi bi-arrow-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced search interface -->
        <div class="search-container">
            <div class="search-input-wrapper">
                <i class="bi bi-search search-icon"></i>
                <input type="text"
                       id="logSearchInput"
                       class="search-input"
                       placeholder="Search logs (use AND/OR for multiple terms, &quot;quotes&quot; for exact match)"
                       title="Search syntax:&#13;• Single term: error&#13;• Multiple terms: error AND timeout&#13;• Alternative terms: error OR timeout&#13;• Exact match: &quot;connection refused&quot;&#13;• Complex: (error AND timeout) OR &quot;connection refused&quot;">

                <div class="search-controls">
                    <div class="search-options">
                        <div class="global-search-toggle-wrapper" title="Search across all logs">
                            <input type="checkbox" id="globalSearchToggle" class="global-search-toggle">
                            <label for="globalSearchToggle" class="global-search-label">Global</label>
                        </div>
                    </div>

                    <div class="search-navigation">
                        <button id="prevMatchBtn" class="search-nav-btn" title="Previous match (↑)" disabled>
                            <i class="bi bi-chevron-up"></i>
                        </button>
                        <span id="searchMatchCount" class="search-match-count" title="Total matches">0 matches</span>
                        <button id="nextMatchBtn" class="search-nav-btn" title="Next match (↓)" disabled>
                            <i class="bi bi-chevron-down"></i>
                        </button>
                    </div>

                    <button id="clearSearchBtn" class="search-clear-btn" title="Clear search (Esc)">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            </div>
            <div id="searchHelpText" class="search-help-text">
                <!-- Help text will be shown here when needed -->
            </div>
            </button>
        </div>

        <!-- Date filter controls - now always visible -->
        <div class="date-filter-container mb-3">
            <div id="dateFilterPanel" class="date-filter-panel">
                <div class="date-filter-content">
                    <div class="date-time-picker-row">
                        <!-- From date/time -->
                        <div class="date-time-group">
                            <label class="date-time-label">From</label>
                            <div class="date-time-inputs">
                                <div class="date-input-wrapper">
                                    <input type="date" id="startDateInput" class="form-control form-control-sm">
                                </div>
                                <div class="time-input-wrapper">
                                    <input type="time" id="startTimeInput" class="form-control form-control-sm" step="1" value="">
                                    <i class="bi bi-clock time-icon"></i>
                                </div>
                            </div>
                        </div>

                        <!-- To date/time -->
                        <div class="date-time-group">
                            <label class="date-time-label">To</label>
                            <div class="date-time-inputs">
                                <div class="date-input-wrapper">
                                    <input type="date" id="endDateInput" class="form-control form-control-sm">
                                </div>
                                <div class="time-input-wrapper">
                                    <input type="time" id="endTimeInput" class="form-control form-control-sm" step="1" value="">
                                    <i class="bi bi-clock time-icon"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Action buttons -->
                        <div class="date-filter-actions">
                            <button id="applyDateFilterBtn" class="btn btn-primary btn-sm">
                                <i class="bi bi-funnel"></i> Apply
                            </button>
                            <button id="resetDateFilterBtn" class="btn btn-outline-secondary btn-sm">
                                <i class="bi bi-x"></i> Reset
                            </button>
                        </div>
                    </div>

                    <div class="date-filter-status mt-2" id="dateFilterStatus">
                        <!-- Date filter status will be shown here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Log entry header moved outside currentLogEntry -->
        <div class="log-entry-header">
            <span id="levelIndicator" class="level-indicator"></span>
            <span id="entryCounter" class="entry-counter">0 of 0</span>
            <div class="header-controls">
                <div class="jobid-dropdown">
                    <button type="button" class="jobid-dropdown-button">
                        <span>Filter by JobId</span>
                        <i class="bi bi-chevron-down"></i>
                    </button>
                    <div class="jobid-dropdown-menu">
                        <label class="jobid-checkbox-item">
                            <input type="checkbox" id="selectAllJobIds" checked>
                            <span>All JobId</span>
                        </label>
                        <div id="jobIdList" class="jobid-dropdown-content"><!-- JobIDs will be added dynamically here --></div>
                    </div>
                </div>
                <button id="copyLogButton" class="copy-button">
                    <i class="bi bi-clipboard" aria-hidden="true"></i>
                    <span>Copy</span>
                </button>
                <div class="dropdown">
                    <button id="exportLogsButton" class="export-button dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-download" aria-hidden="true"></i>
                        <span>Export</span>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" id="exportCurrentLog">Export Current Log</a></li>
                        <li><a class="dropdown-item" href="#" id="exportFilteredLogs">Export Filtered Logs</a></li>
                        <li><a class="dropdown-item" href="#" id="exportAllLogs">Export All Logs</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Simplified currentLogEntry div -->
        <div id="currentLogEntry" class="fileContent hidden">
            <pre id="logContentDisplay"></pre>
        </div>

        <button id="backToTopButton" class="back-to-top hidden" aria-label="Back to top">
            <i class="bi bi-arrow-up"></i>
        </button>
    </div>

    <!-- Toast Notification -->
    <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
        <div id="liveToast" class="toast border border-2 border-success" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">Notification</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="toastMessage">
                <!-- Toast message will be inserted here -->
            </div>
        </div>
    </div>

    <footer class="container text-center ">
        <small>&copy; 2025 · OKD Log Processor</small>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="okdLogProcessor.js"></script>
</body>
</html>