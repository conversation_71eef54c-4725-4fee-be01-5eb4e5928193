/**
 * Theme Manager for Apigee Log Processor
 * Handles theme switching and persistence
 */
import { stateManager } from './state.js';

export class ThemeManager {
  constructor() {
    this.themeSwitch = null;
  }

  /**
   * Initialize the theme manager
   */
  init() {
    // We'll still initialize the theme manager to handle theme application,
    // but we won't rely on the local theme switch since it will be hidden

    // Check if there's a theme set in localStorage by the parent page
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
      // Update the state manager with the saved theme
      stateManager.setTheme(savedTheme);
      // Apply the theme
      this.applyTheme(savedTheme);
    } else {
      // Apply the default theme from state
      this.applyTheme(stateManager.getState().theme);
    }

    // Listen for theme changes from the parent page via localStorage
    window.addEventListener('storage', (event) => {
      if (event.key === 'theme') {
        const newTheme = event.newValue;
        if (newTheme && (newTheme === 'light' || newTheme === 'dark')) {
          console.log('Apigee theme.js: Theme changed via localStorage to:', newTheme);
          this.applyTheme(newTheme);
          stateManager.setTheme(newTheme);
        }
      }
    });
  }

  /**
   * Handle theme switch change
   * This method is kept for compatibility but won't be used since the theme switch is hidden
   * @param {Event} event - The change event
   */
  handleThemeChange(event) {
    // This method is kept for compatibility but won't be used
    // since we're hiding the local theme switch
    const newTheme = event.target.checked ? 'dark' : 'light';
    console.log('Apigee theme.js: Theme switch changed to:', newTheme);

    // Apply the theme locally
    this.applyTheme(newTheme);

    // Update the state manager
    stateManager.setTheme(newTheme);
  }

  /**
   * Apply the theme to the document
   * @param {string} theme - The theme to apply ('light' or 'dark')
   */
  applyTheme(theme) {
    console.log('Apigee theme.js: Applying theme:', theme);

    if (theme === 'dark') {
      document.body.classList.add('dark-theme');
    } else {
      document.body.classList.remove('dark-theme');
    }

    // Update any theme switch that might be in the document (even though it's hidden)
    const themeSwitch = document.getElementById('themeSwitch');
    if (themeSwitch) {
      themeSwitch.checked = theme === 'dark';
    }
  }
}

// Create a singleton instance
export const themeManager = new ThemeManager();
