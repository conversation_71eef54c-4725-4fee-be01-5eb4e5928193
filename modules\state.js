/**
 * State Manager for Apigee Log Processor
 * Centralizes application state and provides methods to update it
 */
export class StateManager {
  constructor() {
    // Initialize with default state
    this.state = {
      entries: [],
      currentViewType: 'flows',
      currentMode: 'file', // 'file' or 'opensearch'
      // Add pagination state
      pagination: {
        pageSize: 50,         // Default page size
        currentPage: 1,       // Current page
        pageSizeOptions: [25, 50, 100, 200] // Available page size options
      },
      filterState: {
        flows: {
          selectedMessageIds: [],
          selectedFlows: [],
          selectedStatusCodes: [],
          allMessageIds: true,
          allFlows: true,
          allStatusCodes: true,
          searchTerm: '',
          isGlobalSearch: false
        },
        calls: {
          selectedMessageIds: [],
          selectedFlows: [],
          selectedStatusCodes: [],
          allMessageIds: true,
          allFlows: true,
          allStatusCodes: true,
          searchTerm: '',
          isGlobalSearch: false
        }
      },
      columnVisibilityState: {
        flows: {
          'allColumns': true,
          'selectedColumns': [],
          'time': true,
          'env-org': true,
          'message-id': true,
          'flow': true,
          'app-name': true,
          'uri': true,
          'status-code': true
        },
        calls: {
          'allColumns': true,
          'selectedColumns': [],
          'time': true,
          'env-org': true,
          'message-id': true,
          'method': true,
          'uri': true,
          'response-time': true,
          'status-code': true
        }
      },
      isMasked: true,
      maskSensitiveData: true,
      processedContent: '',
      isFormattedForOpenSearch: false,
      // OpenSearch specific state
      opensearchConfig: {
        endpoint: '',
        indexPattern: '',
        username: '',
        password: '',
        connected: false,
        lastConnectionTest: null
      },
      opensearchResults: [],
      opensearchQuery: '',
      opensearchTotalHits: 0,
      opensearchTimeline: {
        buckets: [],
        selectedRange: null,
        totalHits: 0
      }
    };

    this.listeners = [];

    // Load saved preferences from localStorage
    this.loadSavedPreferences();
  }

  /**
   * Load saved preferences from localStorage
   */
  loadSavedPreferences() {
    try {
      // Load theme preference
      const savedTheme = localStorage.getItem('theme');
      if (savedTheme) {
        this.state.theme = savedTheme;
      } else {
        // Use system preference as default
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        this.state.theme = prefersDark ? 'dark' : 'light';
      }

      // Load view type preference
      const savedViewType = localStorage.getItem('preferredViewType');
      if (savedViewType) {
        this.state.currentViewType = savedViewType;
      }

      // Load pagination preferences
      const savedPagination = localStorage.getItem('pagination');
      if (savedPagination) {
        const parsedPagination = JSON.parse(savedPagination);
        if (parsedPagination.pageSize) {
          this.state.pagination.pageSize = parsedPagination.pageSize;
        }
      }

      // Load column visibility preferences
      const savedVisibility = localStorage.getItem('columnVisibility');
      if (savedVisibility) {
        const parsedVisibility = JSON.parse(savedVisibility);
        if (parsedVisibility.flows) {
          Object.assign(this.state.columnVisibilityState.flows, parsedVisibility.flows);
        }
        if (parsedVisibility.calls) {
          Object.assign(this.state.columnVisibilityState.calls, parsedVisibility.calls);
        }
      }
    } catch (error) {
      console.error('Error loading saved preferences:', error);
    }
  }

  /**
   * Get the current state
   * @returns {Object} The current state
   */
  getState() {
    return this.state;
  }

  /**
   * Update the state
   * @param {Object} newState - The new state to merge with the current state
   */
  setState(newState) {
    this.state = { ...this.state, ...newState };
    this.notifyListeners();
  }

  /**
   * Update a specific part of the state
   * @param {string} key - The key to update
   * @param {*} value - The new value
   */
  updateState(key, value) {
    this.state[key] = value;
    this.notifyListeners();
  }

  /**
   * Update filter state for a specific view
   * @param {string} viewType - The view type ('flows' or 'calls')
   * @param {Object} filterState - The new filter state
   */
  updateFilterState(viewType, filterState) {
    // Check if we're changing a filter that affects the displayed entries
    const isFilterChange = Object.keys(filterState).some(key =>
      ['selectedMessageIds', 'selectedFlows', 'selectedStatusCodes',
       'allMessageIds', 'allFlows', 'allStatusCodes', 'searchTerm'].includes(key)
    );

    // Update the filter state
    this.state.filterState[viewType] = { ...this.state.filterState[viewType], ...filterState };

    // Reset pagination to page 1 when filters change
    if (isFilterChange) {
      this.state.pagination.currentPage = 1;
    }

    this.notifyListeners();
  }

  /**
   * Update column visibility for a specific view
   * @param {string} viewType - The view type ('flows' or 'calls')
   * @param {string} column - The column name
   * @param {boolean} isVisible - Whether the column is visible
   * @param {Array} selectedColumns - Optional array of selected columns
   */
  updateColumnVisibility(viewType, column, isVisible, selectedColumns) {
    // If updating the "allColumns" property
    if (column === 'allColumns') {
      this.state.columnVisibilityState[viewType]['allColumns'] = isVisible;

      // If selectedColumns is provided, update it
      if (selectedColumns !== undefined) {
        this.state.columnVisibilityState[viewType]['selectedColumns'] = selectedColumns;
      }

      // If allColumns is true, show all columns
      if (isVisible) {
        // Get all column names except 'allColumns' and 'selectedColumns'
        const columnNames = Object.keys(this.state.columnVisibilityState[viewType])
          .filter(key => key !== 'allColumns' && key !== 'selectedColumns');

        // Set all columns to visible
        columnNames.forEach(col => {
          this.state.columnVisibilityState[viewType][col] = true;
        });
      } else if (selectedColumns && selectedColumns.length > 0) {
        // If allColumns is false and we have selected columns, show only those
        const columnNames = Object.keys(this.state.columnVisibilityState[viewType])
          .filter(key => key !== 'allColumns' && key !== 'selectedColumns');

        // Set visibility based on whether the column is in selectedColumns
        columnNames.forEach(col => {
          this.state.columnVisibilityState[viewType][col] = selectedColumns.includes(col);
        });
      }
    } else {
      // Regular column update
      this.state.columnVisibilityState[viewType][column] = isVisible;

      // Update selectedColumns if needed
      if (isVisible && !this.state.columnVisibilityState[viewType]['allColumns']) {
        // Add to selectedColumns if not already there
        if (!this.state.columnVisibilityState[viewType]['selectedColumns'].includes(column)) {
          this.state.columnVisibilityState[viewType]['selectedColumns'].push(column);
        }
      } else if (!isVisible && !this.state.columnVisibilityState[viewType]['allColumns']) {
        // Remove from selectedColumns
        this.state.columnVisibilityState[viewType]['selectedColumns'] =
          this.state.columnVisibilityState[viewType]['selectedColumns'].filter(col => col !== column);
      }
    }

    // Save to localStorage
    localStorage.setItem('columnVisibility', JSON.stringify(this.state.columnVisibilityState));

    this.notifyListeners();
  }

  /**
   * Set the current view type
   * @param {string} viewType - The view type ('flows' or 'calls')
   */
  setViewType(viewType) {
    this.state.currentViewType = viewType;

    // Save to localStorage
    localStorage.setItem('preferredViewType', viewType);

    // Dispatch a custom event to notify that the view type has changed
    const event = new CustomEvent('viewTypeChanged', {
      detail: { viewType }
    });
    document.dispatchEvent(event);

    this.notifyListeners();
  }

  /**
   * Set the theme
   * @param {string} theme - The theme ('light' or 'dark')
   */
  setTheme(theme) {
    this.state.theme = theme;

    // Save to localStorage
    localStorage.setItem('theme', theme);

    this.notifyListeners();
  }

  /**
   * Set the entries
   * @param {Array} entries - The log entries
   */
  setEntries(entries) {
    this.state.entries = entries;
    this.notifyListeners();
  }

  /**
   * Set pagination options
   * @param {Object} paginationOptions - The pagination options to update
   */
  setPagination(paginationOptions) {
    this.state.pagination = { ...this.state.pagination, ...paginationOptions };

    // Save to localStorage
    localStorage.setItem('pagination', JSON.stringify(this.state.pagination));

    this.notifyListeners();
  }

  /**
   * Get paginated entries based on current filters and pagination settings
   * @returns {Array} The paginated entries
   */
  getPaginatedEntries() {
    const allVisibleEntries = this.getFilteredEntries();
    const { currentPage, pageSize } = this.state.pagination;

    // Calculate start and end indices
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;

    // Return the slice of entries for the current page
    return allVisibleEntries.slice(startIndex, endIndex);
  }

  /**
   * Get total number of pages based on filtered entries and page size
   * @returns {number} The total number of pages
   */
  getTotalPages() {
    const { currentViewType, pagination } = this.state;
    const { pageSize } = pagination;

    if (currentViewType === 'calls') {
      // For calls view, we need to count the number of unique message IDs
      // since we group entries by message ID
      const allFilteredEntries = this.getFilteredEntries();

      // Get unique message IDs
      const uniqueMessageIds = new Set();
      allFilteredEntries.forEach(entry => {
        if (entry.messageId) {
          uniqueMessageIds.add(entry.messageId);
        }
      });

      // Calculate pages based on unique message IDs
      return Math.ceil(uniqueMessageIds.size / pageSize);
    } else {
      // For flows view, use the total number of filtered entries
      const allFilteredEntries = this.getFilteredEntries();
      return Math.ceil(allFilteredEntries.length / pageSize);
    }
  }

  /**
   * Get visible entries based on current filters
   * This is now an alias for getFilteredEntries for backward compatibility
   * @returns {Array} The visible entries
   */
  getVisibleEntries() {
    return this.getFilteredEntries();
  }

  /**
   * Get filtered entries based on current filters
   * @returns {Array} The filtered entries
   */
  getFilteredEntries() {
    const { entries, currentViewType, filterState } = this.state;
    const currentFilters = filterState[currentViewType];

    // Helper function to check if an entry matches the search term
    const matchesSearchTerm = (entry) => {
      if (!currentFilters.searchTerm) return true;

      const searchTerm = currentFilters.searchTerm.toLowerCase();

      // Parse search terms with AND/OR operators
      const parsedTerms = parseSearchTerms(searchTerm);

      if (currentFilters.isGlobalSearch) {
        // Global search: Check if any field contains the search term
        if (parsedTerms.type === 'AND') {
          // For AND search, all terms must be found somewhere in the entry
          return parsedTerms.terms.every(term => {
            return Object.values(entry).some(value => {
              if (typeof value === 'string') {
                return value.toLowerCase().includes(term);
              }
              return false;
            });
          });
        } else {
          // For OR search or simple search, at least one field must match
          return Object.values(entry).some(value => {
            if (typeof value === 'string') {
              return matchesSearchTerms(value.toLowerCase(), parsedTerms);
            }
            return false;
          });
        }
      } else {
        // Message body search only
        if (entry.messageBody && typeof entry.messageBody === 'string') {
          return matchesSearchTerms(entry.messageBody.toLowerCase(), parsedTerms);
        }
        return false;
      }
    };

    // Parse search terms with AND/OR operators
    function parseSearchTerms(searchTerm) {
      // Check if the search term contains AND or OR operators
      const hasAnd = searchTerm.includes(' and ');
      const hasOr = searchTerm.includes(' or ');

      if (hasAnd && !hasOr) {
        // AND search
        const terms = searchTerm.split(' and ').map(term => term.trim()).filter(term => term);
        return { type: 'AND', terms };
      } else if (hasOr && !hasAnd) {
        // OR search
        const terms = searchTerm.split(' or ').map(term => term.trim()).filter(term => term);
        return { type: 'OR', terms };
      } else if (hasAnd && hasOr) {
        // Complex search with both AND and OR - treat as simple search for now
        return { type: 'SIMPLE', terms: [searchTerm] };
      } else {
        // Simple search
        return { type: 'SIMPLE', terms: [searchTerm] };
      }
    }

    // Check if text matches search terms
    function matchesSearchTerms(text, parsedTerms) {
      if (!text || typeof text !== 'string') return false;

      if (parsedTerms.type === 'AND') {
        // All terms must match
        return parsedTerms.terms.every(term => text.includes(term));
      } else if (parsedTerms.type === 'OR') {
        // At least one term must match
        return parsedTerms.terms.some(term => text.includes(term));
      } else {
        // Simple search
        return text.includes(parsedTerms.terms[0]);
      }
    }

    // For the API calls view, we need to handle status code filtering differently
    // because we need to keep all entries for a message ID if any entry has the selected status code
    if (currentViewType === 'calls' && !currentFilters.allStatusCodes && currentFilters.selectedStatusCodes.length > 0) {
      // First, filter by message ID and flow
      const filteredByMessageIdAndFlow = entries.filter(entry => {
        const messageIdMatch = currentFilters.allMessageIds ||
                              currentFilters.selectedMessageIds.includes(entry.messageId);
        const flowMatch = currentFilters.allFlows ||
                         currentFilters.selectedFlows.includes(entry.flow);
        return messageIdMatch && flowMatch;
      });

      // Group entries by message ID
      const messageGroups = {};
      filteredByMessageIdAndFlow.forEach(entry => {
        if (!messageGroups[entry.messageId]) {
          messageGroups[entry.messageId] = [];
        }
        messageGroups[entry.messageId].push(entry);
      });

      // Keep only message groups where at least one entry has the selected status code
      // and matches the search term (if any)
      const filteredEntries = [];
      Object.values(messageGroups).forEach(group => {
        const hasSelectedStatusCode = group.some(entry =>
          entry.statusCode && currentFilters.selectedStatusCodes.includes(entry.statusCode)
        );

        // Check if any entry in the group matches the search term
        const matchesSearch = currentFilters.searchTerm ?
          group.some(entry => matchesSearchTerm(entry)) : true;

        if (hasSelectedStatusCode && matchesSearch) {
          // Include all entries for this message ID, not just the ones that match the search term
          filteredEntries.push(...group);
        }
      });

      return filteredEntries;
    } else if (currentViewType === 'calls' && currentFilters.searchTerm) {
      // For calls view with search term but no status code filtering, we need to keep all entries
      // for a message ID if any entry matches the search term

      // First, filter by message ID and flow
      const filteredByMessageIdAndFlow = entries.filter(entry => {
        const messageIdMatch = currentFilters.allMessageIds ||
                              currentFilters.selectedMessageIds.includes(entry.messageId);
        const flowMatch = currentFilters.allFlows ||
                         currentFilters.selectedFlows.includes(entry.flow);
        const statusCodeMatch = currentFilters.allStatusCodes ||
                               (entry.statusCode && currentFilters.selectedStatusCodes.includes(entry.statusCode));
        return messageIdMatch && flowMatch && statusCodeMatch;
      });

      // Group entries by message ID
      const messageGroups = {};
      filteredByMessageIdAndFlow.forEach(entry => {
        if (!messageGroups[entry.messageId]) {
          messageGroups[entry.messageId] = [];
        }
        messageGroups[entry.messageId].push(entry);
      });

      // Keep only message groups where at least one entry matches the search term
      const filteredEntries = [];
      Object.values(messageGroups).forEach(group => {
        // Check if any entry in the group matches the search term
        const matchesSearch = group.some(entry => matchesSearchTerm(entry));

        if (matchesSearch) {
          // Include all entries for this message ID, not just the ones that match the search term
          filteredEntries.push(...group);
        }
      });

      return filteredEntries;
    } else {
      // For flows view or when not filtering by search term or status code, use the standard filtering
      return entries.filter(entry => {
        const messageIdMatch = currentFilters.allMessageIds ||
                              currentFilters.selectedMessageIds.includes(entry.messageId);
        const flowMatch = currentFilters.allFlows ||
                         currentFilters.selectedFlows.includes(entry.flow);
        const statusCodeMatch = currentFilters.allStatusCodes ||
                               (entry.statusCode && currentFilters.selectedStatusCodes.includes(entry.statusCode));
        const searchMatch = matchesSearchTerm(entry);

        return messageIdMatch && flowMatch && statusCodeMatch && searchMatch;
      });
    }
  }

  /**
   * Subscribe to state changes
   * @param {Function} listener - The listener function
   * @returns {Function} A function to unsubscribe
   */
  subscribe(listener) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  /**
   * Notify all listeners of state changes
   */
  notifyListeners() {
    this.listeners.forEach(listener => listener(this.state));
  }
}

// Create a singleton instance
export const stateManager = new StateManager();
