<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Missing TW SQL</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
            font-size: 16px;
            color: #2d3748;
            line-height: 1.6;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        /* Dark theme styles */
        body.dark-theme {
            background-color: #121212;
            color: #e0e0e0;
        }

        body.dark-theme #inputContainer,
        body.dark-theme .container {
            background-color: #1e1e1e;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        body.dark-theme h2,
        body.dark-theme h3 {
            color: #f5f5f5;
        }

        h2 {
            position: relative;
            padding-bottom: 10px;
            margin-bottom: 5px;
        }

        h2:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background-color: #e60000;
        }

        #inputContainer, .container {
            max-width: 90%;
            text-align: left;
            border-radius: 12px;
            background-color: white;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);
            padding: 5px 30px;
            margin: 0 auto 10px;
            border: 1px solid rgba(0, 0, 0, 0.03);
        }
        .hidden {
            display: none;
        }
        .fileContent {
            margin-top: 20px;
            white-space: pre-wrap;
        }
        .highlight {
            background-color: yellow;
            font-weight: bold;
            display: inline; /* Ensure it doesn't affect layout */
        }
        button {
            margin-right: 10px;
        }
        #buttons {
            margin-top: 20px;
        }
        /* Add this to check if styles are affecting the layout */
        #fileContent span {
            display: inline-block; /* Ensure it doesn't push layout */
        }

        /* Dark theme button styles */
        body.dark-theme .btn-primary {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }

        body.dark-theme .btn-primary:hover {
            background-color: #0b5ed7;
            border-color: #0b5ed7;
            box-shadow: 0 4px 10px rgba(13, 110, 253, 0.35);
        }

        body.dark-theme .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
        }

        body.dark-theme .btn-danger:hover {
            background-color: #bb2d3b;
            border-color: #bb2d3b;
            box-shadow: 0 4px 10px rgba(220, 53, 69, 0.35);
        }

        body.dark-theme .btn-success {
            background-color: #198754;
            border-color: #198754;
        }

        body.dark-theme .btn-success:hover {
            background-color: #157347;
            border-color: #157347;
            box-shadow: 0 4px 10px rgba(25, 135, 84, 0.35);
        }

        body.dark-theme hr {
            border-color: rgba(252, 252, 252, 0.1);
        }

        body.dark-theme .highlight {
            background-color: rgba(255, 255, 0);
            color: #1d1d1d;
        }

        body.dark-theme #fileContent {
            color: #e0e0e0;
        }

        /* Dark theme for toast/modal */
        body.dark-theme .toast {
            background-color: #2d2d2d;
            color: #e0e0e0;
            border-color: #198754 !important;
        }

        body.dark-theme .toast-header {
            background-color: #1e1e1e;
            color: #e0e0e0;
            border-bottom-color: rgba(255, 255, 255, 0.1);
        }

        body.dark-theme .toast-body {
            background-color: #2d2d2d;
            color: #e0e0e0;
        }

        body.dark-theme .btn-close {
            filter: invert(1) grayscale(100%) brightness(200%);
        }
    </style>
</head>
<body>
    <div class="container">
        <h2 class="text-center">Missing TW query</h2>

            <div>
                <button id="getQueryBtn" class="btn btn-primary" onclick="window.replaceDate && window.replaceDate()">Get query with Current Date</button>
                <button id="clearBtn" class="btn btn-danger" onclick="window.clearTwText && window.clearTwText()">Clear All</button>
            </div>

        <hr>

        <div id="sqlSection" class="hidden">
            <div class="text-center">
                <h3>Processed SQL</h3>
            </div>

            <div id="buttons">
                <!-- <button id="downloadButton" class="btn btn-success">Download Processed SQL</button> -->
                <button id="copyButton" class="btn btn-success" onclick="window.copySql && window.copySql()">
                    Copy SQL <i class="bi bi-copy"></i>
                </button>
            </div>

            <pre id="fileContent" class="fileContent"></pre>
        </div>
    </div>

    <!-- Toast Notification -->
    <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
        <div id="liveToast" class="toast border border-2 border-success" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">Notification</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="toastMessage">
                <!-- Toast message will be inserted here -->
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="missingTw.js"></script>
    <script>
        // Apply dark theme if parent page has dark theme
        document.addEventListener('DOMContentLoaded', function() {
            // Check if we're in an iframe/embedded context
            if (window.parent && window.parent !== window) {
                try {
                    // Try to check if parent has dark theme
                    if (window.parent.document.body.classList.contains('dark-theme')) {
                        document.body.classList.add('dark-theme');
                    }
                } catch (e) {
                    // If we can't access parent due to same-origin policy, check localStorage
                    const savedTheme = localStorage.getItem('theme');
                    if (savedTheme === 'dark') {
                        document.body.classList.add('dark-theme');
                    }
                }
            } else {
                // If not in iframe, check localStorage
                const savedTheme = localStorage.getItem('theme');
                if (savedTheme === 'dark') {
                    document.body.classList.add('dark-theme');
                }
            }

            // Add event listeners for buttons
            document.getElementById('getQueryBtn').addEventListener('click', function() {
                window.replaceDate();
            });

            document.getElementById('clearBtn').addEventListener('click', function() {
                window.clearTwText();
            });

            document.getElementById('copyButton').addEventListener('click', function() {
                window.copySql();
            });
        });

        // Listen for theme changes from parent
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'themeChange') {
                if (event.data.theme === 'dark') {
                    document.body.classList.add('dark-theme');
                } else {
                    document.body.classList.remove('dark-theme');
                }
            }
        });
    </script>
</body>
</html>
