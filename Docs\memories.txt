# General Requirements
- Maintain current UI design.
- Don't break existing functionality.
- Keep the HTML/CSS/JavaScript structure without server implementation changes.
- Implement only the Phase 1 improvements (refactoring and modularization).
- The offcanvas implementation must exactly match the reference code's UI design and element display.
- Follow reference code implementations exactly when available rather than creating custom solutions.
- Implement performance optimizations including lazy loading for large log files, optimized parsing algorithms, pagination for large result sets, and virtual scrolling for offcanvas messagebody data.
- The apigee log processor needs to be extended to handle multiple different log formats found in the Test_apigee_new_structure folder.
- The current log processing implementation is in the modules folder, not in apigeeLogProcessor.js which is old and not in use.
- Add support for .csv and .log file formats as input types for the log processor.
- The log processor incorrectly identifies old log formats as 'csv_with_path' and fails to display content despite successful processing completion messages in the console.
- User wants to integrate OpenSearch with the Apigee tool to enable direct log searching from OpenSearch.
- User's OpenSearch environment uses default OpenSearch web UI with Active Directory user login authentication.
- User has basic (non-admin) access to OpenSearch, cannot modify CORS settings or server configuration, requiring proxy server approach for browser integration.
- User prefers the Apigee log processor input section to be collapsible and automatically collapse after processing to save screen space.
- Create detailed architecture documentation for components to help others understand the system.

# Code Style
- Maintain separation of concerns by keeping CSS in dedicated files rather than inline HTML.
- Preserve original UI design elements when refactoring.
- Keep original flow names (PROXY_REQ_FLOW, TARGET_REQ_FLOW, etc.) instead of using more descriptive names.
- User prefers simpler masking processes with fewer redundant masking operations in the code.
- The apigeeLogProcessor.js file should not be modified as it's not in use in the current implementation.
- Use existing CSS files and styling conventions rather than creating new ones, particularly for header key formatting.
- The Before_new_structure folder contains the original implementation with working HTTP header formatting that should be used as reference when fixing formatting issues.
- Restore the original HTTP header format function from Before_new_structure folder without breaking the new log format processing by separating old and new formatting logic.
- User prefers a single centralized theme selector in index.html rather than duplicate theme selectors in individual pages.
- User prefers consistent UI behavior with up/down arrows for collapsible sections across different components.

# API Call View Offcanvas Behavior
- In API call view offcanvas, proxy req and proxy resp should be open by default; if no proxy resp exists, target resp should be open; and multiple target requests should display separately with the one containing status code opened.
- In the offcanvas pre section, message-id, uri, and requestid elements should be clickable to copy their values.
- Empty query strings should not display '-' in the offcanvas but should show nothing, similar to empty message bodies.
- By default, client ID in the pre section and auth options in the http header section should be masked, with a toggle button in the offcanvas header to show/hide this sensitive information, and the copy function should respect the masked state.
- When implementing masking/unmasking functionality, ensure original unmodified data is shown when unmasking, following the reference code implementation.
- Message bodies should be displayed and copied in their original raw format without any modification, formatting or processing.
- Message bodies in log files are closed with a pattern 'Row: X, Column: Y:' where X and Y are changing numbers. Test data can be found in the Tesd_data/ directory with filenames starting with 'apigee_'.
- When using the extract message ID function, the view selector button should be hidden and the copy and format to OpenSearch buttons should be shown on the left side of the log-entry header.
- Add an export button to the offcanvas header next to the copy button that exports log details to a text file, with styling matching the copy and mask buttons.
- When a user clicks on the view selector, any open dropdowns (flow, column, export) should automatically close.
- In the offcanvas API flow view, the message body container should not have a border, and virtual scrolling implementation should properly display message bodies in the API call view.
- When processing new log formats, don't reformat HTTP headers that are already correctly formatted, just colorize key parts, and convert double quotes to single quotes in the pre section and message body display.

# Log Export
- When exporting logs to TXT, HTTP headers should always be masked by default.
- When exporting logs to TXT, the functionality should export the logs based on the current filtering, not just create a text file from the tables.
- The offcanvas export should respect the current masking status just like the copy function does.
- Export to XLSX/TXT should respect search filters, and offcanvas navigation should only show filtered entries.

# Column Selector
- In the column selector functionality, all checkboxes should be checked by default; unchecking an individual column should uncheck the 'all' option and hide only that column while keeping others visible; clicking the 'all' option should check all checkboxes again.

# Apigee Log Processor API Calls View
- In the Apigee log processor API calls view, add a status code filter dropdown between the messageid selector and column selector, with the same functionality and design as the messageid selector, visible only for the API call view.
- In dropdown lists, status codes should be displayed as plain text without special styling, and status code colors should be consistent between the table and offcanvas header.

# Search Functionality
- The search function should search only in message bodies by default, and when the global checkbox is checked, it should search everywhere.