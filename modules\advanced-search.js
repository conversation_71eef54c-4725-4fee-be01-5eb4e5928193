/**
 * Advanced Search Manager for OpenSearch
 */
export class AdvancedSearchManager {
  constructor() {
    this.modal = null;
    this.advancedSearchButton = null;
    this.executeButton = null;
    this.clearButton = null;
  }

  /**
   * Initialize advanced search functionality
   */
  init() {
    console.log('Initializing Advanced Search Manager...');
    
    // Get DOM elements
    this.modal = new bootstrap.Modal(document.getElementById('advancedSearchModal'));
    this.advancedSearchButton = document.getElementById('advancedSearchButton');
    this.executeButton = document.getElementById('advancedSearchExecute');
    this.clearButton = document.getElementById('advancedSearchClear');

    if (!this.advancedSearchButton || !this.executeButton) {
      console.warn('Advanced search elements not found');
      return;
    }

    // Set up event listeners
    this.setupEventListeners();

    console.log('Advanced Search Manager initialized');
  }

  /**
   * Set up event listeners
   */
  setupEventListeners() {
    // Open modal
    this.advancedSearchButton.addEventListener('click', () => {
      this.openModal();
    });

    // Execute search
    this.executeButton.addEventListener('click', () => {
      this.executeAdvancedSearch();
    });

    // Clear form
    if (this.clearButton) {
      this.clearButton.addEventListener('click', () => {
        this.clearForm();
      });
    }
  }

  /**
   * Open the advanced search modal
   */
  openModal() {
    this.modal.show();
  }

  /**
   * Execute advanced search
   */
  executeAdvancedSearch() {
    const query = this.buildAdvancedQuery();
    
    if (!query) {
      alert('Please fill in at least one search field');
      return;
    }

    // Close modal
    this.modal.hide();

    // Set the query in the main search input
    const mainSearchInput = document.getElementById('opensearchQuery');
    if (mainSearchInput) {
      mainSearchInput.value = query;
    }

    // Trigger the search
    const searchEvent = new Event('click');
    const searchButton = document.getElementById('opensearchSearchButton');
    if (searchButton) {
      searchButton.dispatchEvent(searchEvent);
    }
  }

  /**
   * Build advanced search query
   * @returns {string} OpenSearch query string
   */
  buildAdvancedQuery() {
    const fields = {
      messageId: document.getElementById('advMessageId')?.value?.trim(),
      uri: document.getElementById('advUri')?.value?.trim(),
      statusCode: document.getElementById('advStatusCode')?.value?.trim(),
      httpMethod: document.getElementById('advHttpMethod')?.value?.trim(),
      flow: document.getElementById('advFlow')?.value?.trim(),
      messageBody: document.getElementById('advMessageBody')?.value?.trim(),
      clientId: document.getElementById('advClientId')?.value?.trim(),
      appName: document.getElementById('advAppName')?.value?.trim()
    };

    const options = {
      exactMatch: document.getElementById('advExactMatch')?.checked,
      caseSensitive: document.getElementById('advCaseSensitive')?.checked
    };

    // Build query parts
    const queryParts = [];

    // Field-specific queries
    if (fields.messageId) {
      queryParts.push(`trace.messageid:"${fields.messageId}"`);
    }

    if (fields.uri) {
      if (options.exactMatch) {
        queryParts.push(`trace.uri:"${fields.uri}"`);
      } else {
        queryParts.push(`trace.uri:*${fields.uri}*`);
      }
    }

    if (fields.statusCode) {
      queryParts.push(`trace.statuscode:"${fields.statusCode}"`);
    }

    if (fields.httpMethod) {
      queryParts.push(`trace.verb:"${fields.httpMethod}"`);
    }

    if (fields.flow) {
      queryParts.push(`trace.flow:"${fields.flow}"`);
    }

    if (fields.clientId) {
      if (options.exactMatch) {
        queryParts.push(`trace.client_id:"${fields.clientId}"`);
      } else {
        queryParts.push(`trace.client_id:*${fields.clientId}*`);
      }
    }

    if (fields.appName) {
      if (options.exactMatch) {
        queryParts.push(`trace.app_name:"${fields.appName}"`);
      } else {
        queryParts.push(`trace.app_name:*${fields.appName}*`);
      }
    }

    // Message body search (searches in message and event.original fields)
    if (fields.messageBody) {
      if (options.exactMatch) {
        queryParts.push(`(message:"${fields.messageBody}" OR event.original:"${fields.messageBody}")`);
      } else {
        queryParts.push(`(message:*${fields.messageBody}* OR event.original:*${fields.messageBody}*)`);
      }
    }

    // Join with AND
    const query = queryParts.join(' AND ');

    console.log('Advanced search query:', query);
    return query;
  }

  /**
   * Clear the advanced search form
   */
  clearForm() {
    // Clear all input fields
    const inputs = [
      'advMessageId', 'advUri', 'advStatusCode', 'advMessageBody', 
      'advClientId', 'advAppName'
    ];

    inputs.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.value = '';
      }
    });

    // Reset selects
    const selects = ['advHttpMethod', 'advFlow'];
    selects.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.selectedIndex = 0;
      }
    });

    // Reset checkboxes
    const checkboxes = ['advExactMatch', 'advCaseSensitive'];
    checkboxes.forEach(id => {
      const element = document.getElementById(id);
      if (element) {
        element.checked = false;
      }
    });

    console.log('Advanced search form cleared');
  }

  /**
   * Populate form from existing query (if possible)
   * @param {string} query - Existing query to parse
   */
  populateFromQuery(query) {
    // This could be implemented to parse existing queries
    // and populate the form fields accordingly
    console.log('Populating form from query:', query);
  }
}

// Create and export singleton instance
export const advancedSearchManager = new AdvancedSearchManager();
