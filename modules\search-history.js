/**
 * Search History Manager for OpenSearch queries
 */
export class SearchHistoryManager {
  constructor() {
    this.maxHistoryItems = 15;
    this.storageKey = 'opensearch_history';
    this.historyButton = null;
    this.historyDropdown = null;
    this.historyList = null;
    this.clearButton = null;
    this.searchInput = null;
    this.recentlySelected = false;
  }

  /**
   * Initialize search history functionality
   */
  init() {
    console.log('Initializing Search History Manager...');
    
    // Get DOM elements
    this.historyButton = document.getElementById('searchHistoryButton');
    this.historyDropdown = document.getElementById('searchHistoryDropdown');
    this.historyList = document.getElementById('searchHistoryList');
    this.clearButton = document.getElementById('clearSearchHistory');
    this.searchInput = document.getElementById('opensearchQuery');

    if (!this.historyButton || !this.historyDropdown || !this.historyList) {
      console.warn('Search history elements not found');
      return;
    }

    // Set up event listeners
    this.setupEventListeners();
    
    // Load and display history
    this.updateHistoryDisplay();

    console.log('Search History Manager initialized');
  }

  /**
   * Set up event listeners
   */
  setupEventListeners() {
    // Toggle history dropdown
    this.historyButton.addEventListener('click', (e) => {
      e.stopPropagation();
      this.toggleHistoryDropdown();
    });

    // Clear history
    if (this.clearButton) {
      this.clearButton.addEventListener('click', (e) => {
        e.stopPropagation();
        this.clearHistory();
      });
    }

    // Close dropdown when clicking outside
    document.addEventListener('click', (e) => {
      if (this.historyDropdown.classList.contains('show')) {
        const searchContainer = document.querySelector('.search-input-container');
        if (!searchContainer.contains(e.target)) {
          this.hideHistoryDropdown();
        }
      }
    });

    // Handle search input changes
    if (this.searchInput) {
      // Show history on focus (only if not recently closed by item selection)
      this.searchInput.addEventListener('focus', () => {
        if (!this.recentlySelected) {
          setTimeout(() => {
            this.showHistoryDropdown();
          }, 100);
        }
      });

      // Clear the recently selected flag when user starts typing
      this.searchInput.addEventListener('input', () => {
        this.recentlySelected = false;
      });

      // Hide on Enter
      this.searchInput.addEventListener('keydown', (e) => {
        if (e.key === 'Enter') {
          this.hideHistoryDropdown();
        }
      });
    }
  }

  /**
   * Add search query to history
   * @param {string} query - Search query to add
   */
  addToHistory(query) {
    if (!query || !query.trim()) {
      return;
    }

    const trimmedQuery = query.trim();
    let history = this.getHistory();

    // Remove if already exists (to move to top)
    history = history.filter(item => item !== trimmedQuery);

    // Add to beginning
    history.unshift(trimmedQuery);

    // Limit to max items
    if (history.length > this.maxHistoryItems) {
      history = history.slice(0, this.maxHistoryItems);
    }

    // Save to localStorage
    this.saveHistory(history);

    // Update display
    this.updateHistoryDisplay();
  }

  /**
   * Get search history from localStorage
   * @returns {Array} Array of search queries
   */
  getHistory() {
    try {
      const stored = localStorage.getItem(this.storageKey);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error loading search history:', error);
      return [];
    }
  }

  /**
   * Save search history to localStorage
   * @param {Array} history - Array of search queries
   */
  saveHistory(history) {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(history));
    } catch (error) {
      console.error('Error saving search history:', error);
    }
  }

  /**
   * Clear search history
   */
  clearHistory() {
    try {
      localStorage.removeItem(this.storageKey);
      this.updateHistoryDisplay();
      this.hideHistoryDropdown();
      console.log('Search history cleared');
    } catch (error) {
      console.error('Error clearing search history:', error);
    }
  }

  /**
   * Update history display
   */
  updateHistoryDisplay() {
    if (!this.historyList) return;

    const history = this.getHistory();

    if (history.length === 0) {
      this.historyList.innerHTML = '<div class="search-history-empty">No recent searches</div>';
      return;
    }

    // Create history items
    const historyHTML = history.map(query => {
      const escapedQuery = this.escapeHtml(query);
      // Store the original query in a data attribute (base64 encoded to handle quotes)
      const encodedQuery = btoa(query);
      return `<div class="search-history-item" data-query-encoded="${encodedQuery}" title="${escapedQuery}">${escapedQuery}</div>`;
    }).join('');

    this.historyList.innerHTML = historyHTML;

    // Add click listeners to history items
    this.historyList.querySelectorAll('.search-history-item').forEach(item => {
      item.addEventListener('click', (e) => {
        e.stopPropagation();
        // Get the original query from base64 encoded data attribute
        const encodedQuery = e.target.getAttribute('data-query-encoded');
        const query = atob(encodedQuery);
        this.selectHistoryItem(query);
      });
    });
  }

  /**
   * Select a history item
   * @param {string} query - Selected query
   */
  selectHistoryItem(query) {
    // Set flag to prevent auto-reopening on focus
    this.recentlySelected = true;

    if (this.searchInput) {
      this.searchInput.value = query;
      // Don't focus to prevent auto-reopening
    }

    this.hideHistoryDropdown();

    // Clear the flag after a delay
    setTimeout(() => {
      this.recentlySelected = false;
    }, 500);
  }

  /**
   * Toggle history dropdown visibility
   */
  toggleHistoryDropdown() {
    if (this.historyDropdown.classList.contains('show')) {
      this.hideHistoryDropdown();
    } else {
      this.showHistoryDropdown();
    }
  }

  /**
   * Show history dropdown
   */
  showHistoryDropdown() {
    this.updateHistoryDisplay();
    this.historyDropdown.classList.add('show');
  }

  /**
   * Hide history dropdown
   */
  hideHistoryDropdown() {
    this.historyDropdown.classList.remove('show');
  }

  /**
   * Escape HTML to prevent XSS
   * @param {string} text - Text to escape
   * @returns {string} Escaped text
   */
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }
}

// Create and export singleton instance
export const searchHistoryManager = new SearchHistoryManager();
