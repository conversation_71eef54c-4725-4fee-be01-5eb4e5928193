<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebTool</title>
    <link rel="icon" type="image/x-icon" href="src/colosseum.ico">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="embedded-content.css">
    <style>
        /* Additional styling if needed */
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            background-color: rgb(245 245 245);
            padding-top: 56px; /* Height of the navbar */
        }

        #content {
            padding: 0;
            margin: 0;
            position: relative;
        }
		.navbar-nav {
			display: flex;
			align-items: center;
		}

		.navbar-nav .nav-item {
			margin-right: 2px; /* Space between items */
		}

		.navbar-nav .nav-item:last-child {
			margin-right: 0; /* Remove margin from the last item */
		}

		.separator {
			margin: 0 0px; /* Space around the separator */
			color: #ccc; /* Color of the separator */
		}

    /* Ensure navbar stays on top */
    .navbar {
        z-index: 1030;
        background-color: rgb(100, 160, 175) !important; /* Light mode color */
    }

    /* Navbar text color for light mode */
    .navbar-dark .nav-link {
        color: #ffffff !important;
    }

    .navbar-dark .nav-link:hover {
        color: #f0f0f0 !important;
        text-decoration: underline;
    }

    /* Theme switcher styles */
    .theme-switch-wrapper {
        display: flex;
        align-items: center;
    }

    .theme-switch {
        display: inline-block;
        height: 24px;
        position: relative;
        width: 50px;
    }

    .theme-switch input {
        display: none;
    }

    .slider {
        background-color: #ccc;
        bottom: 0;
        cursor: pointer;
        left: 0;
        position: absolute;
        right: 0;
        top: 0;
        transition: .4s;
        border-radius: 34px;
    }

    .slider:before {
        background-color: white;
        bottom: 4px;
        content: "";
        height: 16px;
        left: 4px;
        position: absolute;
        transition: .4s;
        width: 16px;
        border-radius: 50%;
    }

    input:checked + .slider {
        background-color: #e60000;
    }

    input:checked + .slider:before {
        transform: translateX(26px);
    }

    /* Theme switcher styles - fixed icon positioning */
    .slider-icon {
        color: #fff;
        position: absolute;
        transform: translate(-50%, -50%);
        top: 50%;
        pointer-events: none;
    }

    .slider-icon.sun {
        left: 30%;
        opacity: 0;
        transition: opacity 0.4s ease;
    }

    .slider-icon.moon {
        right: 10%;
        left: 70%;
        opacity: 1;
        transition: opacity 0.4s ease;
    }

    input:checked + .slider .slider-icon.sun {
        opacity: 1;
    }

    input:checked + .slider .slider-icon.moon {
        opacity: 0;
    }

    /* Dark theme styles */
    body.dark-theme {
        background-color: #222;
        color: #f5f5f5;
    }

    body.dark-theme .navbar {
        background-color: #272727 !important;
    }

    body.dark-theme .nav-link {
        color: #f5f5f5;
    }

    body.dark-theme .nav-link:hover {
        color: #ffffff;
    }

    body.dark-theme .separator {
        color: #555;
    }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark fixed-top">
        <div class="container-fluid">
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
			<div class="collapse navbar-collapse" id="navbarNav">
				<ul class="navbar-nav me-auto">
					<li class="nav-item">
						<a class="nav-link" href="#" onclick="loadPage('home.html')">Home</a>
					</li>
					<span class="separator">|</span>
					<li class="nav-item">
						<a class="nav-link" href="https://toolbox.mwops.vodafone.hu/" target="_blank">Toolbox</a>
					</li>
					<span class="separator">|</span>
					<li class="nav-item">
						<a class="nav-link" href="#" onclick="loadPage('apigeeLogProcessor.html')">Apigee Log Processor</a>
					</li>
					<span class="separator">|</span>
					<li class="nav-item">
						<a class="nav-link" href="#" onclick="loadPage('okdLogProcessor.html')">OKD Log Processor</a>
					</li>
					<span class="separator">|</span>
					<li class="nav-item">
						<a class="nav-link" href="#" onclick="loadPage('missingTw.html')">Missing TW Query</a>
					</li>
				</ul>
				<!-- Theme Switcher in Navbar -->
				<div class="theme-switch-wrapper">
					<label class="theme-switch" for="navThemeSwitch">
						<input type="checkbox" id="navThemeSwitch" />
						<div class="slider">
							<i class="bi bi-sun-fill slider-icon sun"></i>
							<i class="bi bi-moon-fill slider-icon moon"></i>
						</div>
					</label>
				</div>
			</div>
            <!-- <a class="navbar-brand" href="#" onclick="loadPage('home.html')">WebTool</a> -->
        </div>
    </nav>

    <!-- Content Area -->
    <div class="container-fluid px-4 py-2">
        <div id="content">
            <!-- Dynamic content will be loaded here -->
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- External libraries needed for Apigee Log Processor -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>
    <script>
        // Function to load page content
        function loadPage(page) {
            // Store current theme state before loading new content
            const currentTheme = document.body.classList.contains('dark-theme') ? 'dark' : 'light';
            const contentElement = document.getElementById('content');

            // Show loading indicator
            contentElement.innerHTML = '<div class="text-center mt-5 pt-5"><div class="spinner-border" role="status"></div><p class="mt-2">Loading...</p></div>';

            // Use fetch API instead of jQuery load for better performance
            fetch(page)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.text();
                })
                .then(html => {
                    // Set the content
                    contentElement.innerHTML = html;

                    // After loading new content, ensure the navbar collapses
                    const navbarCollapse = document.querySelector('#navbarNav');
                    const bootstrapCollapse = bootstrap.Collapse.getInstance(navbarCollapse);
                    if (bootstrapCollapse) {
                        bootstrapCollapse.hide();
                    }

                    // Process the loaded content in a deferred way
                    setTimeout(() => {
                        processLoadedContent(page, currentTheme);

                        // Scroll to top when loading a new page
                        window.scrollTo(0, 0);
                    }, 0);
                })
                .catch(error => {
                    contentElement.innerHTML = '<div class="alert alert-danger m-5">Error loading page content</div>';
                    console.error('Error loading page:', error);
                });
        }

        // Process loaded content to ensure proper display
        function processLoadedContent(page, theme = null) {
            // Get content element using native DOM API for better performance
            const contentElement = document.getElementById('content');

            // Get the current theme from localStorage or use the provided theme
            const savedTheme = localStorage.getItem('theme');
            const isDarkTheme = theme ? theme === 'dark' :
                                savedTheme ? savedTheme === 'dark' :
                                document.body.classList.contains('dark-theme');

            console.log('index.html: Processing loaded content for page:', page, 'with theme:', isDarkTheme ? 'dark' : 'light');

            // Ensure the theme is applied consistently
            if (isDarkTheme) {
                document.body.classList.add('dark-theme');

                // Update the theme switch state once
                const navThemeSwitch = document.getElementById('navThemeSwitch');
                if (navThemeSwitch) {
                    navThemeSwitch.checked = true;
                }

                // Add dark-theme class to any body elements in content
                const bodyElements = contentElement.querySelectorAll('body');
                if (bodyElements.length > 0) {
                    bodyElements.forEach(body => body.classList.add('dark-theme'));
                }

                // For Apigee page, ensure its theme switch is also updated
                if (page === 'apigeeLogProcessor.html') {
                    setTimeout(() => {
                        const apigeeThemeSwitch = contentElement.querySelector('#themeSwitch');
                        if (apigeeThemeSwitch) {
                            apigeeThemeSwitch.checked = true;
                        }
                    }, 100); // Small delay to ensure the element is available
                }
            } else {
                document.body.classList.remove('dark-theme');

                // Update the theme switch state once
                const navThemeSwitch = document.getElementById('navThemeSwitch');
                if (navThemeSwitch) {
                    navThemeSwitch.checked = false;
                }

                // Remove dark-theme class from any body elements in content
                const bodyElements = contentElement.querySelectorAll('body');
                if (bodyElements.length > 0) {
                    bodyElements.forEach(body => body.classList.remove('dark-theme'));
                }

                // For Apigee page, ensure its theme switch is also updated
                if (page === 'apigeeLogProcessor.html') {
                    setTimeout(() => {
                        const apigeeThemeSwitch = contentElement.querySelector('#themeSwitch');
                        if (apigeeThemeSwitch) {
                            apigeeThemeSwitch.checked = false;
                        }
                    }, 100); // Small delay to ensure the element is available
                }
            }

            // Hide the theme switcher in all pages since we're using the one in the navbar
            const themeSwitcher = contentElement.querySelector('.theme-switch-wrapper');
            if (themeSwitcher) {
                themeSwitcher.style.display = 'none';
            }

            // Initialize specific page functionality
            if (page === 'apigeeLogProcessor.html') {
                // Initialize Apigee Log Processor
                if (typeof initializeColumnControls === 'function') {
                    initializeColumnControls();
                }

                // Initialize the app if using modules
                if (typeof initializeApp === 'function') {
                    initializeApp();
                } else {
                    // Try to import and initialize if using ES modules
                    try {
                        import('./modules/main.js').then(module => {
                            if (typeof module.initializeApp === 'function') {
                                module.initializeApp();
                            }
                        }).catch(err => console.error('Error importing module:', err));
                    } catch (e) {
                        console.log('Module import not supported or module not found');
                    }
                }
            } else if (page === 'okdLogProcessor.html') {
                // Initialize OKD Log Processor if needed
                // Ensure any scripts are properly loaded
                const script = document.createElement('script');
                script.src = 'okdLogProcessor.js';
                document.body.appendChild(script);

                // We need to ensure the toggle button functionality is properly initialized
                // when the OKD Log Processor is loaded from index.html
                setTimeout(() => {
                    // This will be called after the OKD Log Processor script has been loaded
                    if (typeof setupToggleInputButton === 'function') {
                        setupToggleInputButton();
                        console.log('Toggle button initialized from index.html');
                    }
                }, 1000); // Longer delay to ensure the script has loaded and initialized
            } else if (page === 'missingTw.html') {
                // Initialize Missing TW Query page
                // Ensure the script is properly loaded
                const script = document.createElement('script');
                script.src = 'missingTw.js';
                document.body.appendChild(script);
            }
        }

        // Event handlers for navbar links

        // Ensure the navbar toggler collapses the menu when clicked
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', () => {
                const navbarCollapse = document.querySelector('#navbarNav');
                const bootstrapCollapse = bootstrap.Collapse.getInstance(navbarCollapse);
                if (bootstrapCollapse) {
                    bootstrapCollapse.hide();
                }
            });
        });

        // Ensure the navbar collapses when clicking outside
        document.addEventListener('click', (event) => {
            const target = event.target;
            const navbarCollapse = document.querySelector('#navbarNav');
            const toggler = document.querySelector('.navbar-toggler');

            // Check if the click is outside the navbar and the navbar is open
            if (!navbarCollapse.contains(target) && !toggler.contains(target)) {
                const bootstrapCollapse = bootstrap.Collapse.getInstance(navbarCollapse);
                if (bootstrapCollapse && navbarCollapse.classList.contains('show')) {
                    bootstrapCollapse.hide();
                }
            }
        });

        // Theme switching functionality
        function initTheme() {
            const themeSwitch = document.getElementById('navThemeSwitch');
            if (!themeSwitch) return;

            // Check for saved theme preference or use system preference
            const savedTheme = localStorage.getItem('theme');
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;

            // Determine the theme to apply
            const themeToApply = savedTheme || (prefersDark ? 'dark' : 'light');

            // Apply the theme
            applyTheme(themeToApply);

            // Set the switch state
            themeSwitch.checked = themeToApply === 'dark';

            // Add event listener for theme switch
            themeSwitch.addEventListener('change', function() {
                const newTheme = this.checked ? 'dark' : 'light';
                applyTheme(newTheme);
                localStorage.setItem('theme', newTheme);
            });

            // Listen for system theme changes
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
                if (!localStorage.getItem('theme')) {
                    // Only auto-switch if user hasn't explicitly set a preference
                    const newTheme = e.matches ? 'dark' : 'light';
                    applyTheme(newTheme);
                    themeSwitch.checked = e.matches;
                }
            });
        }

        // Apply theme to all elements
        function applyTheme(theme) {
            const isDark = theme === 'dark';
            console.log('index.html: Applying theme:', theme);

            // Save theme to localStorage
            localStorage.setItem('theme', theme);

            // Apply to main document
            if (isDark) {
                document.body.classList.add('dark-theme');
            } else {
                document.body.classList.remove('dark-theme');
            }

            // Apply to content frame
            const contentFrame = document.getElementById('content');
            if (contentFrame) {
                // Apply to any body elements in the content frame
                const contentBodies = contentFrame.querySelectorAll('body');
                contentBodies.forEach(body => {
                    if (isDark) {
                        body.classList.add('dark-theme');
                    } else {
                        body.classList.remove('dark-theme');
                    }
                });

                // Update any theme switches in the content
                const contentThemeSwitches = contentFrame.querySelectorAll('input[type="checkbox"]#themeSwitch');
                contentThemeSwitches.forEach(switch_ => {
                    switch_.checked = isDark;

                    // If this is the Apigee theme switch, also update its state manager
                    if (switch_.closest('.theme-switch-wrapper') &&
                        contentFrame.querySelector('h2') &&
                        contentFrame.querySelector('h2').textContent.includes('Apigee Log Processor')) {
                        console.log('index.html: Updating Apigee theme switch to:', isDark ? 'dark' : 'light');

                        // Try to trigger the change event to update Apigee's state manager
                        try {
                            // Only trigger if the current state is different
                            if (switch_.checked !== isDark) {
                                switch_.checked = isDark;
                                const changeEvent = new Event('change');
                                switch_.dispatchEvent(changeEvent);
                            }
                        } catch (e) {
                            console.log('Could not trigger change event on Apigee theme switch:', e);
                        }
                    }
                });

                // Try to communicate with iframe content if present
                try {
                    // Get all iframes in the content
                    const iframes = contentFrame.querySelectorAll('iframe');
                    iframes.forEach(iframe => {
                        if (iframe.contentWindow) {
                            iframe.contentWindow.postMessage({
                                type: 'themeChange',
                                theme: theme
                            }, '*');
                        }
                    });

                    // If content has its own window object (loaded via jQuery load)
                    if (contentFrame.contentWindow) {
                        contentFrame.contentWindow.postMessage({
                            type: 'themeChange',
                            theme: theme
                        }, '*');
                    }
                } catch (e) {
                    console.log('Could not communicate with iframe content:', e);
                }
            }

            // Update the navbar theme switch
            const navThemeSwitch = document.getElementById('navThemeSwitch');
            if (navThemeSwitch) {
                navThemeSwitch.checked = isDark;
            }
        }

        // Initialize theme and load home page when the document is ready
        // Use DOMContentLoaded instead of jQuery ready for better performance
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize theme first
            initTheme();

            // Then load the home page
            setTimeout(function() {
                loadPage('home.html');
            }, 0); // Use setTimeout to defer non-critical operations
        });

        // Re-apply theme when the window is focused
        window.addEventListener('focus', function() {
            // Get the current theme from localStorage
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme) {
                // Apply the saved theme
                applyTheme(savedTheme);
            }
        });
    </script>
</body>
</html>
