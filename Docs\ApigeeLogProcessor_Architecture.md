# Apigee Log Processor Architecture Documentation

## Table of Contents
1. [Overview](#overview)
2. [System Architecture](#system-architecture)
3. [Module Structure](#module-structure)
4. [Data Flow](#data-flow)
5. [Log Format Handling](#log-format-handling)
6. [Timestamp Processing](#timestamp-processing)
7. [UI Components](#ui-components)
8. [State Management](#state-management)
9. [Search and Filtering](#search-and-filtering)
10. [Export Functionality](#export-functionality)
11. [Log Entry Structure](#log-entry-structure)
12. [HTTP Header Processing](#http-header-processing)
13. [Message Body Handling](#message-body-handling)
14. [Masking Sensitive Data](#masking-sensitive-data)
15. [Response Time Calculation](#response-time-calculation)
16. [API Call Aggregation](#api-call-aggregation)
17. [Error Handling](#error-handling)
18. [Performance Optimizations](#performance-optimizations)
19. [Integration Points](#integration-points)
20. [Future Enhancements](#future-enhancements)

## Overview

The Apigee Log Processor is a client-side web application designed to parse, process, and visualize Apigee API proxy logs in various formats. It provides a user-friendly interface for analyzing API calls, viewing request and response details, and troubleshooting API issues.

### Key Features
- Support for multiple log formats (standard, CSV with path, simple message, raw JSON)
- Flow view and API call view for different analysis perspectives
- Detailed offcanvas view for inspecting individual API calls
- Search functionality across log entries
- Filtering by message ID, flow type, and status code
- Export capabilities (TXT, XLSX)
- Masking of sensitive information
- Responsive UI with dark/light theme support

## System Architecture

The Apigee Log Processor follows a modular architecture with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                      User Interface                         │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ Input Panel │  │ Results View│  │ Offcanvas Detail    │  │
│  │             │  │ (Table/Flow)│  │ View                │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└───────────────────────────┬─────────────────────────────────┘
                            │
┌───────────────────────────▼─────────────────────────────────┐
│                     Core Modules                            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ Processor   │  │ UI Manager  │  │ State Manager       │  │
│  │             │◄─┼─────────────┼─►│                     │  │
│  └──────┬──────┘  └─────────────┘  └─────────────────────┘  │
│         ▼                                     ▲             │
│  ┌─────────────┐  ┌─────────────┐  ┌──────────┴──────────┐  │
│  │ Search      │  │ Export      │  │ Utilities           │  │
│  │             │  │             │  │                     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### Client-Side Processing
The application runs entirely in the browser with no server-side processing. All log parsing, analysis, and visualization happen on the client side using JavaScript.

## Module Structure

The application is organized into the following key modules:

### 1. `processor.js`
The core log processing engine responsible for:
- Detecting log formats
- Extracting data from raw logs
- Parsing and structuring log entries
- Formatting timestamps and HTTP headers
- Calculating response times

### 2. `main.js`
The application entry point that:
- Initializes the application
- Sets up event listeners
- Coordinates between modules
- Handles file uploads and text input

### 3. `ui.js`
Manages the user interface:
- Renders log entries in tables
- Builds the flow view visualization
- Creates and populates the offcanvas detail view
- Handles UI interactions and animations

### 4. `state.js`
Centralized state management:
- Stores processed log entries
- Maintains filter states
- Tracks view preferences
- Manages masking settings

### 5. `search.js`
Implements search functionality:
- Text search across log entries
- Highlighting of search matches
- Navigation between search results

### 6. `export.js`
Handles exporting functionality:
- Export to TXT format
- Export to XLSX format
- Clipboard operations

### 7. `utilities.js`
Provides utility functions:
- Masking sensitive data
- HTML escaping
- String formatting

### 8. `theme.js`
Manages theme switching:
- Light/dark mode toggle
- Theme persistence

## Data Flow

The data flow through the application follows these steps:

1. **Input**: User uploads a log file or pastes log content
2. **Processing**:
   - Log format detection
   - Parsing into structured data
   - Timestamp formatting
   - Response time calculation
3. **State Update**: Processed data is stored in the state manager
4. **Rendering**: UI components render based on the current state
5. **Interaction**: User interactions (filtering, searching, etc.) update the state
6. **Re-rendering**: UI updates to reflect state changes

```
┌─────────┐    ┌────────────┐    ┌─────────────┐    ┌────────────┐
│  Input  │───►│ Processing │───►│ State Update│───►│ Rendering  │
└─────────┘    └────────────┘    └─────────────┘    └─────┬──────┘
                                        ▲                  │
                                        │                  │
                                  ┌─────┴──────┐    ┌──────▼─────┐
                                  │ Re-render  │◄───┤ Interaction│
                                  └────────────┘    └────────────┘
```

## Log Format Handling

The processor supports multiple log formats, each with specific parsing strategies:

### 1. Standard Format
- Identified by date pattern: `Month DD, YYYY @ HH:MM:SS.MMMRow: X, Column: Y:`
- Entries are split by this date pattern
- JSON data is extracted from between curly braces
- Additional sections (query string, headers, message body) are extracted using regex patterns

### 2. CSV with Path Format
- Starts with `log.file.path,message` header
- Contains file paths like `/opt/apigee/var/log/`
- Each entry starts with a path followed by a comma and JSON data
- Handles escaped quotes in CSV format

### 3. Simple Message Format
- Starts with `message` or contains JSON-like structures
- Similar to CSV format but without the file path
- Extracts data using regex patterns for double-quoted fields

### 4. Raw JSON Format
- Entries are valid JSON objects
- Contains standard fields like "time", "flow", etc.
- Sections for query string, headers, and message body follow the JSON

## Timestamp Processing

Timestamp handling is a critical aspect of the log processor:

### Extraction
- From standard format: Extracted from the date pattern
- From JSON-based formats: Extracted from the "time" field

### Parsing
- Uses JavaScript's `Date` object to parse various timestamp formats
- Handles ISO format (with 'T' separator)
- Processes timezone information

### Formatting
- Converts to standardized format: `YYYY-MM-DD HH:MM:SS.SSS`
- Automatically converts UTC timestamps to local timezone
- This explains the 1-2 hour differences observed between raw logs and displayed times

### Usage
- Sorting log entries chronologically
- Calculating response times between different flow stages
- Displaying in the UI with consistent formatting

### Timezone Conversion
- When timestamps with UTC offsets (e.g., `+0000`) are parsed using `new Date()`, JavaScript automatically converts them to the local timezone
- The 1-hour vs. 2-hour difference observed is due to Daylight Saving Time (DST)
- Winter timestamps show UTC+1 conversion, while summer timestamps show UTC+2 conversion

## UI Components

The application features several key UI components:

### Input Section
- File upload control
- Text input area
- Action buttons (Process, Extract, Clear)
- Format selection
- Collapsible design

### Results View
- Tab navigation between Flow and API Call views
- Data tables with sortable columns
- Column visibility selector
- Message ID filter
- Flow type filter
- Status code filter
- Search functionality

### Flow View
- Displays individual log entries
- Shows flow progression (PROXY_REQ, TARGET_REQ, etc.)
- Color-coded status codes
- Clickable rows for detailed view

### API Call View
- Aggregates related flows into single API calls
- Shows request method, URI, status code
- Calculates and displays response time
- Clickable rows for detailed view

### Offcanvas Detail View
- Comprehensive view of a single API call or flow
- Pre section with key metadata
- HTTP headers section with formatting
- Message body display with syntax highlighting
- Copy and export functionality
- Masking toggle for sensitive data

## State Management

The application uses a centralized state management approach:

### State Structure
- **Entries**: Processed log entries
- **FilterState**: Current filter selections
- **ColumnVisibilityState**: Visible/hidden columns
- **CurrentViewType**: Active view (flow or call)
- **MaskSensitiveData**: Masking toggle state
- **ProcessedContent**: Extracted message IDs
- **IsFormattedForOpenSearch**: Format toggle state

### State Updates
- Modules update state through the state manager
- UI components subscribe to state changes
- Custom events notify components of state updates

## Search and Filtering

The application provides powerful search and filtering capabilities:

### Search
- Global search across all fields or targeted message body search
- Highlighting of matches
- Match count display
- Navigation between matches

### Filtering
- Message ID filtering with multi-select
- Flow type filtering (PROXY_REQ, TARGET_REQ, etc.)
- Status code filtering
- Combined filters for precise data selection

### Column Selection
- Toggle visibility of individual columns
- "Select All" option
- Persistent column visibility state

## Export Functionality

The application supports exporting data in multiple formats:

### TXT Export
- Plain text format
- Includes all visible entries
- Respects current filtering
- Optional masking of sensitive data

### XLSX Export
- Excel spreadsheet format
- Tabular data with headers
- Respects current filtering
- Separate sheets for different views

### Clipboard Operations
- Copy selected entries
- Copy formatted for OpenSearch
- Copy message IDs
- Respects masking settings

## Log Entry Structure

After processing, each log entry is structured as a JavaScript object with the following properties:

```javascript
{
  time: "2025-05-13 09:05:12.809",       // Formatted timestamp
  flow: "PROXY_REQ_FLOW",                // Flow type
  flowstage: "PreFlow",                  // Flow stage
  level: "INFO",                         // Log level
  uri: "/v1/example/api",                // Request URI
  appName: "ExampleApp",                 // Application name
  statusCode: "200",                     // HTTP status code
  env: "prod",                           // Environment
  messageId: "a1b2c3d4-e5f6-7890-abcd",  // Message ID
  requestId: "req-12345",                // Request ID
  correlationId: "corr-67890",           // Correlation ID
  verb: "GET",                           // HTTP method
  headers: "...",                        // Formatted HTTP headers
  messageBody: "...",                    // Message body content
  client_id: "client_12345",             // Client ID
  queryString: "param1=value1&param2=value2", // Query string
  rawPosition: 0,                        // Original position in raw log
  format: "standard"                     // Detected log format
}
```

This structured format enables consistent handling across different log formats and provides a foundation for filtering, searching, and visualization.

## HTTP Header Processing

HTTP headers receive special processing to ensure consistent formatting and proper masking of sensitive data:

### Header Extraction
- Headers are extracted from the log entry using regex patterns
- Different patterns are used based on the detected log format
- The processor handles both single-line and multi-line header formats

### Header Formatting
- Headers are normalized to a consistent format: `Header-Name: value`
- Each header is placed on its own line
- The processor preserves the original header case

### Header Colorization
- Header names are wrapped in `<span class="http-header-name">` or `<span class="header-key">` tags
- Header values are wrapped in `<span class="http-header-value">` tags
- Different CSS classes are used based on the log format to maintain consistent styling

### Sensitive Header Handling
- Headers like `Authorization`, `client_id`, and `apikey` are identified as sensitive
- These headers can be automatically masked based on user preference
- The original values are preserved in data attributes for unmasking

## Message Body Handling

Message bodies are processed with care to preserve their original format while enabling proper display:

### Extraction
- Message bodies are extracted using regex patterns that identify the section between "---------- Message body:" and the next section or end of entry
- The processor handles different message body formats and encodings

### Preservation
- Message bodies are preserved in their original raw format without reformatting
- This ensures that the exact data sent/received is available for analysis

### Display
- Message bodies are displayed in a scrollable container in the offcanvas view
- Large message bodies use virtual scrolling for performance
- The processor detects and applies appropriate syntax highlighting for JSON and XML content

### Masking
- Sensitive data in message bodies (like client IDs) can be masked
- The original unmasked content is preserved for toggling visibility

## Masking Sensitive Data

The application implements comprehensive masking of sensitive information:

### Maskable Elements
- Authorization headers
- Client IDs
- API keys
- OAuth tokens
- Passwords
- Session tokens

### Masking Implementation
- Centralized in the `utilities.js` module
- Uses regex patterns to identify sensitive data
- Replaces sensitive values with "*** masked ***"
- Preserves original data for unmasking

### Masking Toggle
- UI control in the offcanvas header
- Toggles visibility of all masked elements
- State is preserved in the state manager

### Export Masking
- Exports respect the current masking state
- TXT exports are always masked by default
- XLSX exports can be configured for masking

## Response Time Calculation

The processor calculates API response times using a sophisticated algorithm:

### Calculation Logic
1. Start time is taken from the first `PROXY_REQ_FLOW` entry
2. End time is determined by checking, in order of precedence:
   - `PROXY_RESP_FLOW` timestamp
   - `TARGET_RESP_FLOW` timestamp
   - Last `TARGET_REQ_FLOW` with status code
   - Last `PROXY_REQ_FLOW` with status code
3. The difference is calculated and formatted in seconds with millisecond precision

### Edge Cases
- If no valid end time is found, response time is shown as "-"
- For incomplete requests, the processor attempts to provide the most accurate time available
- Multiple target requests are handled by finding the one with a status code

### Display
- Response times are displayed in the API Call view
- Format: "X.XXX s" (seconds with 3 decimal places)
- Provides crucial performance metrics for API analysis

## API Call Aggregation

In API Call view, the processor aggregates related flow entries into unified API calls:

### Aggregation Process
1. Entries are grouped by message ID
2. For each group, the processor extracts:
   - Basic info from the first `PROXY_REQ_FLOW`
   - Status code based on precedence rules
   - Response time using the calculation algorithm
   - All original entries are preserved for detailed view

### Status Code Precedence
1. `PROXY_RESP_FLOW` status code (highest priority)
2. `TARGET_RESP_FLOW` status code
3. Last `TARGET_REQ_FLOW` with status code
4. Last `PROXY_REQ_FLOW` with status code (lowest priority)

### Benefits
- Provides a higher-level view of API activity
- Simplifies analysis of complete request/response cycles
- Enables easier identification of failed or slow requests

## Error Handling

The application implements robust error handling to ensure stability:

### Input Validation
- Checks for valid file types and content
- Validates text input before processing
- Provides clear error messages for invalid inputs

### Processing Error Handling
- Try/catch blocks around critical parsing operations
- Fallback parsing strategies for non-standard formats
- Detailed error logging to console for debugging

### UI Error Recovery
- Graceful degradation when data is incomplete
- Default values for missing fields
- Clear error indicators in the UI

### Console Logging
- Structured logging of errors with context
- Warning messages for non-critical issues
- Detailed information for debugging complex parsing problems

## Performance Optimizations

Several optimizations ensure the application performs well with large log files:

### Lazy Loading
- Large log files are processed incrementally
- UI updates are batched for efficiency
- Virtual scrolling for large result sets

### Efficient Parsing
- Optimized regex patterns for faster matching
- Single-pass extraction of multiple data points
- Caching of intermediate parsing results

### DOM Efficiency
- Minimal DOM updates
- Use of document fragments for batch insertions
- Event delegation for reduced listener count

### Memory Management
- Proper cleanup of large data structures
- Avoidance of circular references
- Efficient string handling for large logs

## Integration Points

The application provides several integration points with external systems:

### OpenSearch Integration
- Format for OpenSearch button
- Converts log entries to OpenSearch-compatible format
- Facilitates searching in OpenSearch

### Export Formats
- TXT export for text editors and documentation
- XLSX export for spreadsheet analysis
- Clipboard operations for quick sharing

### Theme Integration
- Respects system dark/light mode preference
- Integrates with the parent application's theme system
- Consistent styling across the WebTool suite

## Future Enhancements

Potential areas for future development include:

### Additional Log Formats
- Support for more Apigee log variants
- Custom format definitions
- Format auto-detection improvements

### Advanced Visualization
- Timeline view of API calls
- Performance graphs and statistics
- Flow diagrams for complex API interactions

### Enhanced Search
- Regular expression search
- Advanced filtering options
- Saved searches and filters

### Direct API Integration
- Direct connection to Apigee API
- Real-time log streaming
- Integration with monitoring systems
