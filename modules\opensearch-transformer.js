/**
 * OpenSearch Data Transformer
 * Converts OpenSearch responses to current app data structure
 */

export class OpenSearchTransformer {
  constructor() {
    // Field mapping configuration
    this.fieldMapping = {
      timestamp: ['@timestamp', 'timestamp', 'time'],
      messageId: ['messageId', 'message_id', 'msgId'],
      flow: ['flow', 'flowType', 'flow_type'],
      flowstage: ['flowstage', 'flow_stage', 'stage'],
      level: ['level', 'logLevel', 'log_level'],
      statusCode: ['statusCode', 'status_code', 'status', 'responseCode'],
      uri: ['uri', 'url', 'path', 'endpoint'],
      appName: ['appName', 'app_name', 'application', 'app'],
      environment: ['environment', 'env', 'stage'],
      verb: ['verb', 'method', 'httpMethod', 'http_method'],
      requestId: ['requestId', 'request_id', 'reqId'],
      correlationId: ['correlationId', 'correlation_id', 'corrId'],
      clientId: ['clientId', 'client_id', 'client'],
      responseTime: ['responseTime', 'response_time', 'duration', 'elapsed'],
      headers: ['headers', 'httpHeaders', 'http_headers'],
      messageBody: ['messageBody', 'message_body', 'body', 'payload'],
      queryString: ['queryString', 'query_string', 'queryParams', 'query_params']
    };
  }

  /**
   * Transform OpenSearch search response to app data structure
   * @param {Object} searchResponse - OpenSearch search response
   * @returns {Object} Transformed data
   */
  transformSearchResponse(searchResponse) {
    console.log('Transforming OpenSearch response:', searchResponse);

    if (!searchResponse) {
      return {
        entries: [],
        totalHits: 0,
        took: 0,
        success: false,
        error: 'No search response received'
      };
    }

    // Handle different response structures
    let hits = [];
    let total = 0;
    let took = 0;

    if (searchResponse.hits && Array.isArray(searchResponse.hits.hits)) {
      // Standard OpenSearch response structure
      hits = searchResponse.hits.hits;
      total = searchResponse.hits.total?.value || searchResponse.hits.total || 0;
      took = searchResponse.took || 0;
    } else if (Array.isArray(searchResponse.hits)) {
      // Direct hits array
      hits = searchResponse.hits;
      total = hits.length;
    } else if (Array.isArray(searchResponse)) {
      // Response is directly an array
      hits = searchResponse;
      total = hits.length;
    } else {
      console.error('Unexpected response structure:', searchResponse);
      return {
        entries: [],
        totalHits: 0,
        took: 0,
        success: false,
        error: 'Unexpected response structure from OpenSearch'
      };
    }

    console.log(`Processing ${hits.length} hits from OpenSearch`);

    // Enable field mapping debugging for first search
    window.debugFieldMapping = true;

    const entries = hits.map((hit, index) => this.transformHit(hit, index));

    // Disable debugging after first few hits
    window.debugFieldMapping = false;

    return {
      entries,
      totalHits: total,
      took: took,
      success: true,
      aggregations: searchResponse.aggregations
    };
  }

  /**
   * Transform single OpenSearch hit to app entry structure
   * @param {Object} hit - OpenSearch hit
   * @param {number} index - Entry index
   * @returns {Object} Transformed entry
   */
  transformHit(hit, index = 0) {
    const source = hit._source || {};

    // Debug: Log the first few hits to see actual field structure
    if (index < 3) {
      console.log(`Hit ${index} source fields:`, Object.keys(source));
      console.log(`Hit ${index} sample data:`, source);
    }

    return {
      // Core fields with fallbacks
      time: this.formatTimestamp(this.getFieldValue(source, 'timestamp')),
      flow: this.getFieldValue(source, 'flow') || '-',
      flowstage: this.getFieldValue(source, 'flowstage') || '',
      level: this.getFieldValue(source, 'level') || '',
      uri: this.getFieldValue(source, 'uri') || '-',
      appName: this.getFieldValue(source, 'appName') || '-',
      statusCode: this.getFieldValue(source, 'statusCode') || '-',
      env: this.getFieldValue(source, 'environment') || '-',
      messageId: this.getFieldValue(source, 'messageId') || '-',
      requestId: this.getFieldValue(source, 'requestId') || '',
      correlationId: this.getFieldValue(source, 'correlationId') || '',
      verb: this.getFieldValue(source, 'verb') || '',
      client_id: this.getFieldValue(source, 'clientId') || '',

      // Complex field transformations
      headers: this.transformHeaders(source),
      messageBody: this.transformMessageBody(source),
      queryString: this.transformQueryString(source),

      // OpenSearch specific metadata
      opensearchId: hit._id,
      opensearchIndex: hit._index,
      opensearchScore: hit._score,

      // Mark as OpenSearch data for formatting logic
      dataSource: 'opensearch',
      format: 'opensearch',
      rawPosition: index
    };
  }

  /**
   * Get field value using field mapping
   * @param {Object} source - Source document
   * @param {string} fieldType - Field type key
   * @returns {*} Field value
   */
  getFieldValue(source, fieldType) {
    const possibleFields = this.fieldMapping[fieldType] || [fieldType];

    for (const field of possibleFields) {
      if (source.hasOwnProperty(field) && source[field] !== null && source[field] !== undefined) {
        // Debug: Log successful field mappings for the first few hits
        if (window.debugFieldMapping) {
          console.log(`✅ Found ${fieldType} in field: ${field} = ${source[field]}`);
        }
        return source[field];
      }
    }

    // Debug: Log missing fields for the first few hits
    if (window.debugFieldMapping) {
      console.log(`❌ Missing ${fieldType}, tried fields:`, possibleFields);
    }

    return null;
  }

  /**
   * Transform headers from various OpenSearch formats
   * @param {Object} source - Source document
   * @returns {string} Formatted headers
   */
  transformHeaders(source) {
    const headersValue = this.getFieldValue(source, 'headers');
    
    if (!headersValue) {
      return 'No headers available';
    }

    // Case 1: Headers as object
    if (typeof headersValue === 'object' && !Array.isArray(headersValue)) {
      return Object.entries(headersValue)
        .map(([key, value]) => `${key}: ${value}`)
        .join('\n');
    }

    // Case 2: Headers as array of objects
    if (Array.isArray(headersValue)) {
      return headersValue
        .map(header => {
          if (typeof header === 'object') {
            return Object.entries(header)
              .map(([key, value]) => `${key}: ${value}`)
              .join('\n');
          }
          return header.toString();
        })
        .join('\n');
    }

    // Case 3: Headers as string
    if (typeof headersValue === 'string') {
      return headersValue;
    }

    return 'No headers available';
  }

  /**
   * Transform message body from various formats
   * @param {Object} source - Source document
   * @returns {string} Formatted message body
   */
  transformMessageBody(source) {
    const bodyValue = this.getFieldValue(source, 'messageBody');
    
    if (!bodyValue) {
      return 'No message body available';
    }

    // Case 1: Object that needs to be stringified
    if (typeof bodyValue === 'object') {
      try {
        return JSON.stringify(bodyValue, null, 2);
      } catch (error) {
        return bodyValue.toString();
      }
    }

    // Case 2: String (most common)
    if (typeof bodyValue === 'string') {
      return bodyValue;
    }

    // Case 3: Other types
    return bodyValue.toString();
  }

  /**
   * Transform query parameters
   * @param {Object} source - Source document
   * @returns {string} Formatted query string
   */
  transformQueryString(source) {
    const queryValue = this.getFieldValue(source, 'queryString');
    
    if (!queryValue) {
      return '';
    }

    // Case 1: Query params as object
    if (typeof queryValue === 'object' && !Array.isArray(queryValue)) {
      return Object.entries(queryValue)
        .map(([key, value]) => `${key}=${value}`)
        .join('&');
    }

    // Case 2: Query string as string
    if (typeof queryValue === 'string') {
      return queryValue;
    }

    // Case 3: Array of query params
    if (Array.isArray(queryValue)) {
      return queryValue.join('&');
    }

    return queryValue.toString();
  }

  /**
   * Format OpenSearch timestamp to app format
   * @param {string|number|Date} timestamp - Timestamp value
   * @returns {string} Formatted timestamp
   */
  formatTimestamp(timestamp) {
    if (!timestamp) return '-';

    try {
      const date = new Date(timestamp);
      
      // Check if date is valid
      if (isNaN(date.getTime())) {
        return timestamp.toString();
      }

      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        fractionalSecondDigits: 3
      }).replace(',', '');
    } catch (error) {
      console.error('Error formatting timestamp:', error);
      return timestamp ? timestamp.toString() : '-';
    }
  }

  /**
   * Transform timeline aggregation response
   * @param {Object} aggregationResponse - OpenSearch aggregation response
   * @returns {Object} Transformed timeline data
   */
  transformTimelineResponse(aggregationResponse) {
    if (!aggregationResponse || !aggregationResponse.aggregations) {
      return {
        buckets: [],
        totalHits: 0,
        success: false
      };
    }

    const timeline = aggregationResponse.aggregations.timeline;
    if (!timeline || !timeline.buckets) {
      return {
        buckets: [],
        totalHits: 0,
        success: false
      };
    }

    const buckets = timeline.buckets.map(bucket => ({
      key: bucket.key,
      keyAsString: bucket.key_as_string,
      docCount: bucket.doc_count,
      timestamp: new Date(bucket.key)
    }));

    return {
      buckets,
      totalHits: aggregationResponse.hits?.total?.value || 0,
      success: true
    };
  }

  /**
   * Create API calls view from OpenSearch entries
   * @param {Array} entries - Transformed entries
   * @returns {Array} API calls
   */
  createApiCallsFromEntries(entries) {
    const callsMap = new Map();

    entries.forEach(entry => {
      const callKey = `${entry.messageId}_${entry.requestId}`;
      
      if (!callsMap.has(callKey)) {
        callsMap.set(callKey, {
          messageId: entry.messageId,
          requestId: entry.requestId,
          correlationId: entry.correlationId,
          verb: entry.verb,
          uri: entry.uri,
          statusCode: entry.statusCode,
          appName: entry.appName,
          env: entry.env,
          time: entry.time,
          responseTime: this.calculateResponseTime(entries, entry),
          entries: [],
          dataSource: 'opensearch'
        });
      }

      callsMap.get(callKey).entries.push(entry);
    });

    return Array.from(callsMap.values());
  }

  /**
   * Calculate response time for an API call
   * @param {Array} allEntries - All entries
   * @param {Object} entry - Current entry
   * @returns {string} Response time
   */
  calculateResponseTime(allEntries, entry) {
    // Try to find corresponding response entry
    const responseEntry = allEntries.find(e => 
      e.messageId === entry.messageId && 
      e.flow && e.flow.includes('RESP')
    );

    if (responseEntry && entry.time && responseEntry.time) {
      try {
        const startTime = new Date(entry.time);
        const endTime = new Date(responseEntry.time);
        const diff = endTime - startTime;
        
        if (diff > 0) {
          return `${diff}ms`;
        }
      } catch (error) {
        console.error('Error calculating response time:', error);
      }
    }

    return '-';
  }

  /**
   * Validate transformed entry
   * @param {Object} entry - Transformed entry
   * @returns {boolean} Is valid
   */
  validateEntry(entry) {
    return entry && 
           typeof entry === 'object' &&
           entry.hasOwnProperty('time') &&
           entry.hasOwnProperty('messageId') &&
           entry.dataSource === 'opensearch';
  }
}

// Create and export singleton instance
export const opensearchTransformer = new OpenSearchTransformer();
