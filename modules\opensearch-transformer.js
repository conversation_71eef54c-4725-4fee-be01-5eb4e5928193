/**
 * OpenSearch Data Transformer
 * Converts OpenSearch responses to current app data structure
 */

export class OpenSearchTransformer {
  constructor() {
    // Field mapping configuration for your OpenSearch structure
    this.fieldMapping = {
      timestamp: ['@timestamp', 'timestamp', 'time'],
      messageId: ['trace.messageid', 'messageId', 'message_id', 'msgId'],
      flow: ['trace.flow', 'flow', 'flowType', 'flow_type'],
      flowstage: ['trace.flowstage', 'flowstage', 'flow_stage', 'stage'],
      level: ['trace.level', 'level', 'logLevel', 'log_level'],
      statusCode: ['trace.statuscode', 'statusCode', 'status_code', 'status', 'responseCode'],
      uri: ['trace.uri', 'uri', 'url', 'path', 'endpoint'],
      appName: ['trace.app_name', 'appName', 'app_name', 'application', 'app'],
      environment: ['environment', 'env', 'stage'],
      verb: ['trace.verb', 'verb', 'method', 'httpMethod', 'http_method'],
      requestId: ['trace.requestid', 'requestId', 'request_id', 'reqId'],
      correlationId: ['trace.correlationid', 'correlationId', 'correlation_id', 'corrId'],
      clientId: ['trace.client_id', 'clientId', 'client_id', 'client'],
      responseTime: ['responseTime', 'response_time', 'duration', 'elapsed'],
      headers: ['DATA', 'headers', 'httpHeaders', 'http_headers'], // Headers are in DATA field
      messageBody: ['DATA', 'messageBody', 'message_body', 'body', 'payload'], // Message body is in DATA field
      queryString: ['DATA', 'queryString', 'query_string', 'queryParams', 'query_params'] // Query string is in DATA field
    };
  }

  /**
   * Transform OpenSearch search response to app data structure
   * @param {Object} searchResponse - OpenSearch search response
   * @returns {Object} Transformed data
   */
  transformSearchResponse(searchResponse) {
    console.log('Transforming OpenSearch response:', searchResponse);

    if (!searchResponse) {
      return {
        entries: [],
        totalHits: 0,
        took: 0,
        success: false,
        error: 'No search response received'
      };
    }

    // Handle different response structures
    let hits = [];
    let total = 0;
    let took = 0;

    if (searchResponse.hits && Array.isArray(searchResponse.hits.hits)) {
      // Standard OpenSearch response structure
      hits = searchResponse.hits.hits;
      total = searchResponse.hits.total?.value || searchResponse.hits.total || 0;
      took = searchResponse.took || 0;
    } else if (Array.isArray(searchResponse.hits)) {
      // Direct hits array
      hits = searchResponse.hits;
      total = hits.length;
    } else if (Array.isArray(searchResponse)) {
      // Response is directly an array
      hits = searchResponse;
      total = hits.length;
    } else {
      console.error('Unexpected response structure:', searchResponse);
      return {
        entries: [],
        totalHits: 0,
        took: 0,
        success: false,
        error: 'Unexpected response structure from OpenSearch'
      };
    }

    console.log(`Processing ${hits.length} hits from OpenSearch`);

    // Enable field mapping debugging for first search
    window.debugFieldMapping = true;

    const entries = hits.map((hit, index) => this.transformHit(hit, index));

    // Disable debugging after first few hits
    window.debugFieldMapping = false;

    return {
      entries,
      totalHits: total,
      took: took,
      success: true,
      aggregations: searchResponse.aggregations
    };
  }

  /**
   * Transform single OpenSearch hit to app entry structure
   * @param {Object} hit - OpenSearch hit
   * @param {number} index - Entry index
   * @returns {Object} Transformed entry
   */
  transformHit(hit, index = 0) {
    const source = hit._source || {};

    // Debug: Log the first few hits to see actual field structure
    if (index < 3) {
      console.log(`Hit ${index} source fields:`, Object.keys(source));
      console.log(`Hit ${index} sample data:`, source);
    }

    return {
      // Core fields with fallbacks
      time: this.formatTimestamp(this.getFieldValue(source, 'timestamp')),
      flow: this.getFieldValue(source, 'flow') || '-',
      flowstage: this.getFieldValue(source, 'flowstage') || '',
      level: this.getFieldValue(source, 'level') || '',
      uri: this.getFieldValue(source, 'uri') || '-',
      appName: this.getFieldValue(source, 'appName') || '-',
      statusCode: this.getFieldValue(source, 'statusCode') || '-',
      env: this.getFieldValue(source, 'environment') || '-',
      messageId: this.getFieldValue(source, 'messageId') || '-',
      requestId: this.getFieldValue(source, 'requestId') || '',
      correlationId: this.getFieldValue(source, 'correlationId') || '',
      verb: this.getFieldValue(source, 'verb') || '',
      client_id: this.getFieldValue(source, 'clientId') || '',

      // Complex field transformations
      headers: this.transformHeaders(source),
      messageBody: this.transformMessageBody(source),
      queryString: this.transformQueryString(source),

      // OpenSearch specific metadata
      opensearchId: hit._id,
      opensearchIndex: hit._index,
      opensearchScore: hit._score,

      // Mark as OpenSearch data for formatting logic
      dataSource: 'opensearch',
      format: 'opensearch',
      rawPosition: index
    };
  }

  /**
   * Get field value using field mapping (supports nested paths like 'trace.messageid')
   * @param {Object} source - Source document
   * @param {string} fieldType - Field type key
   * @returns {*} Field value
   */
  getFieldValue(source, fieldType) {
    const possibleFields = this.fieldMapping[fieldType] || [fieldType];

    for (const field of possibleFields) {
      let value = this.getNestedValue(source, field);

      if (value !== null && value !== undefined && value !== '') {
        // Debug: Log successful field mappings for the first few hits
        if (window.debugFieldMapping) {
          console.log(`✅ Found ${fieldType} in field: ${field} = ${value}`);
        }
        return value;
      }
    }

    // Debug: Log missing fields for the first few hits
    if (window.debugFieldMapping) {
      console.log(`❌ Missing ${fieldType}, tried fields:`, possibleFields);
    }

    return null;
  }

  /**
   * Get nested value from object using dot notation (e.g., 'trace.messageid')
   * @param {Object} obj - Source object
   * @param {string} path - Dot-separated path
   * @returns {*} Value or null
   */
  getNestedValue(obj, path) {
    if (!obj || !path) return null;

    const keys = path.split('.');
    let current = obj;

    for (const key of keys) {
      if (current && typeof current === 'object' && current.hasOwnProperty(key)) {
        current = current[key];
      } else {
        return null;
      }
    }

    return current;
  }

  /**
   * Transform headers from DATA field
   * @param {Object} source - Source document
   * @returns {string} Formatted headers
   */
  transformHeaders(source) {
    const dataValue = source.DATA;

    if (!dataValue || typeof dataValue !== 'string') {
      return 'No headers available';
    }

    // Extract headers section from DATA field
    const headerMatch = dataValue.match(/---------- HTTPHeaders:\n(.*?)\n\n---------- Message body:/s);

    if (headerMatch && headerMatch[1]) {
      return headerMatch[1].trim();
    }

    return 'No headers available';
  }

  /**
   * Transform message body from DATA field
   * @param {Object} source - Source document
   * @returns {string} Formatted message body
   */
  transformMessageBody(source) {
    const dataValue = source.DATA;

    if (!dataValue || typeof dataValue !== 'string') {
      return 'No message body available';
    }

    // Extract message body section from DATA field
    const bodyMatch = dataValue.match(/---------- Message body:\n(.*?)$/s);

    if (bodyMatch && bodyMatch[1]) {
      return bodyMatch[1].trim();
    }

    return 'No message body available';
  }

  /**
   * Transform query parameters from DATA field
   * @param {Object} source - Source document
   * @returns {string} Formatted query string
   */
  transformQueryString(source) {
    const dataValue = source.DATA;

    if (!dataValue || typeof dataValue !== 'string') {
      return '';
    }

    // Extract query string section from DATA field
    const queryMatch = dataValue.match(/---------- Query string:\n(.*?)\n\n---------- HTTPHeaders:/s);

    if (queryMatch && queryMatch[1] && queryMatch[1].trim()) {
      return queryMatch[1].trim();
    }

    return '';
  }

  /**
   * Format OpenSearch timestamp to app format
   * @param {string|number|Date} timestamp - Timestamp value
   * @returns {string} Formatted timestamp
   */
  formatTimestamp(timestamp) {
    if (!timestamp) return '-';

    try {
      const date = new Date(timestamp);
      
      // Check if date is valid
      if (isNaN(date.getTime())) {
        return timestamp.toString();
      }

      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        fractionalSecondDigits: 3
      }).replace(',', '');
    } catch (error) {
      console.error('Error formatting timestamp:', error);
      return timestamp ? timestamp.toString() : '-';
    }
  }

  /**
   * Transform timeline aggregation response
   * @param {Object} aggregationResponse - OpenSearch aggregation response
   * @returns {Object} Transformed timeline data
   */
  transformTimelineResponse(aggregationResponse) {
    if (!aggregationResponse || !aggregationResponse.aggregations) {
      return {
        buckets: [],
        totalHits: 0,
        success: false
      };
    }

    const timeline = aggregationResponse.aggregations.timeline;
    if (!timeline || !timeline.buckets) {
      return {
        buckets: [],
        totalHits: 0,
        success: false
      };
    }

    const buckets = timeline.buckets.map(bucket => ({
      key: bucket.key,
      keyAsString: bucket.key_as_string,
      docCount: bucket.doc_count,
      timestamp: new Date(bucket.key)
    }));

    return {
      buckets,
      totalHits: aggregationResponse.hits?.total?.value || 0,
      success: true
    };
  }

  /**
   * Create API calls view from OpenSearch entries
   * @param {Array} entries - Transformed entries
   * @returns {Array} API calls
   */
  createApiCallsFromEntries(entries) {
    const callsMap = new Map();

    entries.forEach(entry => {
      const callKey = `${entry.messageId}_${entry.requestId}`;
      
      if (!callsMap.has(callKey)) {
        callsMap.set(callKey, {
          messageId: entry.messageId,
          requestId: entry.requestId,
          correlationId: entry.correlationId,
          verb: entry.verb,
          uri: entry.uri,
          statusCode: entry.statusCode,
          appName: entry.appName,
          env: entry.env,
          time: entry.time,
          responseTime: this.calculateResponseTime(entries, entry),
          entries: [],
          dataSource: 'opensearch'
        });
      }

      callsMap.get(callKey).entries.push(entry);
    });

    return Array.from(callsMap.values());
  }

  /**
   * Calculate response time for an API call
   * @param {Array} allEntries - All entries
   * @param {Object} entry - Current entry
   * @returns {string} Response time
   */
  calculateResponseTime(allEntries, entry) {
    // Try to find corresponding response entry
    const responseEntry = allEntries.find(e => 
      e.messageId === entry.messageId && 
      e.flow && e.flow.includes('RESP')
    );

    if (responseEntry && entry.time && responseEntry.time) {
      try {
        const startTime = new Date(entry.time);
        const endTime = new Date(responseEntry.time);
        const diff = endTime - startTime;
        
        if (diff > 0) {
          return `${diff}ms`;
        }
      } catch (error) {
        console.error('Error calculating response time:', error);
      }
    }

    return '-';
  }

  /**
   * Validate transformed entry
   * @param {Object} entry - Transformed entry
   * @returns {boolean} Is valid
   */
  validateEntry(entry) {
    return entry && 
           typeof entry === 'object' &&
           entry.hasOwnProperty('time') &&
           entry.hasOwnProperty('messageId') &&
           entry.dataSource === 'opensearch';
  }
}

// Create and export singleton instance
export const opensearchTransformer = new OpenSearchTransformer();
