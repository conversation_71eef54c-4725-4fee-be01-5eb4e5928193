/**
 * Mode Manager for Apigee Log Processor
 * Handles switching between File Mode and OpenSearch Mode
 */
import { stateManager } from './state.js';

export class ModeManager {
  constructor() {
    this.currentMode = 'file'; // 'file' or 'opensearch'
    this.listeners = [];
    this.modeSwitchElement = null;
    this.fileModeSection = null;
    this.opensearchModeSection = null;
  }

  /**
   * Initialize the mode manager
   */
  init() {
    console.log('Initializing Mode Manager...');
    
    // Get DOM elements
    this.modeSwitchElement = document.getElementById('modeSwitch');
    this.fileModeSection = document.getElementById('fileModeSection');
    this.opensearchModeSection = document.getElementById('opensearchModeSection');

    if (!this.modeSwitchElement || !this.fileModeSection || !this.opensearchModeSection) {
      console.error('Mode switcher elements not found');
      return;
    }

    // Set up event listener for mode switch
    this.modeSwitchElement.addEventListener('change', (event) => {
      const isOpenSearchMode = event.target.checked;
      this.switchMode(isOpenSearchMode ? 'opensearch' : 'file');
    });

    // Initialize with file mode
    this.switchMode('file');

    // Set up listener for credential manager updates
    this.addListener((newMode) => {
      if (newMode === 'opensearch') {
        // Import and update credential manager when switching to OpenSearch mode
        import('./credential-manager.js').then(({ credentialManager }) => {
          credentialManager.updateConnectionStatus();
        });
      }
    });

    console.log('Mode Manager initialized');
  }

  /**
   * Switch between modes
   * @param {string} mode - 'file' or 'opensearch'
   */
  switchMode(mode) {
    if (mode !== 'file' && mode !== 'opensearch') {
      console.error('Invalid mode:', mode);
      return;
    }

    const previousMode = this.currentMode;
    this.currentMode = mode;

    console.log(`Switching from ${previousMode} to ${mode} mode`);

    // Update UI
    this.updateUI(mode);

    // Update state
    stateManager.updateState('currentMode', mode);

    // Notify listeners
    this.notifyListeners(mode, previousMode);

    // Clear any existing data when switching modes
    if (previousMode !== mode) {
      this.clearModeData(previousMode);
    }
  }

  /**
   * Update UI based on current mode
   * @param {string} mode - Current mode
   */
  updateUI(mode) {
    // Update mode switch
    this.modeSwitchElement.checked = (mode === 'opensearch');

    // Show/hide appropriate sections
    if (mode === 'file') {
      this.fileModeSection.classList.remove('hidden');
      this.opensearchModeSection.classList.add('hidden');
      
      // Update button states for file mode
      this.updateFileModeButttons(true);
      this.updateOpenSearchModeButtons(false);
    } else {
      this.fileModeSection.classList.add('hidden');
      this.opensearchModeSection.classList.remove('hidden');
      
      // Update button states for opensearch mode
      this.updateFileModeButttons(false);
      this.updateOpenSearchModeButtons(true);
    }

    // Update processed container visibility
    this.updateProcessedContainerVisibility();
  }

  /**
   * Update file mode button states
   * @param {boolean} enabled - Whether buttons should be enabled
   */
  updateFileModeButttons(enabled) {
    const processButton = document.getElementById('processButton');
    const extractButton = document.getElementById('extractButton');
    const clearButton = document.getElementById('clearButton');

    if (processButton) processButton.disabled = !enabled;
    if (extractButton) extractButton.disabled = !enabled;
    if (clearButton) clearButton.disabled = !enabled;
  }

  /**
   * Update OpenSearch mode button states
   * @param {boolean} enabled - Whether buttons should be enabled
   */
  updateOpenSearchModeButtons(enabled) {
    const searchButton = document.getElementById('opensearchSearchButton');
    const clearButton = document.getElementById('opensearchClearButton');
    const queryInput = document.getElementById('opensearchQuery');
    const helpButton = document.getElementById('opensearchHelpButton');

    if (searchButton) searchButton.disabled = !enabled;
    if (clearButton) clearButton.disabled = !enabled;
    if (queryInput) queryInput.disabled = !enabled;
    if (helpButton) helpButton.disabled = !enabled;
  }

  /**
   * Update processed container visibility based on mode and data
   */
  updateProcessedContainerVisibility() {
    const processedContainer = document.getElementById('processedContainer');
    if (!processedContainer) return;

    const state = stateManager.getState();
    const hasData = (this.currentMode === 'file' && state.entries.length > 0) ||
                   (this.currentMode === 'opensearch' && state.opensearchResults && state.opensearchResults.length > 0);

    if (hasData) {
      processedContainer.classList.remove('hidden');
    } else {
      processedContainer.classList.add('hidden');
    }
  }

  /**
   * Clear data when switching modes
   * @param {string} previousMode - The mode being switched from
   */
  clearModeData(previousMode) {
    if (previousMode === 'file') {
      // Clear file mode data
      const textInput = document.getElementById('textInput');
      const fileInput = document.getElementById('fileInput');
      
      if (textInput) textInput.value = '';
      if (fileInput) fileInput.value = '';
      
      // Clear processed entries
      stateManager.setState({
        entries: [],
        calls: [],
        processedContent: ''
      });
    } else if (previousMode === 'opensearch') {
      // Clear OpenSearch mode data
      const queryInput = document.getElementById('opensearchQuery');
      
      if (queryInput) queryInput.value = '';
      
      // Clear OpenSearch results
      stateManager.setState({
        opensearchResults: [],
        opensearchQuery: '',
        opensearchTotalHits: 0
      });
    }

    // Hide processed container
    const processedContainer = document.getElementById('processedContainer');
    if (processedContainer) {
      processedContainer.classList.add('hidden');
    }
  }

  /**
   * Get current mode
   * @returns {string} Current mode
   */
  getCurrentMode() {
    return this.currentMode;
  }

  /**
   * Check if currently in OpenSearch mode
   * @returns {boolean} True if in OpenSearch mode
   */
  isOpenSearchMode() {
    return this.currentMode === 'opensearch';
  }

  /**
   * Check if currently in file mode
   * @returns {boolean} True if in file mode
   */
  isFileMode() {
    return this.currentMode === 'file';
  }

  /**
   * Add mode change listener
   * @param {Function} listener - Callback function
   */
  addListener(listener) {
    this.listeners.push(listener);
  }

  /**
   * Remove mode change listener
   * @param {Function} listener - Callback function to remove
   */
  removeListener(listener) {
    const index = this.listeners.indexOf(listener);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * Notify all listeners of mode change
   * @param {string} newMode - New mode
   * @param {string} previousMode - Previous mode
   */
  notifyListeners(newMode, previousMode) {
    this.listeners.forEach(listener => {
      try {
        listener(newMode, previousMode);
      } catch (error) {
        console.error('Error in mode change listener:', error);
      }
    });
  }
}

// Create and export singleton instance
export const modeManager = new ModeManager();
