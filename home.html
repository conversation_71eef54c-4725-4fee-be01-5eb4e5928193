
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Home</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
            font-size: 16px;
            color: #2d3748;
            line-height: 1.6;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        #inputContainer, .container {
            max-width: 90%;
            text-align: left;
            border-radius: 12px;
            background-color: white;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);
            padding: 5px 30px;
            margin: 0 auto 10px;
            border: 1px solid rgba(0, 0, 0, 0.03);
        }

        /* Dark theme styles */
        body.dark-theme {
            background-color: #121212;
            color: #e0e0e0;
        }

        body.dark-theme #inputContainer,
        body.dark-theme .container {
            background-color: #1e1e1e;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        .link-card {
            margin-bottom: 20px;
            border: none;
            border-radius: 15px;
            transition: transform 0.1s ease-in-out, background-color 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
            background-color: #fff;
        }

        .link-card:hover {
            transform: scale(1.05);
        }

        /* Dark theme card styles */
        body.dark-theme .link-card {
            background-color: #2d2d2d;
            color: #e0e0e0;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.4) !important;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }

        body.dark-theme .link-card:hover {
            transform: scale(1.05);
            box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.5) !important;
        }

        body.dark-theme .card-title {
            color: #e0e0e0;
        }

        body.dark-theme h2,
        body.dark-theme h3 {
            color: #f5f5f5;
        }

        h2 {
            position: relative;
            padding-bottom: 10px;
            margin-bottom: 5px;
        }

        h2:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background-color: #e60000;
        }

        body.dark-theme h2:after {
            background-color: #e60000; /* Keep the same red color in dark mode for consistency */
        }

        /* Logo styles for dark mode */
        body.dark-theme .card-logo {
            filter: brightness(0.9);
        }

        .card-logo {
            width: 80px;
            height: 80px;
            margin-bottom: 0px;
            transition: filter 0.3s ease;
        }

        .card-title {
            font-weight: bold;
        }
    </style>
</head>
<body>

    <div class="container">
        <h2 class="text-center">Middleware Tools</h2>
        <!-- <p class="text-center mb-3">A collection of tools for various tasks.</p> -->
        <div class="row row-cols-1 row-cols-md-4 g-4 mt-2">

            <!-- <div class="col">
                <a href="" target="_blank" class="card link-card shadow text-center">
                    <div class="card-body">
                        <img src="src/" alt="" class="card-logo">
                        <h5 class="card-title">Tool Name</h5>
                        <p class="card-text">Short description here.</p>
                    </div>
                </a>
            </div> -->

            <div class="col">
                <a href="https://vfhu.onbmc.com/" target="_blank" class="card link-card shadow text-center">
                    <div class="card-body">
                        <img src="src/itsm.png" alt="" class="card-logo">
                        <h5 class="card-title">ITSM</h5>
                        <!-- <p class="card-text">Short description here.</p> -->
                    </div>
                </a>
            </div>

            <div class="col">
                <a href="https://toolbox.mwops.vodafone.hu/login/" target="_blank" class="card link-card shadow text-center">
                    <div class="card-body">
                        <img src="src/one_logo.svg" alt="Middleware Toolbox" class="card-logo">
                        <h5 class="card-title">Middleware Toolbox</h5>
                        <!-- <p class="card-text">Short description here.</p> -->
                    </div>
                </a>
            </div>

            <div class="col">
                <a href="https://opensearch.vodafone.hu" target="_blank" class="card link-card shadow text-center">
                    <div class="card-body">
                        <img src="src/OpenSearch.png" alt="Open Search" class="card-logo">
                        <h5 class="card-title">OpenSearch</h5>
                        <!-- <p class="card-text">Short description here.</p> -->
                    </div>
                </a>
            </div>

            <div class="col">
                <a href="https://vault.apps.okd.vodafone.hu/" target="_blank" class="card link-card shadow text-center">
                    <div class="card-body">
                        <img src="src/vault.png" alt="Vault" class="card-logo">
                        <h5 class="card-title">Vault</h5>
                        <!-- <p class="card-text">Short description here.</p> -->
                    </div>
                </a>
            </div>

            <div class="col">
                <a href="https://apigwuitst.vodafone.hu/" target="_blank" class="card link-card shadow text-center">
                    <div class="card-body">
                        <img src="src/apigee.png" alt="Apigee Teszt" class="card-logo">
                        <h5 class="card-title">Apigee Teszt</h5>
                        <!-- <p class="card-text">Short description here.</p> -->
                    </div>
                </a>
            </div>

            <div class="col">
                <a href="https://apigwneetst.vodafone.hu/edge" target="_blank" class="card link-card shadow text-center">
                    <div class="card-body">
                        <img src="src/apigee_new.png" alt="Apigee Teszt New Edge" class="card-logo">
                        <h5 class="card-title">Apigee Teszt - New Edge</h5>
                        <!-- <p class="card-text">Short description here.</p> -->
                    </div>
                </a>
            </div>

            <div class="col">
                <a href="https://apigwui.vodafone.hu/" target="_blank" class="card link-card shadow text-center">
                    <div class="card-body">
                        <img src="src/apigee.png" alt="Apigee Prod" class="card-logo">
                        <h5 class="card-title">Apigee Prod</h5>
                        <!-- <p class="card-text">Short description here.</p> -->
                    </div>
                </a>
            </div>

            <div class="col">
                <a href="https://apigwnee.vodafone.hu/edge" target="_blank" class="card link-card shadow text-center">
                    <div class="card-body">
                        <img src="src/apigee_new.png" alt="Apigee Prod New Edge" class="card-logo">
                        <h5 class="card-title">Apigee Prod - New Edge</h5>
                        <!-- <p class="card-text">Short description here.</p> -->
                    </div>
                </a>
            </div>

            <div class="col">
                <a href="https://dev.azure.com/vfhu-digital/apigee" target="_blank" class="card link-card shadow text-center">
                    <div class="card-body">
                        <img src="src/Azure-Devops-Logo.png" alt="Azure DevOps Apigee" class="card-logo">
                        <h5 class="card-title">Azure DevOps - Apigee</h5>
                        <!-- <p class="card-text">Short description here.</p> -->
                    </div>
                </a>
            </div>

            <div class="col">
                <a href="https://dev.azure.com/vfhu-digital/bwce" target="_blank" class="card link-card shadow text-center">
                    <div class="card-body">
                        <img src="src/Azure-Devops-Logo.png" alt="Azure DevOps BWCE" class="card-logo">
                        <h5 class="card-title">Azure DevOps - BWCE</h5>
                        <!-- <p class="card-text">Short description here.</p> -->
                    </div>
                </a>
            </div>

            <div class="col">
                <a href="https://dev.azure.com/vfhu-digital/som4cmnp" target="_blank" class="card link-card shadow text-center">
                    <div class="card-body">
                        <img src="src/Azure-Devops-Logo.png" alt="Azure DevOps SOMA" class="card-logo">
                        <h5 class="card-title">Azure DevOps - SOMA</h5>
                        <!-- <p class="card-text">Short description here.</p> -->
                    </div>
                </a>
            </div>

            <div class="col">
                <a href="https://dev.azure.com/vfhu-digital/Middleware%20OPS" target="_blank" class="card link-card shadow text-center">
                    <div class="card-body">
                        <img src="src/Azure-Devops-Logo.png" alt="Azure DevOps MWOps" class="card-logo">
                        <h5 class="card-title">Azure DevOps - MWOps</h5>
                        <!-- <p class="card-text">Short description here.</p> -->
                    </div>
                </a>
            </div>

            <div class="col">
                <a href="https://som4cmnp-frontend.apps.okd.vodafone.hu/dashboard" target="_blank" class="card link-card shadow text-center">
                    <div class="card-body">
                        <img src="src/Soma.png" alt="SOMA" class="card-logo">
                        <h5 class="card-title">SOMA</h5>
                        <!-- <p class="card-text">Short description here.</p> -->
                    </div>
                </a>
            </div>

            <div class="col">
                <a href="https://pgadmin-postgres-som4cmnpdb-som4cmnp.apps.okd.vodafone.hu/browser/" target="_blank" class="card link-card shadow text-center">
                    <div class="card-body">
                        <img src="src/pgadmin.png" alt="SOMA pgAdmin" class="card-logo">
                        <h5 class="card-title">SOMA pgAdmin</h5>
                        <!-- <p class="card-text">Short description here.</p> -->
                    </div>
                </a>
            </div>

            <div class="col">
                <a href="https://som4cmnp-frontend-uat.apps.okdtest.vodafone.hu/" target="_blank" class="card link-card shadow text-center">
                    <div class="card-body">
                        <img src="src/Soma.png" alt="SOMA UAT" class="card-logo">
                        <h5 class="card-title">SOMA - UAT</h5>
                        <!-- <p class="card-text">Short description here.</p> -->
                    </div>
                </a>
            </div>

            <div class="col">
                <a href="https://pgadmin-postgres-som4cmnpdb-som4cmnp-uat.apps.okdtest.vodafone.hu" target="_blank" class="card link-card shadow text-center">
                    <div class="card-body">
                        <img src="src/pgadmin.png" alt="SOMA pgAdmin UAT" class="card-logo">
                        <h5 class="card-title">SOMA pgAdmin - UAT</h5>
                        <!-- <p class="card-text">Short description here.</p> -->
                    </div>
                </a>
            </div>

            <div class="col">
                <a href="https://console-openshift-console.apps.okd.vodafone.hu/" target="_blank" class="card link-card shadow text-center">
                    <div class="card-body">
                        <img src="src/okd.png" alt="OKD Prod" class="card-logo">
                        <h5 class="card-title">OKD Prod</h5>
                        <!-- <p class="card-text">Short description here.</p> -->
                    </div>
                </a>
            </div>

            <div class="col">
                <a href="https://console-openshift-console.apps.okdtest.vodafone.hu/" target="_blank" class="card link-card shadow text-center">
                    <div class="card-body">
                        <img src="src/okd.png" alt="OKD Teszt" class="card-logo">
                        <h5 class="card-title">OKD Teszt</h5>
                        <!-- <p class="card-text">Short description here.</p> -->
                    </div>
                </a>
            </div>




            <!-- Add more cards for additional links if needed -->

        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Apply dark theme if parent page has dark theme
        document.addEventListener('DOMContentLoaded', function() {
            // Check if we're in an iframe/embedded context
            if (window.parent && window.parent !== window) {
                try {
                    // Try to check if parent has dark theme
                    if (window.parent.document.body.classList.contains('dark-theme')) {
                        document.body.classList.add('dark-theme');
                    }
                } catch (e) {
                    // If we can't access parent due to same-origin policy, check localStorage
                    const savedTheme = localStorage.getItem('theme');
                    if (savedTheme === 'dark') {
                        document.body.classList.add('dark-theme');
                    }
                }
            } else {
                // If not in iframe, check localStorage
                const savedTheme = localStorage.getItem('theme');
                if (savedTheme === 'dark') {
                    document.body.classList.add('dark-theme');
                }
            }
        });

        // Listen for theme changes from parent
        window.addEventListener('message', function(event) {
            if (event.data && event.data.type === 'themeChange') {
                if (event.data.theme === 'dark') {
                    document.body.classList.add('dark-theme');
                } else {
                    document.body.classList.remove('dark-theme');
                }
            }
        });
    </script>
</body>
</html>
