/**
 * OpenSearch Query Builder
 * Converts UI filters and search terms to OpenSearch Query DSL
 */

export class OpenSearchQueryBuilder {
  constructor() {
    // Field mapping for different query types
    this.fieldMapping = {
      messageId: ['messageId', 'message_id', 'msgId'],
      flow: ['flow', 'flowType', 'flow_type'],
      statusCode: ['statusCode', 'status_code', 'status', 'responseCode'],
      uri: ['uri', 'url', 'path', 'endpoint'],
      appName: ['appName', 'app_name', 'application', 'app'],
      environment: ['environment', 'env', 'stage'],
      verb: ['verb', 'method', 'httpMethod', 'http_method'],
      requestId: ['requestId', 'request_id', 'reqId'],
      correlationId: ['correlationId', 'correlation_id', 'corrId'],
      clientId: ['clientId', 'client_id', 'client'],
      level: ['level', 'logLevel', 'log_level']
    };

    // Default search fields for text queries
    this.defaultSearchFields = [
      'messageBody^2',  // Boost message body
      'uri^1.5',        // Boost URI
      'messageId^1.5',  // Boost message ID
      'headers',
      'flow',
      'appName',
      'environment'
    ];
  }

  /**
   * Build OpenSearch query from UI state
   * @param {Object} filterState - Current filter state from UI
   * @param {string} searchTerm - Search term
   * @param {Object} options - Additional options
   * @returns {Object} OpenSearch Query DSL
   */
  buildQuery(filterState, searchTerm = '', options = {}) {
    const mustClauses = [];
    const filterClauses = [];

    // Add search term query
    if (searchTerm && searchTerm.trim()) {
      const searchQuery = this.buildSearchQuery(searchTerm, options.globalSearch);
      mustClauses.push(searchQuery);
    }

    // Add filter clauses
    if (filterState) {
      // Message ID filters
      if (filterState.selectedMessageIds && filterState.selectedMessageIds.length > 0) {
        filterClauses.push({
          terms: {
            messageId: filterState.selectedMessageIds
          }
        });
      }

      // Flow filters
      if (filterState.selectedFlows && filterState.selectedFlows.length > 0) {
        filterClauses.push({
          terms: {
            flow: filterState.selectedFlows
          }
        });
      }

      // Status code filters
      if (filterState.selectedStatusCodes && filterState.selectedStatusCodes.length > 0) {
        filterClauses.push({
          terms: {
            statusCode: filterState.selectedStatusCodes
          }
        });
      }
    }

    // Add time range filter if specified
    if (options.timeRange) {
      filterClauses.push({
        range: {
          '@timestamp': {
            gte: options.timeRange.start,
            lte: options.timeRange.end
          }
        }
      });
    }

    // Build final query
    if (mustClauses.length === 0 && filterClauses.length === 0) {
      return { match_all: {} };
    }

    const boolQuery = {
      bool: {}
    };

    if (mustClauses.length > 0) {
      boolQuery.bool.must = mustClauses;
    }

    if (filterClauses.length > 0) {
      boolQuery.bool.filter = filterClauses;
    }

    return boolQuery;
  }

  /**
   * Build search query from search term
   * @param {string} searchTerm - Search term
   * @param {boolean} globalSearch - Whether to search globally or just in message body
   * @returns {Object} Search query
   */
  buildSearchQuery(searchTerm, globalSearch = false) {
    const term = searchTerm.trim();

    // Check for field-specific queries (e.g., "messageId:12345")
    const fieldQueryPattern = /(\w+):([^\s]+)/g;
    const fieldQueries = [];
    let remainingTerm = term;
    let match;

    while ((match = fieldQueryPattern.exec(term)) !== null) {
      const [fullMatch, field, value] = match;
      const mappedFields = this.fieldMapping[field] || [field];
      
      // Create term query for each possible field mapping
      const termQueries = mappedFields.map(mappedField => ({
        term: { [mappedField]: value }
      }));

      if (termQueries.length === 1) {
        fieldQueries.push(termQueries[0]);
      } else {
        fieldQueries.push({
          bool: {
            should: termQueries,
            minimum_should_match: 1
          }
        });
      }

      remainingTerm = remainingTerm.replace(fullMatch, '').trim();
    }

    // Build text search query for remaining term
    let textQuery = null;
    if (remainingTerm) {
      if (globalSearch) {
        // Search in all fields
        textQuery = {
          multi_match: {
            query: remainingTerm,
            fields: this.defaultSearchFields,
            type: 'best_fields',
            fuzziness: 'AUTO'
          }
        };
      } else {
        // Search only in message body (default behavior)
        textQuery = {
          match: {
            messageBody: {
              query: remainingTerm,
              fuzziness: 'AUTO'
            }
          }
        };
      }
    }

    // Combine field queries and text query
    if (fieldQueries.length > 0 && textQuery) {
      return {
        bool: {
          must: [...fieldQueries, textQuery]
        }
      };
    } else if (fieldQueries.length > 0) {
      return fieldQueries.length === 1 ? fieldQueries[0] : {
        bool: { must: fieldQueries }
      };
    } else if (textQuery) {
      return textQuery;
    }

    return { match_all: {} };
  }

  /**
   * Build aggregation query for timeline
   * @param {Object} baseQuery - Base query to aggregate on
   * @param {string} interval - Time interval (1h, 1d, etc.)
   * @param {Object} timeRange - Time range
   * @returns {Object} Aggregation query
   */
  buildTimelineAggregation(baseQuery, interval = '1h', timeRange = null) {
    const aggregationQuery = {
      size: 0,
      query: baseQuery,
      aggs: {
        timeline: {
          date_histogram: {
            field: '@timestamp',
            calendar_interval: interval,
            min_doc_count: 1,
            extended_bounds: timeRange ? {
              min: timeRange.start,
              max: timeRange.end
            } : undefined
          }
        },
        // Add additional aggregations
        status_codes: {
          terms: {
            field: 'statusCode',
            size: 10
          }
        },
        flows: {
          terms: {
            field: 'flow',
            size: 10
          }
        },
        apps: {
          terms: {
            field: 'appName',
            size: 10
          }
        }
      }
    };

    return aggregationQuery;
  }

  /**
   * Build query for message ID extraction
   * @param {string} messageId - Message ID to search for
   * @returns {Object} Query for finding all entries with the message ID
   */
  buildMessageIdQuery(messageId) {
    return {
      bool: {
        should: [
          { term: { messageId: messageId } },
          { term: { message_id: messageId } },
          { term: { msgId: messageId } }
        ],
        minimum_should_match: 1
      }
    };
  }

  /**
   * Build query for API call view
   * @param {Object} filterState - Filter state
   * @param {string} searchTerm - Search term
   * @returns {Object} Query optimized for API call aggregation
   */
  buildApiCallQuery(filterState, searchTerm = '') {
    const baseQuery = this.buildQuery(filterState, searchTerm);

    return {
      query: baseQuery,
      aggs: {
        api_calls: {
          terms: {
            field: 'messageId',
            size: 1000
          },
          aggs: {
            flows: {
              terms: {
                field: 'flow',
                size: 10
              }
            },
            latest_entry: {
              top_hits: {
                size: 1,
                sort: [{ '@timestamp': { order: 'desc' } }]
              }
            },
            response_time: {
              max: {
                field: 'responseTime'
              }
            }
          }
        }
      }
    };
  }

  /**
   * Calculate optimal time interval based on time range
   * @param {Object} timeRange - Time range with start and end
   * @returns {string} Optimal interval
   */
  calculateOptimalInterval(timeRange) {
    if (!timeRange || !timeRange.start || !timeRange.end) {
      return '1h';
    }

    const duration = new Date(timeRange.end) - new Date(timeRange.start);
    const hours = duration / (1000 * 60 * 60);

    if (hours <= 6) return '15m';
    if (hours <= 24) return '1h';
    if (hours <= 168) return '6h'; // 1 week
    if (hours <= 720) return '1d'; // 1 month
    return '1w';
  }

  /**
   * Build query for field suggestions/autocomplete
   * @param {string} field - Field name
   * @param {string} prefix - Prefix to search for
   * @param {number} size - Number of suggestions
   * @returns {Object} Suggestion query
   */
  buildSuggestionQuery(field, prefix, size = 10) {
    const mappedFields = this.fieldMapping[field] || [field];

    return {
      size: 0,
      aggs: {
        suggestions: {
          terms: {
            field: mappedFields[0],
            include: `${prefix}.*`,
            size: size
          }
        }
      }
    };
  }

  /**
   * Validate query structure
   * @param {Object} query - Query to validate
   * @returns {boolean} Is valid
   */
  validateQuery(query) {
    if (!query || typeof query !== 'object') {
      return false;
    }

    // Basic validation - check for required structure
    if (query.match_all) return true;
    if (query.bool && (query.bool.must || query.bool.should || query.bool.filter)) return true;
    if (query.term || query.terms || query.match || query.multi_match) return true;

    return false;
  }
}

// Create and export singleton instance
export const opensearchQueryBuilder = new OpenSearchQueryBuilder();
