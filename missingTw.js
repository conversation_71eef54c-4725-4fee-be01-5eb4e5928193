(function() {

let processedContent = '';

function replaceDate() {
    let sqlQuery = `select count(*), pd.package_id, pd.package_size
from iprocapp.mnp_psmdata pd
where substr(pd.package_id,-16) = '1999-01-01_20-00'
group by pd.package_id, pd.package_size;

select * from (
    select p.proc_id case_number
         , p.proc_name case_type
         , c.recipient_provider
         , c.donor_provider
         , m.msisdn
         , m.data
         , m.fax
         , m.TEMP_MSISDN
         , m.TEMP_DATA_MSISDN
         , m.TEMP_FAX_MSISDN
         , decode(MNP_PORT_ROUTING_ID,null,'NO','YES') KRA_ROUTING_FLOW_STARTED
         , decode(PSM_RESPONSE_ARR,null,'NOT_STARTED',0,'SENT',1,'RECEIVED',2,'FINISHED','UNKNOWN') PSM_FLOW_STATUS
    from iprocapp.mnp_proc p
       , iprocapp.mnp_port_msisdn m
       , iprocapp.mnp_port_ftp f
       , iprocapp.mnp_port_ftp_tr tr
       , iprocapp.mnp_port_carry c
       , iprocapp.mnp_port_routing r
       , iprocapp.mnp_port_psm ps
       , (select '1999-01-01' checkdate from dual) cd
    where 1 = 1
--     and p.proc_name = 'PORTIN'
      and p.proc_name not in ('RETURNMSISDN')
      and p.proc_id = m.proc_id
      and m.mnp_port_msisdn_id = f.mnp_port_msisdn_id (+)
      and f.mnp_port_ftp_id = tr.mnp_port_ftp_id (+)
      and m.mnp_port_msisdn_id = c.mnp_port_msisdn_id (+)
      and c.mnp_port_carry_id = r.mnp_port_carry_id (+)
      and m.mnp_port_msisdn_id = ps.mnp_port_msisdn_id (+)
      and substr(m.timewindow,0,10) = cd.checkdate
      and (36 || m.msisdn not in (
        select psm.MSISDN
        from iprocapp.mnp_psmdata psm
        where substr(substr(psm.PACKAGE_ID,-16),0,10) = cd.checkdate))
      and m.msisdn in (
        select md.msisdn
        from iprocapp.mnp_data md
        where substr(to_char(md.valid_from,'YYYY-MM-DD HH24:MI:SS'),0,10) = cd.checkdate
            or substr(to_char(md.valid_until,'YYYY-MM-DD HH24:MI:SS'),0,10) = cd.checkdate)
union all
    select bd.case_number
         , bd.case_type
         , ipepr.parameter_value recipient_provider
         , ipepd.parameter_value donor_provider
         , x.VOICE_NUMBER msisdn
         , null data
         , null fax
         , x.TEMP_VOICE_NUMBER temp_msisdn
         , null TEMP_DATA_MSISDN
         , null TEMP_FAX_MSISDN
         , 'N/A' KRA_ROUTING_FLOW_STARTED
        , decode(ps.PSM_STATUS,null,'NOT_STARTED',0,'STARTED',1,'SENT',2,'RECEIVED',3,'ALL_RECEIVED',4,'FINISHED','UNKNOWN') PSM_FLOW_STATUS
    from iprocapp.MNP_OVPN_BD bd
       , iprocapp.MNP_OVPN_PHONE_MTX x
       , iprocapp.MNP_TIME_WINDOW tw
       , iprocapp.MNP_OVPN_ROUTING r
       , iprocapp.MNP_OVPN_PSM ps
       , iprocapp.ipe_parameter ipepr
       , iprocapp.ipe_parameter ipepd
       , (select '1999-01-01' checkdate from dual) cd
    where bd.MNP_OVPN_BD_ID = x.MNP_OVPN_BD_ID
      and bd.mnp_timewindow_id = tw.TIMEWINDOW_ID
      and x.MNP_OVPN_PHONE_MTX_ID = r.MNP_OVPN_PHONE_MTX_ID(+)
      and x.MNP_OVPN_PHONE_MTX_ID = ps.MNP_OVPN_PHONE_MTX_ID (+)
      and tw.window_start = to_date(cd.checkdate || ' 20:00:00','YYYY-MM-DD HH24:MI:SS')
      and ipepr.parameter_type = 'ProviderKey'
      and ipepr.parameter_code = bd.RECIPIENT_KEY
      and ipepd.parameter_type = 'ProviderKey'
      and ipepd.parameter_code = bd.DONOR_KEY
      and (36 || x.VOICE_NUMBER not in (
        select psm.MSISDN
        from iprocapp.mnp_psmdata psm
        where substr(substr(psm.PACKAGE_ID,-16),0,10) = cd.checkdate))
      and x.VOICE_NUMBER in (
        select md.msisdn
        from iprocapp.mnp_data md
        where substr(to_char(md.valid_from,'YYYY-MM-DD HH24:MI:SS'),0,10) = cd.checkdate
            or substr(to_char(md.valid_until,'YYYY-MM-DD HH24:MI:SS'),0,10) = cd.checkdate))
order by case_type, msisdn`;

    const currentDate = new Date().toISOString().split('T')[0];

    const processedSQL = sqlQuery.replace(/(\d{4}-\d{2}-\d{2})/g, `<span class="highlight">${currentDate}</span>`);

    processedContent = processedSQL;
    displayContent(processedSQL);
    copySqlToClipboard(processedSQL);
    document.getElementById('sqlSection').classList.remove('hidden');
    showToast('SQL generated with today\'s date and copied to clipboard');
}

function clearTwText() {
    document.getElementById('fileContent').textContent = '';
    document.getElementById('sqlSection').classList.add('hidden');
}

function displayContent(content) {
    const fileContent = document.getElementById('fileContent');
    fileContent.innerHTML = content;
}

// function downloadProcessedFile() {
//     const textContent = processedContent.replace(/<\/?span[^>]*>/g, '');
//     const blob = new Blob([textContent], { type: 'text/plain' });
//     const url = URL.createObjectURL(blob);

//     const a = document.createElement('a');
//     a.href = url;
//     a.download = 'processed_sql.txt';
//     document.body.appendChild(a);
//     a.click();
//     document.body.removeChild(a);
//     URL.revokeObjectURL(url);
//     showToast('Processed SQL downloaded');
// }

function copySql() {
    const textArea = document.createElement('textarea');
    textArea.value = processedContent.replace(/<\/?span[^>]*>/g, '');
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    showToast('Processed SQL copied to clipboard');
}

function showToast(message) {
    const toastElement = document.getElementById('liveToast');
    const toastBody = document.getElementById('toastMessage');
    toastBody.textContent = message;
    const toast = new bootstrap.Toast(toastElement);
    toast.show();
}

function copySqlToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = processedContent.replace(/<\/?span[^>]*>/g, '');
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
}

    // Expose functions that need to be called from the HTML
    window.clearTwText = clearTwText;
    window.replaceDate = replaceDate;
    window.copySql = copySql;
})();