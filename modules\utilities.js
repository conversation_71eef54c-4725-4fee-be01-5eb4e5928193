/**
 * Utility functions for Apigee Log Processor
 */

import { stateManager } from './state.js';

/**
 * Mask sensitive data based on type
 * @param {string} value - The value to mask
 * @param {string} type - The type of data (e.g., 'authorization', 'client_id')
 * @returns {string} - The masked or original value based on masking state
 */
export function maskSensitiveData(value, type) {
  if (!value || !stateManager.getState().maskSensitiveData) return value;

  switch (type) {
    case 'authorization':
      if (value.toLowerCase().trim().startsWith('basic ')) {
        return 'Basic *** masked ***';
      } else if (value.toLowerCase().trim().startsWith('bearer ')) {
        return 'Bearer *** masked ***';
      } else {
        return '*** masked ***';
      }
    case 'client_id':
    case 'token':
    case 'key':
    case 'cookie':
    case 'vfhu-identityprofilejwt':
      return '*** masked ***';
    default:
      // Check for sensitive keywords in the value
      if (value.toLowerCase().includes('api-key') ||
          value.toLowerCase().includes('secret')) {
        return '*** masked ***';
      }
      return value;
  }
}

/**
 * Mask sensitive data in text content (for headers, message bodies, etc.)
 * @param {string} content - The content to mask
 * @returns {string} - The masked content
 */
export function maskContent(content) {
  if (!content || !stateManager.getState().maskSensitiveData) return content;

  // Mask client_id in both JSON formats
  content = content.replace(/("client_id":\s*")[^"]*(")/g, '$1*** masked ***$2');
  content = content.replace(/(""client_id"":\s*"")[^""]*("")/g, '$1*** masked ***$2');

  // Mask authorization headers - case insensitive match but preserve original case
  content = content.replace(/Authorization:(?:\s*)(Basic|Bearer)?(?:\s*)([^\s\n<]+)/gi, function(match, type) {
    // Skip if already masked
    if (match.includes('*** masked ***')) {
      return match;
    }

    // Extract the original case of "Authorization" from the match
    const authPart = match.substring(0, match.indexOf(':'));

    // Reconstruct with the same type (if present) but masked token
    if (type) {
      return `${authPart}: ${type} *** masked ***`;
    } else {
      return `${authPart}: *** masked ***`;
    }
  });

  // Mask other sensitive headers
  content = content.replace(/(Token|Key|Cookie|vfhu-identityprofilejwt):\s*([^\s\n<]+)/gi, function(match) {
    // Extract the original case of the header name from the match
    const headerPart = match.substring(0, match.indexOf(':'));
    // Mask the value
    return headerPart + ': *** masked ***';
  });

  return content;
}

/**
 * Highlight search terms in text
 * @param {string} text - The text to highlight
 * @returns {string} - The text with highlighted search terms
 */
export function highlightSearchTerms(text) {
  if (!text) return '';

  const { currentViewType, filterState } = stateManager.getState();
  const currentFilters = filterState[currentViewType];

  // If no search term or text is already HTML, return as is
  if (!currentFilters.searchTerm || text.includes('<span')) return text;

  const searchTerm = currentFilters.searchTerm.toLowerCase();

  // Parse search terms with AND/OR operators using the same function as in state.js
  function parseSearchTerms(searchTerm) {
    const hasAnd = searchTerm.includes(' and ');
    const hasOr = searchTerm.includes(' or ');

    if (hasAnd && !hasOr) {
      // AND search
      return searchTerm.split(' and ').map(term => term.trim()).filter(term => term);
    } else if (hasOr && !hasAnd) {
      // OR search
      return searchTerm.split(' or ').map(term => term.trim()).filter(term => term);
    } else if (hasAnd && hasOr) {
      // Complex search with both AND and OR - treat as simple search for now
      return [searchTerm];
    } else {
      // Simple search
      return [searchTerm];
    }
  }

  const terms = parseSearchTerms(searchTerm);

  // Check if the text already contains HTML entities (already escaped)
  const isAlreadyEscaped = text.includes('&lt;') || text.includes('&gt;') || text.includes('&amp;');

  // Only escape HTML special characters if the text hasn't already been escaped
  let safeText = isAlreadyEscaped ? text : text.replace(/&/g, '&amp;')
                                              .replace(/</g, '&lt;')
                                              .replace(/>/g, '&gt;')
                                              .replace(/"/g, '&quot;')
                                              .replace(/'/g, '&#039;');

  // Highlight each term
  terms.forEach(term => {
    if (!term) return;

    // Create a regex that matches the term with word boundaries if possible
    const regex = new RegExp(`(${term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');

    // Replace with highlighted version
    safeText = safeText.replace(regex, '<span class="search-highlight">$1</span>');
  });

  return safeText;
}
