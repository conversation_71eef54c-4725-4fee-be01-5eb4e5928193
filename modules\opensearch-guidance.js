/**
 * OpenSearch Connection Guidance System
 * Provides step-by-step guidance for resolving connection issues
 */

export class OpenSearchGuidance {
  constructor() {
    this.guidanceModal = null;
  }

  /**
   * Initialize the guidance system
   */
  init() {
    this.createGuidanceModal();
  }

  /**
   * Show guidance based on error type
   * @param {string} errorType - Type of error (CORS, CERTIFICATE, CONNECTION)
   * @param {Object} details - Additional error details
   */
  showGuidance(errorType, details = {}) {
    const guidance = this.getGuidanceForError(errorType, details);
    this.displayGuidance(guidance);
  }

  /**
   * Get guidance content for specific error type
   * @param {string} errorType - Error type
   * @param {Object} details - Error details
   * @returns {Object} Guidance content
   */
  getGuidanceForError(errorType, details) {
    switch (errorType) {
      case 'CORS':
        return this.getCORSGuidance(details);
      case 'CERTIFICATE':
        return this.getCertificateGuidance(details);
      case 'CONNECTION':
        return this.getConnectionGuidance(details);
      default:
        return this.getGeneralGuidance(details);
    }
  }

  /**
   * Get CORS guidance
   */
  getCORSGuidance(details) {
    return {
      title: 'CORS Policy Blocking Connection',
      description: 'Your browser is blocking the connection to OpenSearch due to CORS (Cross-Origin Resource Sharing) policy.',
      icon: 'bi-shield-exclamation',
      severity: 'warning',
      solutions: [
        {
          title: '🎯 Recommended: Configure OpenSearch CORS',
          description: 'Ask your OpenSearch administrator to enable CORS',
          steps: [
            'Contact your OpenSearch administrator',
            'Ask them to add these settings to opensearch.yml:',
            '<code>http.cors.enabled: true</code>',
            '<code>http.cors.allow-origin: "*"</code>',
            '<code>http.cors.allow-headers: "X-Requested-With,Content-Type,Content-Length,Authorization"</code>',
            '<code>http.cors.allow-methods: "GET,POST,PUT,DELETE,OPTIONS"</code>',
            'Restart OpenSearch service',
            'Try connecting again'
          ],
          difficulty: 'Easy (requires admin)',
          recommended: true
        },
        {
          title: '🔧 Alternative: Use Manual Query Method',
          description: 'Use curl or Postman to query OpenSearch manually',
          steps: [
            'Open Command Prompt or Terminal',
            'Use this curl command to test:',
            `<code>curl -k -u "username:password" "${details.endpoint || 'https://bslogmesprod1.hu:9200'}/_cluster/health"</code>`,
            'Copy the query results',
            'Use the file upload feature to process the results'
          ],
          difficulty: 'Medium',
          recommended: false
        },
        {
          title: '⚠️ Development Only: Disable Browser Security',
          description: 'Temporarily disable CORS in your browser (NOT for production)',
          steps: [
            'Close all Chrome browser windows',
            'Open Command Prompt as Administrator',
            'Run: <code>chrome.exe --disable-web-security --user-data-dir="c:/temp/chrome"</code>',
            'A new Chrome window will open with security disabled',
            'Try connecting again',
            '⚠️ WARNING: Only use this for development, never for production'
          ],
          difficulty: 'Easy',
          recommended: false,
          warning: true
        }
      ]
    };
  }

  /**
   * Get certificate guidance
   */
  getCertificateGuidance(details) {
    return {
      title: 'SSL Certificate Issue',
      description: 'The OpenSearch server uses a self-signed or untrusted SSL certificate.',
      icon: 'bi-shield-x',
      severity: 'info',
      solutions: [
        {
          title: '🔒 Accept Certificate in Browser',
          description: 'Accept the SSL certificate to establish trust',
          steps: [
            `Click this link to open OpenSearch: <a href="${details.endpoint || 'https://bslogmesprod1.hu:9200'}" target="_blank">${details.endpoint || 'https://bslogmesprod1.hu:9200'}</a>`,
            'You will see a security warning',
            'Click "Advanced" or "Show details"',
            'Click "Proceed to [domain] (unsafe)" or "Accept the risk"',
            'You should see OpenSearch cluster information',
            'Return to this page and try connecting again'
          ],
          difficulty: 'Easy',
          recommended: true
        }
      ]
    };
  }

  /**
   * Get connection guidance
   */
  getConnectionGuidance(details) {
    return {
      title: 'Connection Failed',
      description: 'Unable to establish connection to OpenSearch server.',
      icon: 'bi-wifi-off',
      severity: 'danger',
      solutions: [
        {
          title: '🔍 Check Network and Credentials',
          description: 'Verify basic connectivity and authentication',
          steps: [
            'Verify the OpenSearch URL is correct',
            'Check your username and password',
            'Test with curl command:',
            `<code>curl -k -u "username:password" "${details.endpoint || 'https://bslogmesprod1.hu:9200'}/_cluster/health"</code>`,
            'Check if you can access the URL in your browser',
            'Verify your network connection'
          ],
          difficulty: 'Easy',
          recommended: true
        },
        {
          title: '🛠️ Server-Side Troubleshooting',
          description: 'Check OpenSearch server status',
          steps: [
            'Verify OpenSearch service is running',
            'Check OpenSearch logs for errors',
            'Verify the server is accessible from your network',
            'Check firewall settings',
            'Verify the correct port (usually 9200)'
          ],
          difficulty: 'Medium (requires admin)',
          recommended: false
        }
      ]
    };
  }

  /**
   * Get general guidance
   */
  getGeneralGuidance(details) {
    return {
      title: 'Connection Issue',
      description: 'An unexpected error occurred while connecting to OpenSearch.',
      icon: 'bi-exclamation-triangle',
      severity: 'warning',
      solutions: [
        {
          title: '🔄 Basic Troubleshooting',
          description: 'Try these basic steps first',
          steps: [
            'Check your internet connection',
            'Verify the OpenSearch URL and credentials',
            'Try refreshing the page',
            'Clear your browser cache',
            'Try using a different browser'
          ],
          difficulty: 'Easy',
          recommended: true
        }
      ]
    };
  }

  /**
   * Create the guidance modal
   */
  createGuidanceModal() {
    const modalHtml = `
      <div class="modal fade" id="opensearchGuidanceModal" tabindex="-1" aria-labelledby="opensearchGuidanceModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="opensearchGuidanceModalLabel">
                <i id="guidanceIcon" class="bi bi-info-circle me-2"></i>
                <span id="guidanceTitle">Connection Guidance</span>
              </h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div id="guidanceContent">
                <!-- Content will be populated dynamically -->
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
              <button type="button" class="btn btn-primary" id="retryConnectionBtn">
                <i class="bi bi-arrow-clockwise me-1"></i>Retry Connection
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    // Add modal to page if it doesn't exist
    if (!document.getElementById('opensearchGuidanceModal')) {
      document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    // Initialize Bootstrap modal
    this.guidanceModal = new bootstrap.Modal(document.getElementById('opensearchGuidanceModal'));

    // Set up retry button
    document.getElementById('retryConnectionBtn').addEventListener('click', () => {
      this.guidanceModal.hide();
      // Trigger retry connection event
      document.dispatchEvent(new CustomEvent('retryOpenSearchConnection'));
    });
  }

  /**
   * Display guidance in the modal
   * @param {Object} guidance - Guidance content
   */
  displayGuidance(guidance) {
    // Update modal title and icon
    document.getElementById('guidanceTitle').textContent = guidance.title;
    document.getElementById('guidanceIcon').className = `bi ${guidance.icon} me-2`;

    // Update modal header color based on severity
    const modalHeader = document.querySelector('#opensearchGuidanceModal .modal-header');
    modalHeader.className = `modal-header bg-${guidance.severity === 'danger' ? 'danger' : guidance.severity === 'warning' ? 'warning' : 'info'} text-white`;

    // Build content HTML
    let contentHtml = `
      <div class="alert alert-${guidance.severity} mb-4">
        <i class="bi ${guidance.icon} me-2"></i>
        ${guidance.description}
      </div>
    `;

    guidance.solutions.forEach((solution, index) => {
      const badgeClass = solution.recommended ? 'bg-success' : solution.warning ? 'bg-warning' : 'bg-secondary';
      const badgeText = solution.recommended ? 'Recommended' : solution.warning ? 'Use with Caution' : 'Alternative';

      contentHtml += `
        <div class="card mb-3 ${solution.recommended ? 'border-success' : ''}">
          <div class="card-header d-flex justify-content-between align-items-center">
            <h6 class="mb-0">${solution.title}</h6>
            <span class="badge ${badgeClass}">${badgeText}</span>
          </div>
          <div class="card-body">
            <p class="card-text">${solution.description}</p>
            <div class="difficulty mb-2">
              <small class="text-muted">
                <i class="bi bi-speedometer2 me-1"></i>
                Difficulty: ${solution.difficulty}
              </small>
            </div>
            <ol class="list-group list-group-numbered">
              ${solution.steps.map(step => `<li class="list-group-item border-0 ps-0">${step}</li>`).join('')}
            </ol>
          </div>
        </div>
      `;
    });

    document.getElementById('guidanceContent').innerHTML = contentHtml;

    // Show the modal
    this.guidanceModal.show();
  }
}

// Create and export singleton instance
export const opensearchGuidance = new OpenSearchGuidance();
