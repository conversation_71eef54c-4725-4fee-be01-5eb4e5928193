/**
 * Export Manager for Apigee Log Processor
 * Handles exporting data in various formats
 */
import { stateManager } from './state.js';
import { uiManager } from './ui.js';
import { logProcessor } from './processor.js';
import { maskContent } from './utilities.js';

export class ExportManager {
  constructor() {
    // Export options
    this.exportOptions = {
      xlsx: {
        label: 'Excel (.xlsx)',
        handler: this.exportToExcel.bind(this)
      },
      txt: {
        label: 'Text (.txt)',
        handler: this.exportToText.bind(this)
      },
      jpeg: {
        label: 'Image (.jpeg)',
        handler: this.exportToImage.bind(this)
      }
    };
  }

  /**
   * Initialize the export manager
   */
  init() {
    // Initialize export dropdown
    this.initExportDropdown();
  }

  /**
   * Initialize export dropdown
   */
  initExportDropdown() {
    const exportButton = document.getElementById('exportButton');
    const exportDropdown = document.getElementById('exportControls');

    if (!exportButton || !exportDropdown) {
      console.warn('Export elements not found');
      return;
    }

    // We're using existing elements, no need to clear

    // Use existing export items instead of creating new ones
    const exportXLSX = document.getElementById('exportXLSX');
    const exportTXT = document.getElementById('exportTXT');
    const exportJPEG = document.getElementById('exportJPEG');

    if (exportXLSX) {
      exportXLSX.addEventListener('click', (e) => {
        e.preventDefault();
        this.exportToExcel();
      });
    }

    if (exportTXT) {
      exportTXT.addEventListener('click', (e) => {
        e.preventDefault();
        this.exportToText();
      });
    }

    if (exportJPEG) {
      exportJPEG.addEventListener('click', (e) => {
        e.preventDefault();
        this.exportToImage();
      });
    }

    // Toggle dropdown on button click
    exportButton.addEventListener('click', (e) => {
      e.preventDefault();
      e.stopPropagation();
      // Use uiManager.toggleDropdown to ensure all other dropdowns are closed
      uiManager.toggleDropdown(exportDropdown);
    });
  }

  /**
   * Export data to Excel
   */
  exportToExcel() {
    try {
      const currentViewType = stateManager.getState().currentViewType;
      // Use getFilteredEntries to get all filtered entries, not just the paginated ones
      const filteredEntries = stateManager.getFilteredEntries();

      // Access XLSX through the window object
      if (typeof window.XLSX === 'undefined') {
        throw new Error('XLSX library not loaded');
      }

      // Create workbook
      const wb = window.XLSX.utils.book_new();

      if (currentViewType === 'flows') {
        // Export flows view
        this.exportFlowsToExcel(wb, filteredEntries);
      } else if (currentViewType === 'calls') {
        // Export calls view
        this.exportCallsToExcel(wb, filteredEntries);
      }

      // Generate filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `apigee-logs-${timestamp}.xlsx`;

      // Save file
      window.XLSX.writeFile(wb, filename);

      // Show success message
      uiManager.showToast(`Exported to ${filename}`);
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      uiManager.showToast('Error exporting to Excel', true);
    }
  }

  /**
   * Export flows view to Excel
   * @param {Object} wb - The workbook
   * @param {Array} entries - The entries to export
   */
  exportFlowsToExcel(wb, entries) {
    // Get column visibility
    const columnVisibility = stateManager.getState().columnVisibilityState.flows;

    // Define columns to export
    const columns = [
      { key: 'time', header: 'Time', visible: columnVisibility['time'] },
      { key: 'env', header: 'Environment', visible: columnVisibility['env-org'] },
      { key: 'messageId', header: 'Message ID', visible: columnVisibility['message-id'] },
      { key: 'flow', header: 'Flow', visible: columnVisibility['flow'] },
      { key: 'appName', header: 'App Name', visible: columnVisibility['app-name'] },
      { key: 'uri', header: 'URI', visible: columnVisibility['uri'] },
      { key: 'statusCode', header: 'Status Code', visible: columnVisibility['status-code'] }
    ];

    // Filter visible columns
    const visibleColumns = columns.filter(col => col.visible);

    // Create headers
    const headers = visibleColumns.map(col => col.header);

    // Create data rows
    const data = entries.map(entry => {
      return visibleColumns.map(col => entry[col.key] || '-');
    });

    // Create worksheet
    const ws = window.XLSX.utils.aoa_to_sheet([headers, ...data]);

    // Add worksheet to workbook
    window.XLSX.utils.book_append_sheet(wb, ws, 'Flows');
  }

  /**
   * Export calls view to Excel
   * @param {Object} wb - The workbook
   * @param {Array} entries - The entries to export
   */
  exportCallsToExcel(wb, entries) {
    // Get column visibility
    const columnVisibility = stateManager.getState().columnVisibilityState.calls;

    // Define columns to export
    const columns = [
      { key: 'time', header: 'Time', visible: columnVisibility['time'] },
      { key: 'env', header: 'Environment', visible: columnVisibility['env-org'] },
      { key: 'messageId', header: 'Message ID', visible: columnVisibility['message-id'] },
      { key: 'httpMethod', header: 'HTTP Method', visible: columnVisibility['method'] },
      { key: 'uri', header: 'URI', visible: columnVisibility['uri'] },
      { key: 'responseTime', header: 'Response Time', visible: columnVisibility['response-time'] },
      { key: 'statusCode', header: 'Status Code', visible: columnVisibility['status-code'] }
    ];

    // Filter visible columns
    const visibleColumns = columns.filter(col => col.visible);

    // Create headers
    const headers = visibleColumns.map(col => col.header);

    // Use logProcessor to aggregate entries by call
    // This properly handles status codes from different flow types
    const calls = logProcessor.aggregateEntriesByCall(entries);

    // Create data rows
    const data = calls.map(call => {
      return visibleColumns.map(col => call[col.key] || '-');
    });

    // Create worksheet
    const ws = window.XLSX.utils.aoa_to_sheet([headers, ...data]);

    // Add worksheet to workbook
    window.XLSX.utils.book_append_sheet(wb, ws, 'Calls');
  }

  /**
   * Calculate response time between request and response flows
   * @param {Array} entries - The entries for a message ID
   * @returns {string} The response time
   */
  calculateResponseTime(entries) {
    // Use the logProcessor's implementation for consistency
    return logProcessor.calculateResponseTime(entries);
  }

  /**
   * Export data to text
   */
  exportToText() {
    try {
      // Use getFilteredEntries to get all filtered entries, not just the paginated ones
      const filteredEntries = stateManager.getFilteredEntries();

      // Export detailed logs to text with masking always enabled for security
      const content = this.exportDetailedLogsToText(filteredEntries, {
        mask: true, // Always mask sensitive data in global exports
        maskContent: { maskContent } // Pass the imported maskContent function
      });

      // Create blob
      const blob = new Blob([content], { type: 'text/plain' });

      // Generate filename
      const now = new Date();
      const dateStr = now.toISOString().split('T')[0];
      const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-');
      const filename = `apigee_log_export_${dateStr}T${timeStr}.txt`;

      // Create download link
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;

      // Trigger download
      document.body.appendChild(a);
      a.click();

      // Clean up
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      // Show success message
      uiManager.showToast(`Exported to ${filename}`);
    } catch (error) {
      console.error('Error exporting to text:', error);
      uiManager.showToast('Error exporting to text', true);
    }
  }

  /**
   * Export detailed logs to text (following reference implementation)
   * @param {Array} entries - The entries to export
   * @param {Object} options - Export options
   * @returns {string} The text content
   */
  exportDetailedLogsToText(entries, options = {}) {
    let txtContent = '';
    let isFirstEntry = true;

    // Default to masked for exports unless explicitly set to false
    const shouldMask = options.mask !== false;

    // Import maskContent function if needed
    const { maskContent } = options.maskContent || window.maskContent || {};

    entries.forEach(entry => {
      // Create flow header for each entry
      let spacing;
      switch (entry.flow) {
        case 'TARGET_RESP_FLOW':
          spacing = ['           ', '             '];
          break;
        case 'PROXY_RESP_FLOW':
        case 'TARGET_REQ_FLOW':
          spacing = ['            ', '             '];
          break;
        case 'PROXY_REQ_FLOW':
          spacing = ['             ', '             '];
          break;
        default:
          spacing = ['            ', '             '];
      }

      // Add header without leading newline for first entry
      const stars = '*'.repeat(60);
      const headerContent = `${stars}\n${'*'.repeat(10)}${spacing[0]}${entry.flow}${spacing[1]}${'*'.repeat(10)}\n${stars}\n\n`;
      txtContent += isFirstEntry ? headerContent : '\n' + headerContent;
      isFirstEntry = false;

      // Prepare pre section with client_id (will be masked only if it has a value)
      const preData = {
        time: entry.time,
        level: entry.level || '',
        messageid: entry.messageId,
        flow: entry.flow,
        flowstage: entry.flowstage || '',
        requestid: entry.requestId || '',
        correlationid: entry.correlationId || '',
        uri: entry.uri,
        verb: entry.verb,
        client_id: entry.client_id || '',
        app_name: entry.appName,
        statuscode: entry.statusCode
      };

      // Replace dash placeholders with empty strings
      Object.keys(preData).forEach(key => {
        if (preData[key] === '-') {
          preData[key] = '';
        }
      });

      // Apply masking to client_id only if it has a value
      if (shouldMask && preData.client_id && preData.client_id !== '') {
        preData.client_id = "*** masked ***";
      }

      // Format JSON with commas but no spaces after colons for consistent formatting
      const formattedJson = JSON.stringify(preData)
        .replace(/,/g, ', ')
        .replace(/"([^"]+)":/g, '"$1":');

      txtContent += formattedJson + '\n\n';

      // Get query string, headers and message body
      let queryString = entry.queryString || '';
      let headers = entry.headers || '';
      let messageBody = entry.messageBody || '';

      // Apply masking to headers and message body if needed
      if (shouldMask) {
        // Mask headers using the maskContent utility
        if (headers && headers !== 'No headers available') {
          // Use the imported maskContent function or fall back to a simple regex replacement
          if (typeof maskContent === 'function') {
            headers = maskContent(headers);
          } else {
            // Basic masking for authorization headers
            headers = headers.replace(/Authorization:(?:\s*)(Basic|Bearer)?(?:\s*)([^\s\n<]+)/gi,
              (match, type) => {
                const authPart = match.substring(0, match.indexOf(':'));
                return type ? `${authPart}: ${type} *** masked ***` : `${authPart}: *** masked ***`;
              });

            // Mask other sensitive headers
            headers = headers.replace(/(Token|Key|Cookie|vfhu-identityprofilejwt):\s*([^\s\n<]+)/gi,
              (match) => {
                const headerPart = match.substring(0, match.indexOf(':'));
                return `${headerPart}: *** masked ***`;
              });
          }
        }

        // Mask message body if it contains sensitive data
        if (messageBody && typeof maskContent === 'function') {
          messageBody = maskContent(messageBody);
        }
      }

      // Always include all sections with their headers, even if empty
      txtContent += '---------- Query string:\n' + queryString + '\n\n';
      txtContent += '---------- HTTPHeaders:\n' + headers + '\n\n';
      txtContent += '---------- Message body:\n' + messageBody + '\n\n';
    });

    return txtContent;
  }

  /**
   * Export flows view to text
   * @param {Array} entries - The entries to export
   * @returns {string} The text content
   */
  exportFlowsToText(entries) {
    // Get column visibility
    const columnVisibility = stateManager.getState().columnVisibilityState.flows;

    // Define columns to export
    const columns = [
      { key: 'time', header: 'Time', visible: columnVisibility['time'] },
      { key: 'env', header: 'Environment', visible: columnVisibility['env-org'] },
      { key: 'messageId', header: 'Message ID', visible: columnVisibility['message-id'] },
      { key: 'flow', header: 'Flow', visible: columnVisibility['flow'] },
      { key: 'appName', header: 'App Name', visible: columnVisibility['app-name'] },
      { key: 'uri', header: 'URI', visible: columnVisibility['uri'] },
      { key: 'statusCode', header: 'Status Code', visible: columnVisibility['status-code'] }
    ];

    // Filter visible columns
    const visibleColumns = columns.filter(col => col.visible);

    // Create headers
    const headers = visibleColumns.map(col => col.header);

    // Calculate column widths
    const columnWidths = headers.map((header, index) => {
      const key = visibleColumns[index].key;
      const maxEntryLength = entries.reduce((max, entry) => {
        const value = String(entry[key] || '-');
        return Math.max(max, value.length);
      }, 0);
      return Math.max(header.length, maxEntryLength) + 2;
    });

    // Create header row
    let content = headers.map((header, index) => {
      return header.padEnd(columnWidths[index]);
    }).join(' | ') + '\n';

    // Create separator row
    content += headers.map((_, index) => {
      return '-'.repeat(columnWidths[index]);
    }).join('-+-') + '\n';

    // Create data rows
    entries.forEach(entry => {
      const row = visibleColumns.map((col, index) => {
        const value = String(entry[col.key] || '-');
        return value.padEnd(columnWidths[index]);
      }).join(' | ');
      content += row + '\n';
    });

    return content;
  }

  /**
   * Export calls view to text
   * @param {Array} entries - The entries to export
   * @returns {string} The text content
   */
  exportCallsToText(entries) {
    // Get column visibility
    const columnVisibility = stateManager.getState().columnVisibilityState.calls;

    // Define columns to export
    const columns = [
      { key: 'time', header: 'Time', visible: columnVisibility['time'] },
      { key: 'env', header: 'Environment', visible: columnVisibility['env-org'] },
      { key: 'messageId', header: 'Message ID', visible: columnVisibility['message-id'] },
      { key: 'httpMethod', header: 'HTTP Method', visible: columnVisibility['method'] },
      { key: 'uri', header: 'URI', visible: columnVisibility['uri'] },
      { key: 'responseTime', header: 'Response Time', visible: columnVisibility['response-time'] },
      { key: 'statusCode', header: 'Status Code', visible: columnVisibility['status-code'] }
    ];

    // Filter visible columns
    const visibleColumns = columns.filter(col => col.visible);

    // Create headers
    const headers = visibleColumns.map(col => col.header);

    // Use logProcessor to aggregate entries by call
    // This properly handles status codes from different flow types
    const calls = logProcessor.aggregateEntriesByCall(entries);

    // Calculate column widths
    const columnWidths = headers.map((header, index) => {
      const key = visibleColumns[index].key;
      let maxEntryLength = 0;

      calls.forEach(call => {
        const value = String(call[key] || '-');
        maxEntryLength = Math.max(maxEntryLength, value.length);
      });

      return Math.max(header.length, maxEntryLength) + 2;
    });

    // Create header row
    let content = headers.map((header, index) => {
      return header.padEnd(columnWidths[index]);
    }).join(' | ') + '\n';

    // Create separator row
    content += headers.map((_, index) => {
      return '-'.repeat(columnWidths[index]);
    }).join('-+-') + '\n';

    // Create data rows
    calls.forEach(call => {
      // Create row
      const row = visibleColumns.map((col, index) => {
        const value = String(call[col.key] || '-');
        return value.padEnd(columnWidths[index]);
      }).join(' | ');

      content += row + '\n';
    });

    return content;
  }

  /**
   * Export data to image
   * Note: This exports only the currently visible table (current page)
   */
  exportToImage() {
    try {
      const table = document.getElementById('logTable');
      if (!table) {
        uiManager.showToast('No table to export', true);
        return;
      }

      // Access html2canvas through the window object
      if (typeof window.html2canvas === 'undefined') {
        throw new Error('html2canvas library not loaded');
      }

      // Use html2canvas to capture the table
      window.html2canvas(table).then(canvas => {
        // Convert canvas to JPEG
        const imgData = canvas.toDataURL('image/jpeg');

        // Generate filename
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `apigee-logs-${timestamp}.jpeg`;

        // Create download link
        const a = document.createElement('a');
        a.href = imgData;
        a.download = filename;

        // Trigger download
        document.body.appendChild(a);
        a.click();

        // Clean up
        document.body.removeChild(a);

        // Show success message
        uiManager.showToast(`Exported to ${filename} (current page only)`);
      });
    } catch (error) {
      console.error('Error exporting to image:', error);
      uiManager.showToast('Error exporting to image', true);
    }
  }

  /**
   * Export a single entry or all entries for a call to a TXT file
   * @param {Object|Array} entryOrEntries - The entry or entries to export
   */
  exportLogDetailsToFile(entryOrEntries) {
    try {
      // Get current masking state from stateManager
      const isMasked = stateManager.getState().maskSensitiveData;

      // Import maskContent function from utilities.js
      import('../modules/utilities.js').then(utilities => {
        let content = '';

        if (Array.isArray(entryOrEntries)) {
          // For calls view - export all flow entries
          content = this.exportDetailedLogsToText(entryOrEntries, {
            mask: isMasked, // Respect current mask status
            maskContent: { maskContent: utilities.maskContent }
          });
        } else {
          // For flows view - export single entry
          content = this.exportDetailedLogsToText([entryOrEntries], {
            mask: isMasked, // Respect current mask status
            maskContent: { maskContent: utilities.maskContent }
          });
        }

        // Create blob
        const blob = new Blob([content], { type: 'text/plain' });

        // Generate filename with message ID if available
        const messageId = Array.isArray(entryOrEntries)
          ? entryOrEntries[0]?.messageId
          : entryOrEntries?.messageId;

        const now = new Date();
        const dateStr = now.toISOString().split('T')[0];
        const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-');

        let filename = `apigee_log_export_${dateStr}T${timeStr}.txt`;
        if (messageId) {
          // Truncate message ID if it's too long
          const shortMessageId = messageId.length > 20
            ? messageId.substring(0, 20) + '...'
            : messageId;
          filename = `apigee_log_${shortMessageId}_${dateStr}T${timeStr}.txt`;
        }

        // Create download link
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;

        // Trigger download
        document.body.appendChild(a);
        a.click();

        // Clean up
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        // Show visual feedback on the export button only (no toast)
        const exportLogBtn = document.getElementById('exportLogBtn');
        if (exportLogBtn) {
          const originalIcon = exportLogBtn.innerHTML;
          exportLogBtn.innerHTML = '<i class="bi bi-check-lg"></i>';
          exportLogBtn.style.color = '#198754';
          setTimeout(() => {
            exportLogBtn.innerHTML = originalIcon;
            exportLogBtn.style.color = '';
          }, 1000);
        }
      }).catch(error => {
        console.error('Error importing utilities:', error);
        uiManager.showToast('Error exporting log details', true);
      });
    } catch (error) {
      console.error('Error exporting log details to file:', error);
      uiManager.showToast('Error exporting log details', true);
    }
  }
}

// Create a singleton instance
export const exportManager = new ExportManager();
