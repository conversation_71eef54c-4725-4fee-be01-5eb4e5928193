.http-headers {

    padding: 6px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.header-line {
    font-family: monospace;
    margin: 2px 0;
    color: #444;
}

.json-content {
    margin-bottom: 10px;
}
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, Roboto, Oxygen, Ubuntu, sans-serif;
    margin: 0;
    padding: 20px;
    background-color: #f5f7fa;
    font-size: 16px;
    color: #2d3748;
    line-height: 1.6;
}

.hidden {
    display: none;
}

/* Collapsible input section */
.input-section {
    max-height: 1000px;
    overflow: visible;
    opacity: 1;
    transition: max-height 0.3s ease, opacity 0.3s ease, padding 0.3s ease;
    margin-bottom: 0;
    padding: 15px 0;
    display: block;
}

.input-section.collapsed {
    max-height: 0 !important;
    overflow: hidden !important;
    padding: 0 !important;
    margin: 0 !important;
    opacity: 0 !important;
    pointer-events: none !important;
    visibility: hidden !important;
}

#toggleInputButton {
    transition: transform 0.3s ease;
    z-index: 10;
    cursor: pointer;
    margin-left: 10px;
}

#toggleInputButton i {
    display: inline-block;
}

/* Remove the rotation effect to avoid confusion */
/* #toggleInputButton.collapsed i {
    transform: rotate(180deg);
} */

#inputContainer, #processedContainer {
    max-width: 90%;
    text-align: left;
    border-radius: 12px;
    background-color: white;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.06);
    padding: 5px 30px;
    margin: 0 auto 10px;
    border: 1px solid rgba(0, 0, 0, 0.03);
    position: relative;
    z-index: 1;
}

/* Add styles for the hidden state to prevent layout shifts */
#processedContainer.hidden {
    display: none !important;
}

#inputContainer {
    margin-bottom: 20px;
}

h2, h3, h4 {
    color: #1a202c;
    font-weight: 600;
}

h2 {
    position: relative;
    padding-bottom: 10px;
    margin-bottom: 5px;
}

h3 {
    margin-bottom: 0px;
}

h2:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: #e60000;
}

.fileContent {
    max-width: 100%;
    margin-top: 0;
    white-space: pre-wrap;
}

.input-section h4 {
    color: #4a5568;
    font-weight: 500;
    letter-spacing: 0.3px;
}

button {
    margin-right: 10px;
    font-weight: 500;
}

textarea.form-control {
    border-radius: 8px;
    border-color: #e2e8f0;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

textarea.form-control:focus {
    box-shadow: 0 0 0 3px rgba(230, 0, 0, 0.15);
    border-color: #e60000;
}

input.form-control {
    border-radius: 8px;
    transition: all 0.3s ease;
    border-color: #e2e8f0;
}

input.form-control:focus {
    box-shadow: 0 0 0 3px rgba(230, 0, 0, 0.15);
    border-color: #e60000;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0b5ed7;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(13, 110, 253, 0.25);
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-danger:hover {
    background-color: #bb2d3b;
    border-color: #bb2d3b;
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(220, 53, 69, 0.25);
}

.btn-secondary {
    transition: all 0.3s ease;
    font-weight: 500;
    background-color: #edf2f7;
    border-color: #edf2f7;
    color: #4a5568;
    margin: 15px;
}

.btn-secondary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    background-color: #e2e8f0;
    border-color: #e2e8f0;
    color: #2d3748;
}

.btn-secondary:disabled {
    background-color: #f7fafc;
    border-color: #f7fafc;
    color: #a0aec0;
}

hr {
    opacity: 0.12;
    margin: 30px 0;
}

#countDisplay {
    text-align: left;
    margin-top: 20px;
}

#processedContainer {
    padding-bottom: 30px;
}

.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    background-color: #e60000;
    color: white;
    width: 48px;
    height: 48px;
    border: none;
    border-radius: 50%;
    box-shadow: 0 4px 14px rgba(230, 0, 0, 0.3);
    font-size: 20px;
    cursor: pointer;
    z-index: 1000;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    outline: none;
}

.back-to-top:hover {
    background-color: #cc0000;
    transform: translateY(-4px);
    box-shadow: 0 6px 18px rgba(230, 0, 0, 0.35);
}

.back-to-top.hidden {
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.log-key {
    color: #e05252;
    font-weight: 600;
    margin-right: 3px;
}

.log-string {
    color: #22863a;
    margin-bottom: 4px;
    display: inline-block;
}

.log-number {
    color: #0366d6;
    margin-bottom: 4px;
    display: inline-block;
}

.log-boolean {
    color: #e36209;
    margin-bottom: 4px;
    display: inline-block;
}

.log-null {
    color: #6a737d;
    margin-bottom: 4px;
    display: inline-block;
}

#currentLogEntry {
    margin-bottom: 30px;
    position: relative;
    border-radius: 8px;
    background-color: #ffffff;
    box-shadow: 0 4px 14px rgba(0, 0, 0, 0.08);
    display: flex;
    flex-direction: column;
    gap: 8px;
}

#currentLogEntry pre {
    overflow-x: auto;
    padding: 20px;
    font-size: 15px;
    line-height: 1.5;
    background-color: #fcfcfc;
    border-radius: 0 0 8px 8px;
    border: none;
    border-top: none;
    margin-bottom: 0;
}

/* General button styles */
.btn-group label.btn {
    cursor: pointer;
    transition: all 0.3s ease;
    border-width: 2px;
    font-weight: 300;
    font-size: 0.8rem;
    padding: 6px 16px;
    border-radius: 30px;
}

.btn-group label.btn:last-child {
    margin-right: 0;
}

.btn-check:checked + .btn-outline-success {
    color: white;
    background-color: #38a169;
    box-shadow: 0 2px 10px rgba(56, 161, 105, 0.3);
}

.btn-check:checked + .btn-outline-primary {
    color: white;
    background-color: #3182ce;
    box-shadow: 0 2px 10px rgba(49, 130, 206, 0.3);
}

.btn-check:checked + .btn-outline-warning {
    color: white;
    background-color: #d69e2e;
    box-shadow: 0 2px 10px rgba(214, 158, 46, 0.3);
}

.btn-check:checked + .btn-outline-danger {
    color: white;
    background-color: #e53e3e;
    box-shadow: 0 2px 10px rgba(229, 62, 62, 0.3);
}

/* XML content styling improvements */
.log-xml-content {
    color: #0366d6;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    white-space: pre-wrap;
    word-break: break-word;
    background-color: #f8f9fa;
    display: block;
    margin-top: 3px;
    padding: 12px 16px;
    border-left: 3px solid #0366d6;
    font-size: 15px;
    border-radius: 0 6px 6px 0;
    max-height: 600px;
    overflow-y: auto;
    line-height: 1.5;
}

/* XML element styling */
.xml-tag {
    color: #22863a;
    font-weight: 500;
}

.xml-attr-name {
    color: #6f42c1;
}

.xml-attr-value {
    color: #e36209;
}

.xml-text {
    color: #24292e;
}

.xml-comment {
    color: #6a737d;
    font-style: italic;
}

.xml-cdata {
    color: #032f62;
    background-color: rgba(0, 0, 0, 0.05);
    padding: 1px 3px;
    border-radius: 3px;
}

/* Preserve XML structure spacing */
.log-xml-content .xml-tag + .xml-tag,
.log-xml-content .xml-text + .xml-tag {
    margin-left: 4px;
}

/* Base64 content formatting */
.log-xml-content .base64-content {
    color: #666;
    background-color: #f0f0f0;
    padding: 4px;
    border-radius: 3px;
    font-size: 0.9em;
    display: block;
    margin: 4px 0;
}

.log-xml-content .truncated-indicator {
    color: #e83e8c;
    font-style: italic;
    padding: 0 4px;
}

/* Http header formatting */
.log-xml-content .http-header {
    color: #0366d6;
    font-weight: 500;
}

.log-xml-content .http-header-value {
    color: #24292e;
}

/* Empty element styling */
.log-xml-content:empty {
    display: none;
}

.response-json {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 12px;
}

.response-headers {
    padding: 12px;
}

/* HTTP message styling */
.http-message {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    background-color: #f8f9fa;
    border-left: 3px solid #4a5568;
    border-radius: 0 6px 6px 0;
    margin: 8px 0;
    padding: 12px;
    line-height: 1.5;
}

.http-start-line {
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    margin-bottom: 8px;
}

.http-method {
    color: #2563eb;  /* Blue */
    font-weight: 600;
}

.http-uri {
    color: #047857;  /* Green */
}

.http-version {
    color: #6b7280;  /* Gray */
}

.http-status-code {
    color: #2563eb;  /* Blue */
    font-weight: 600;
}

.http-reason-phrase {
    color: #374151;  /* Dark Gray */
}

.http-header-line {
    padding: 2px 0;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.http-header-name {
    color: #2563eb;  /* Blue */
    font-weight: 500;
    white-space: nowrap;
}

.http-header-value {
    color: #374151;  /* Dark Gray */
    word-break: break-all;
}

/* Plain text content styling */
/* Message content container */
.log-message-content {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin: 8px 0;
    overflow: hidden;
}

/* Message prefix styling */
.message-prefix {
    padding: 8px 12px;
    color: #2b72fe;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    font-style: italic;
    background-color: #f3f4f6;
    display: block;
}

/* Formatted payload (replaces plain-content) */
.formatted-payload {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    white-space: pre-wrap;
    word-break: break-word;
    line-height: 1.5;
    padding: 12px;
    background-color: #f8f9fa;
    border-left: 3px solid #6b7280;
    border-radius: 0 6px 6px 0;
    margin: 8px 0;
}

/* When formatted-payload is inside log-message-content */
.log-message-content .formatted-payload {
    margin: 0;
    border-left: none;
    border-radius: 0;
}

.plain-timestamp {
    color: #6366f1;  /* Indigo */
    font-weight: 500;
}

.plain-key {
    color: #2b72fe;  /* Purple */
    font-weight: 500;
}

.plain-value {
    color: #374151;  /* Dark Gray */
}

.plain-text {
    color: #374151;  /* Dark Gray */
    /* Ensure it behaves like other plain text elements */
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    white-space: pre-wrap;
    word-break: break-word;
    line-height: 1.5;
    display: block; /* Or inline-block if preferred */
}

/* Response sent styling */
.response-sent {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin: 8px 0;
    overflow: hidden;
}

.response-prefix {
    padding: 8px 12px;
    color: #4a5568;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    font-style: italic;
}

.response-json {
    border-bottom: 2px solid rgba(0, 0, 0, 0.1);
    margin: 0;
}

.response-json .json-payload {
    margin: 0;
    border-radius: 0;
}

/* HTTP Message styling */
.http-message {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    background-color: #f8f9fa;
    border-left: 3px solid #4a5568;
    border-radius: 0 6px 6px 0;
    margin: 8px 0;
    padding: 12px;
    line-height: 1.5;
}

.http-start-line {
    padding-bottom: 8px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    margin-bottom: 8px;
}

.http-method {
    color: #2563eb;  /* Blue */
    font-weight: 600;
}

.http-uri {
    color: #047857;  /* Green */
}

.http-version {
    color: #6b7280;  /* Gray */
}

.http-status-code {
    color: #2563eb;  /* Blue */
    font-weight: 600;
}

.http-reason-phrase {
    color: #374151;  /* Dark Gray */
}

/* JSON payload styling */
.json-payload {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    white-space: pre-wrap;
    word-break: break-word;
    background-color: #f8f9fa;
    display: block;
    margin-top: 3px;
    padding: 12px 16px;
    border-left: 3px solid #0366d6;
    font-size: 15px;
    border-radius: 0 6px 6px 0;
    max-height: 600px;
    overflow-y: auto;
    line-height: 1.5;
}

.json-payload .key {
    color: #e05252;
    font-weight: 600;
}

.json-payload .string {
    color: #22863a;
}

.json-payload .number {
    color: #0366d6;
}

.json-payload .boolean {
    color: #e36209;
}

.json-payload .null {
    color: #6a737d;
}

/* Indentation and formatting */
.json-payload .indent {
    margin-left: 20px;
}

.json-payload .brace {
    color: #24292e;
}

.json-payload .comma {
    color: #24292e;
}

.json-payload .colon {
    color: #24292e;
    margin: 0 4px;
}

/* Navigation controls */
.entry-navigation {
    display: flex;
    justify-content: center;
    align-items: center;

}

.entry-navigation .btn-group {
    border-radius: 30px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.entry-navigation .btn-group label skill-patch-item {
    display:none;
}

/* Log entry header */
.log-entry-header {
    display: flex;
    align-items: center;
    padding: 4px 16px;
    background-color: #f8fafc;
    border-radius: 8px 8px 0 0;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    z-index: 2;
    height: 36px;
    position: relative;
}

.level-indicator {
    display: flex;
    align-items: center;
    position: absolute;
    left: 16px;
}

.entry-counter {
    font-weight: 500;
    padding: 4px 12px;
    border-radius: 16px;
    background-color: #edf2f7;
    font-size: 12px;
    border: 1px solid rgba(0, 0, 0, 0.03);
    color: #4a5568;
    white-space: nowrap;
    text-align: center;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
}

.header-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    position: absolute;
    right: 16px;
}

.header-controls .jobid-dropdown,
.header-controls .export-button-container {
    display: inline-flex;
    align-items: center;
}

/* JobID filter */
.jobid-filter-container {
    position: relative;
    display: flex;
    align-items: center;
    height: 28px;
}

.jobid-filter-label {
    display: none;
}

.jobid-dropdown {
    position: relative;
    display: inline-block;
    height: 100%;
    vertical-align: middle;
}

.jobid-dropdown-button {
    display: inline-flex;
    align-items: center;
    justify-content: space-between;
    width: 150px;
    height: 28px;
    padding: 0 8px;
    font-size: 0.85rem;
    border-radius: 6px;
    border: 1px solid #e2e8f0;
    background-color: white;
    color: #333;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.jobid-dropdown-button:hover {
    border-color: #cbd5e0;
    background-color: #f7fafc;
}

.jobid-dropdown-button:focus {
    outline: none;
    border-color: #e60000;
    box-shadow: 0 0 0 2px rgba(230, 0, 0, 0.15);
}

.jobid-dropdown-button i {
    transition: transform 0.2s ease;
    margin-left: 5px;
}

.jobid-dropdown.open .jobid-dropdown-button i {
    transform: rotate(180deg);
}

.jobid-dropdown-menu {
    position: absolute;
    top: calc(100% + 4px);
    left: 0;
    width: 150px;
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    z-index: 1000;
    opacity: 0;
    transform: translateY(-10px);
    transition: opacity 0.2s ease, transform 0.2s ease;
    pointer-events: none;
    border: 1px solid #e2e8f0;
    display: none;
}

.jobid-dropdown.open .jobid-dropdown-menu {
    opacity: 1;
    transform: translateY(0);
    pointer-events: auto;
    display: block;
    max-height: 300px;
    overflow-y: auto;
}

.jobid-dropdown-header {
    border-bottom: 1px solid #edf2f7;
    background-color: #f8fafc;
    margin: 0;
    padding: 0;
}

.jobid-checkbox-item {
    display: flex;
    align-items: center;
    padding: 6px 10px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: background-color 0.1s ease;
    border-bottom: 1px solid #f7f7f7;
    width: 100%;
    margin: 0;
}

.jobid-checkbox-item:hover {
    background-color: #f7fafc;
}

.jobid-checkbox-item:last-child {
    border-bottom: none;
}

.jobid-checkbox-item input[type="checkbox"] {
    margin-right: 8px;
    flex-shrink: 0;
}

.jobid-item-controls {
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: space-between;
}

.select-only-button {
    margin-left: 4px;
    padding: 2px 6px;
    font-size: 10px;
    border: none;
    border-radius: 3px;
    background: #f0f0f0;
    cursor: pointer;
    opacity: 0.7;
    transition: all 0.2s ease;
}

.select-only-button:hover {
    opacity: 1;
    background: #e2e8f0;
}

/* Copy button */
.copy-button-container {
    display: flex;
    align-items: center;
    height: 28px;
}

.copy-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 26px;
    background-color: #edf2f7;
    border: none;
    border-radius: 20px;
    padding: 0 10px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #4a5568;
    white-space: nowrap;
}

.copy-button:hover {
    background-color: #e60000;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(230, 0, 0, 0.25);
}

.copy-button i {
    margin-right: 5px;
    font-size: 14px;
}

.copy-success {
    background-color: #38a169;
    color: white;
}

/* Export button */
.export-button-container {
    display: flex;
    align-items: center;
    height: 28px;
    position: relative;
}

.export-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 26px;
    background-color: #edf2f7;
    border: none;
    border-radius: 20px;
    padding: 0 10px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #4a5568;
    white-space: nowrap;
}

.export-button:hover {
    background-color: #38a169;
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(56, 161, 105, 0.25);
}

.export-button i {
    margin-right: 5px;
    font-size: 14px;
}

.export-success {
    background-color: #38a169;
    color: white;
}

.export-button-container .dropdown-menu {
    position: absolute;
    top: calc(100% + 4px);
    right: 0;
    left: auto;
    width: 180px;
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    z-index: 1000;
    border: 1px solid #e2e8f0;
    padding: 6px 0;
    font-size: 14px;
}

.export-button-container .dropdown-item {
    display: flex;
    align-items: center;
    padding: 6px 10px;
    font-size: 0.85rem;
    color: #4a5568;
    cursor: pointer;
    transition: background-color 0.1s ease;
    border-bottom: 1px solid #f7f7f7;
    width: 100%;
    margin: 0;
}

.export-button-container .dropdown-item:hover {
    background-color: #f7fafc;
}

.export-button-container .dropdown-item:last-child {
    border-bottom: none;
}

/* Toast */
.toast {
    border-radius: 8px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.toast.border-success {
    border-color: #198754 !important;
}

.toast.border-danger {
    border-color: #dc3545 !important;
}

.toast-header {
    border-radius: 8px 8px 0 0;
    font-weight: 600;
    padding: 12px 16px;
}

.toast-header strong {
    font-weight: 600;
}

.toast-body {
    padding: 16px;
    font-size: 0.95rem;
    color: #2d3748;
}

footer {
    margin-bottom: 0px;
    color: #a0aec0;
    font-size: 0.85rem;
}

/* Search */
/* Search container and wrapper */
.search-container {
    margin: 0 0 10px;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    width: auto;
    height: 30px;
    border-radius: 6px;
    background-color: white;
    border: 1px solid #e2e8f0;
    padding: 0 4px 0 20px;
    /* margin: 0 0 10px; */
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: all 0.2s ease;
}

/* Search input */
.search-input {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    font-size: 14px;
    color: #2d3748;
    padding: 4px 8px;
    min-width: 200px;
}

.search-icon {
    color: #a0aec0;
    font-size: 14px;
    margin-right: 4px;
}

/* Search controls layout */
.search-controls {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: 8px;
    padding-left: 8px;
    border-left: 1px solid #e2e8f0;
}

.search-options {
    display: flex;
    align-items: center;
    gap: 4px;
}

.search-option {
    display: flex;
    align-items: center;
    margin: 0 2px;
}

.search-option-input {
    display: none;
}

.search-option-label {
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    color: #718096;
    background-color: transparent;
    transition: all 0.2s ease;
    margin: 0;
    user-select: none;
}

.search-option-input:checked + .search-option-label {
    background-color: #e2e8f0;
    color: #2d3748;
}

.search-option-label:hover {
    background-color: #edf2f7;
}

/* Search navigation */
.search-navigation {
    display: flex;
    align-items: center;
    gap: 4px;
}

.search-nav-btn {
    /* padding: 2px 6px; */
    border: none;
    background: transparent;
    color: #718096;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin: 0px;
}

.search-nav-btn:hover:not(:disabled) {
    background-color: #edf2f7;
    color: #2d3748;
}

.search-nav-btn:disabled {
    opacity: 0.5;
    cursor: default;
}

.search-match-count {
    font-size: 12px;
    color: #718096;
    padding: 0 4px;
    min-width: 80px;
    text-align: center;
    margin: 0px;
}

/* Clear search button */
.search-clear-btn {
    padding: 2px 6px;
    border: none;
    background: transparent;
    color: #718096;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.search-clear-btn:hover {
    background-color: #edf2f7;
    color: #2d3748;
}

/* Help text */
.search-help-text {
    font-size: 12px;
    color: #718096;
    margin-top: 4px;
    padding: 0 4px;
}

/* Focus states */
.search-input-wrapper:focus-within {
    border-color: #3182ce;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.15);
}

.search-input-wrapper:focus-within {
    border-color: #3182ce;
    box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.15);
}

.search-icon {
    font-size: 14px;
    color: #718096;
    margin-right: 8px;
}

.search-input {
    flex: 1;
    border: none;
    outline: none;
    height: 100%;
    background: transparent;
    font-size: 14px;
    color: #2d3748;
    padding: 0;
}

.search-input::placeholder {
    color: #a0aec0;
}

.search-nav-btn {
    height: 24px;
    width: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background-color: transparent;
    color: #718096;
    border-radius: 4px;
    margin-left: 2px;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 0;
    font-size: 12px;
}

.search-nav-btn:hover:not(:disabled) {
    background-color: #edf2f7;
    color: #4a5568;
}

.search-nav-btn:disabled {
    opacity: 0.5;
    cursor: default;
}

.search-match-count {
    font-size: 12px;
    color: #718096;
    margin: 0 8px;
    white-space: nowrap;
}

.search-clear-btn {
    height: 24px;
    width: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background-color: transparent;
    color: #718096;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 0;
    font-size: 12px;
}

.search-clear-btn:hover {
    background-color: #edf2f7;
    color: #4a5568;
}

/* Search highlights */
.search-highlight,
.search-highlight-and,
.search-highlight-or {
    display: inline-block;
    border-radius: 2px;
    padding: 0 2px;
    margin: 0 -2px;
}

.search-highlight {
    background-color: rgba(255, 255, 0);  /* Yellow */
}

.search-highlight-and {
    background-color: rgba(72, 187, 120);  /* Green */
    box-shadow: 0 0 0 1px rgba(72, 187, 120, 0.1);
}

.search-highlight-or {
    background-color: rgba(66, 153, 225);  /* Blue */
    box-shadow: 0 0 0 1px rgba(66, 153, 225, 0.1);
}

.search-highlight-active {
    background-color: rgba(255, 165, 0);  /* Orange */
    border-radius: 2px;
    padding: 0 2px;
    margin: 0 -2px;
    box-shadow: 0 0 0 2px rgba(255, 165, 0, 0.3);
    z-index: 1;
}

/* Global search */
.global-search-highlight {
    background-color: rgba(46, 204, 113);
    border-radius: 2px;
    padding: 0 2px;
    margin: 0 -2px;
    box-shadow: 0 0 0 1px rgba(46, 204, 113, 0.2);
}

.global-search-toggle-wrapper {
    display: flex;
    align-items: center;
    margin: 0 10px;
}

.global-search-toggle {
    appearance: none;
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border: 1px solid #999;
    border-radius: 3px;
    margin-right: 5px;
    position: relative;
    cursor: pointer;
    outline: none;
}

.global-search-toggle:checked {
    background-color: #2196F3;
    border-color: #2196F3;
}

.global-search-toggle:checked::after {
    content: '✓';
    color: white;
    position: absolute;
    font-size: 12px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.global-search-label {
    font-size: 12px;
    color: #666;
    cursor: pointer;
    user-select: none;
}

.global-search-info {
    background-color: #e8f4fc;
    color: #0c5460;
    padding: 8px 12px;
    margin-bottom: 10px;
    border-radius: 4px;
    font-size: 14px;
    border: 1px solid #bee5eb;
}

/* Date filter container */
.date-filter-container {
    width: 100%;
    margin: 0 0 15px;
}

.date-filter-panel {
    background-color: white;
    border-radius: 6px;
    padding: 10px 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
    transition: all 0.2s ease;
}

.date-filter-content {
    padding: 0;
}

.date-time-picker-row {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: flex-start;
    gap: 15px;
    /* margin-bottom: 10px; */
}

.date-time-group {
    min-width: 220px;
    flex-grow: 1;
    position: relative;
    display: flex;
    align-items: center;
    gap: 8px;
}

.date-time-label {
    font-weight: 500;
    font-size: 14px;
    color: #495057;
    white-space: nowrap;
    margin-bottom: 0;
    width: 40px;
}

.date-time-inputs {
    display: flex;
    gap: 8px;
    flex: 1;
}

.date-input-wrapper {
    position: relative;
    flex: 3;
}

.time-input-wrapper {
    position: relative;
    flex: 2;
}

input[type="date"],
input[type="time"] {
    padding-right: 30px;
    width: 100%;
    font-size: 14px;
    cursor: pointer;
}

.date-icon,
.time-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    pointer-events: none;
    z-index: 1;
    font-size: 14px;
}

.date-filter-actions {
    display: flex;
    flex-direction: row;
    gap: 8px;
    align-self: flex-end;
}

.date-filter-status {
    font-size: 0.85rem;
    padding: 8px 12px;
    background-color: rgba(0, 0, 0, 0.04);
    border-radius: 6px;
    display: none;
    margin-top: 10px;
    text-align: center;
}

.date-filter-status.active {
    display: block;
}

.date-filter-status.success {
    color: #0f5132;
    background-color: rgba(25, 135, 84, 0.1);
}

.date-filter-status.warning {
    color: #664d03;
    background-color: rgba(255, 193, 7, 0.1);
}

/* Fix flickering on hover */
input[type="date"]::-webkit-calendar-picker-indicator,
input[type="time"]::-webkit-calendar-picker-indicator {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

@media (max-width: 768px) {
    .date-time-picker-row {
        flex-direction: column;
    }

    .date-filter-actions {
        flex-direction: row;
        width: 100%;
        margin-top: 10px;
    }

    .date-time-group {
        width: 100%;
    }
}

/* Firefox specific styles */
@-moz-document url-prefix() {
    input[type="date"],
    input[type="time"] {
        appearance: textfield;
        -moz-appearance: textfield;
        position: relative;
        padding-right: 30px;
    }

    .date-input-wrapper::after,
    .time-input-wrapper::after {
        content: "";
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        z-index: 2;
    }

    .date-input-wrapper::after {
        content: "\F1E7";
        font-family: "bootstrap-icons";
        font-size: 14px;
        color: #6c757d;
    }

    .time-input-wrapper::after {
        content: "\F223";
        font-family: "bootstrap-icons";
        font-size: 14px;
        color: #6c757d;
    }

    .date-icon,
    .time-icon {
        display: none;
    }
.empty-message {
    color: #999;
    font-style: italic;
}

/* Formatted message content */
.message-key {
    color: #0366d6;
    font-weight: 600;
}

.message-value {
    color: #24292e;
}

.message-block {
    padding: 8px 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    margin: 4px 0;
    border-left: 3px solid #0366d6;
}

.message-response {
    color: #24292e;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.log-json-content {
    white-space: pre-wrap;
    word-break: break-word;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #0366d6;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    line-height: 1.5;
}

.log-json-content .struct-key {
    color: #0366d6;
    font-weight: 600;
}

.log-json-content .struct-value {
    color: #24292e;
}

.log-json-content .struct-array {
    margin-left: 20px;
    padding-left: 10px;
    border-left: 1px solid #e1e4e8;
}

.log-json-content .struct-object {
    margin: 4px 0;
}

/* Response specific styling */
.response-header {
    color: #6f42c1;
    font-weight: 600;
    padding: 4px 0;
}

.response-status {
    color: #22863a;
    font-weight: 600;
}

.response-body {
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid #e1e4e8;
}



}
