// Background script for OpenSearch CORS Helper
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'opensearchRequest') {
    makeOpenSearchRequest(request.config)
      .then(response => sendResponse({ success: true, data: response }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true; // Keep message channel open for async response
  }
});

async function makeOpenSearchRequest(config) {
  const { url, method, headers, body } = config;
  
  const fetchOptions = {
    method: method || 'GET',
    headers: {
      'Content-Type': 'application/json',
      ...headers
    }
  };

  if (body) {
    fetchOptions.body = typeof body === 'string' ? body : JSON.stringify(body);
  }

  try {
    const response = await fetch(url, fetchOptions);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('OpenSearch request failed:', error);
    throw error;
  }
}
