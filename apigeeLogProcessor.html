<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apigee Log Processor</title>    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Roboto+Mono:wght@400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="apigeeLogProcessor.css">
</head>
<body>
    <!-- Theme Switcher -->
    <div class="theme-switch-wrapper">
        <label class="theme-switch" for="themeSwitch">
            <input type="checkbox" id="themeSwitch" />
            <div class="slider">
                <i class="bi bi-sun-fill slider-icon sun"></i>
                <i class="bi bi-moon-fill slider-icon moon"></i>
            </div>
        </label>
    </div>

    <div class="container" id="inputContainer">
        <div class="d-flex justify-content-between align-items-center">
            <!-- Mode Switcher on the left -->
            <div class="d-flex align-items-center gap-1">
                <label class="theme-switch" for="modeSwitch">
                    <input type="checkbox" id="modeSwitch" checked />
                    <div class="slider">
                        <i class="bi bi-file-earmark-text slider-icon sun"></i>
                        <i class="bi bi-search slider-icon moon"></i>
                    </div>
                </label>
                <!-- OpenSearch Config and Status -->
                <button id="opensearchConfigButton" class="header-config-btn" title="Configure OpenSearch">
                    <i class="bi bi-gear"></i>
                </button>
                <span id="opensearchStatus" class="header-status-badge">Not Connected</span>
            </div>
            <!-- Centered title -->
            <h2 class="text-center flex-grow-1">Apigee Log Processor</h2>
            <!-- Toggle button on the right -->
            <button class="btn btn-sm btn-outline-secondary d-none" id="toggleInputButton" title="Expand input section">
                <i class="bi bi-chevron-down"></i>
            </button>
        </div>

        <div class="input-section" id="inputSection">

            <!-- File Mode Section -->
            <div id="fileModeSection" class="hidden">
                <h4 class="mb-3">Paste text or Open from file</h4>
                <div class="mb-3">
                    <textarea id="textInput" class="form-control shadow-sm" rows="10" cols="100" placeholder="Paste your log content here..."></textarea>
                </div>
                <div class="mb-3">
                    <input type="file" id="fileInput" class="form-control shadow-sm" accept=".txt,.log,.json,.csv">
                </div>
                <div class="d-flex mt-3 mb-2">
                    <button class="btn btn-primary" id="processButton">
                        <i class="bi bi-diagram-3 me-1"></i> Process Logs
                    </button>
                    <button class="btn btn-warning" id="extractButton">
                        <i class="bi bi-filter me-1"></i> Extract message Id
                    </button>
                    <button class="btn btn-danger" id="clearButton">
                        <i class="bi bi-x-lg me-1"></i> Clear All
                    </button>
                </div>
            </div>

            <!-- OpenSearch Mode Section -->
            <div id="opensearchModeSection">
                <!-- Search Input with Index Pattern -->
                <div class="mb-3">
                    <div class="d-flex gap-2 align-items-start">
                        <select id="opensearchIndexPattern" class="form-select" style="max-width: 200px;">
                            <option value="okd-test*">okd-test*</option>
                            <option value="okd-prod*">okd-prod*</option>
                            <option value="apigee-e2e*">apigee-e2e*</option>
                            <option value="apigee-sit*">apigee-sit*</option>
                            <option value="apigee-uat-pet-*">apigee-uat-pet-*</option>
                            <option value="apigee-prod-*" selected>apigee-prod-*</option>
                        </select>
                        <div class="search-input-container flex-grow-1">
                            <div class="input-group">
                                <input type="text" id="opensearchQuery" class="form-control" placeholder="Enter search query (message ID, simple text, or OpenSearch DSL JSON)..." autocomplete="off">
                                <button id="searchHistoryButton" class="btn btn-outline-secondary" type="button" title="Search History">
                                    <i class="bi bi-clock-history"></i>
                                </button>
                            </div>
                            <div id="searchHistoryDropdown" class="search-history-dropdown">
                                <div class="search-history-header">
                                    <span>Recent Searches</span>
                                    <button id="clearSearchHistory" class="btn btn-sm btn-outline-danger" title="Clear History">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                                <div id="searchHistoryList" class="search-history-list">
                                    <div class="search-history-empty">No recent searches</div>
                                </div>
                            </div>
                        </div>
                        <button id="opensearchSearchButton" class="btn btn-primary">
                            <i class="bi bi-search me-1"></i>Search
                        </button>
                        <button id="advancedSearchButton" class="btn btn-outline-secondary" title="Advanced Search">
                            <i class="bi bi-sliders"></i>
                        </button>
                    </div>
                </div>

                <!-- Time Range and Clear -->
                <div class="mb-3 d-flex gap-2 align-items-center">
                    <select id="timeRange" class="form-select" style="max-width: 200px;">
                        <option value="15m">Last 15 minutes</option>
                        <option value="1h">Last 1 hour</option>
                        <option value="4h">Last 4 hours</option>
                        <option value="1d" selected>Last 24 hours</option>
                        <option value="7d">Last 7 days</option>
                        <option value="30d">Last 30 days</option>
                    </select>
                    <button class="btn btn-outline-danger" id="opensearchClearButton">
                        <i class="bi bi-x-lg me-1"></i>Clear
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container hidden" id="processedContainer">
        <div class="text-center">
            <h3>Processed logs</h3>

            <!-- Moved entry navigation elsewhere -->
        </div>

        <!-- Enhanced search interface -->
        <div class="search-container">
            <div class="search-input-wrapper">
                <i class="bi bi-search search-icon"></i>
                <input type="text"
                       id="logSearchInput"
                       class="search-input"
                       placeholder="Search logs...">

                <div class="search-controls">
                    <div class="search-options">
                        <div class="global-search-toggle-wrapper" title="Advanced search options">
                            <input type="checkbox" id="globalSearchToggle" class="global-search-toggle">
                            <label for="globalSearchToggle" class="global-search-label">Global</label>
                        </div>
                    </div>

                    <div class="search-navigation">
                        <button id="prevMatchBtn" class="search-nav-btn" title="Previous match" disabled>
                            <i class="bi bi-chevron-up"></i>
                        </button>
                        <span id="searchMatchCount" class="search-match-count" title="Total matches">0 matches</span>
                        <button id="nextMatchBtn" class="search-nav-btn" title="Next match" disabled>
                            <i class="bi bi-chevron-down"></i>
                        </button>
                    </div>

                    <button id="clearSearchBtn" class="search-clear-btn" title="Clear search">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            </div>
            <div id="searchHelpText" class="search-help-text">
                <!-- Help text will be shown here when needed -->
            </div>
        </div>

        <!-- Column controls as dropdown -->
        <div class="log-entry-header" data-process-type="">
            <div class="dropdown view-type-selector">
                <button id="viewTypeButton" class="export-button" type="button">
                    <i class="bi bi-diagram-3 me-1"></i>
                    <span>API Calls</span>
                    <i class="bi bi-chevron-down ms-1"></i>
                </button>
                <div id="viewTypeControls" class="dropdown-menu">
                    <label class="dropdown-item" data-view-type="flows">
                        <i class="bi bi-list-ul me-2"></i>API Flows
                    </label>
                    <label class="dropdown-item" data-view-type="calls">
                        <i class="bi bi-diagram-3 me-2"></i>API Calls
                    </label>
                </div>
            </div>
            <div class="header-left">
                <span id="entryCounter" class="entry-counter">0 records found</span>
                <button id="copyButton" class="export-button">
                    <i class="bi bi-clipboard me-1"></i>
                    <span>Copy Text</span>
                </button>
                <button id="formatButton" class="export-button">
                    <i class="bi bi-code-slash me-1"></i>
                    <span>Format for OpenSearch</span>
                </button>
            </div>
            <div class="header-controls">
                <div class="dropdown">
                    <button id="flowButton" class="export-button" type="button">
                        <i class="bi bi-funnel me-1"></i>
                        <span>Flows</span>
                        <i class="bi bi-chevron-down ms-1"></i>
                    </button>
                    <div id="flowControls" class="dropdown-menu">
                        <label class="dropdown-item"><input type="checkbox" id="selectAllFlows" checked> All flows</label>
                        <div class="dropdown-divider"></div>
                        <label class="dropdown-item"><input type="checkbox" class="flow-checkbox" value="PROXY_REQ_FLOW"> PROXY_REQ_FLOW</label>
                        <label class="dropdown-item"><input type="checkbox" class="flow-checkbox" value="TARGET_REQ_FLOW"> TARGET_REQ_FLOW</label>
                        <label class="dropdown-item"><input type="checkbox" class="flow-checkbox" value="TARGET_RESP_FLOW"> TARGET_RESP_FLOW</label>
                        <label class="dropdown-item"><input type="checkbox" class="flow-checkbox" value="PROXY_RESP_FLOW"> PROXY_RESP_FLOW</label>
                    </div>
                </div>
                <div class="dropdown">
                    <button id="messageIdButton" class="export-button" type="button">
                        <i class="bi bi-funnel me-1"></i>
                        <span>Message IDs</span>
                        <i class="bi bi-chevron-down ms-1"></i>
                    </button>
                    <div id="messageIdControls" class="dropdown-menu" style="max-height: 300px; overflow-y: auto;">
                        <label class="dropdown-item"><input type="checkbox" id="selectAllMessageIds" checked> All message id</label>
                        <div class="dropdown-divider"></div>
                        <!-- Message IDs will be populated here -->
                    </div>
                </div>
                <div class="dropdown status-code-dropdown">
                    <button id="statusCodeButton" class="export-button" type="button">
                        <i class="bi bi-funnel me-1"></i>
                        <span>Status Codes</span>
                        <i class="bi bi-chevron-down ms-1"></i>
                    </button>
                    <div id="statusCodeControls" class="dropdown-menu" style="max-height: 300px; overflow-y: auto;">
                        <label class="dropdown-item"><input type="checkbox" id="selectAllStatusCodes" checked> All status codes</label>
                        <div class="dropdown-divider"></div>
                        <!-- Status codes will be populated here -->
                    </div>
                </div>
                <div class="dropdown">
                    <button id="columnButton" class="export-button" type="button">
                        <i class="bi bi-columns me-1"></i>
                        <span>Columns</span>
                        <i class="bi bi-chevron-down ms-1"></i>
                    </button>
                    <div id="columnControls" class="dropdown-menu">
                        <!-- Column checkboxes will be populated dynamically -->
                    </div>
                </div>
                <div class="dropdown">
                    <button id="exportButton" class="export-button" type="button">
                        <i class="bi bi-download me-1"></i>
                        <span>Export</span>
                        <i class="bi bi-chevron-down ms-1"></i>
                    </button>                    <div id="exportControls" class="dropdown-menu">
                        <label class="dropdown-item" id="exportXLSX">
                            <i class="bi bi-file-earmark-spreadsheet me-2"></i><span class="export-label">Export <span class="export-type">flows</span> to XLSX</span>
                        </label>
                        <label class="dropdown-item" id="exportTXT">
                            <i class="bi bi-file-earmark-text me-2"></i>Export logs to TXT
                        </label>
                        <label class="dropdown-item" id="exportJPEG">
                            <i class="bi bi-file-earmark-image me-2"></i>Export current page to JPEG
                        </label>
                    </div>
                </div>
            </div>
        </div>
          <div id="fileContent" class="fileContent">
            <!-- Content will be inserted here by JavaScript -->
        </div>

        <button id="backToTopButton" class="back-to-top hidden" aria-label="Back to top">
            <i class="bi bi-arrow-up"></i>
        </button>
    </div>    <!-- Toast Notification -->
    <div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1060">
        <div id="liveToast" class="toast border border-2 border-success" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">Notification</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body" id="toastMessage">
                <!-- Toast message will be inserted here -->
            </div>
        </div>
    </div>

    <footer class="container text-center">
        <small>&copy; 2025 · Apigee Log Processor</small>
    </footer>    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- External libraries -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>

    <!-- Modular JavaScript -->
    <script type="module">
        // Ensure external libraries are available to modules
        if (typeof XLSX !== 'undefined') {
            window.XLSX = XLSX;
        } else {
            console.error('XLSX library not loaded');
        }

        if (typeof html2canvas !== 'undefined') {
            window.html2canvas = html2canvas;
        } else {
            console.error('html2canvas library not loaded');
        }

        import { initializeApp } from './modules/main.js';
        // Initialize the app when the module is loaded
        document.addEventListener('DOMContentLoaded', initializeApp);
    </script>
    <!-- Offcanvas for entry details -->    <div class="offcanvas offcanvas-end responsive-offcanvas" tabindex="-1" id="entryDetailsOffcanvas" aria-labelledby="entryDetailsOffcanvasLabel">        <div class="offcanvas-header">
            <div class="offcanvas-header-content">
                <div class="offcanvas-header-top">
                    <div class="offcanvas-header-left">
                        <h5 class="offcanvas-title" id="entryDetailsOffcanvasLabel">
                            <span id="offcanvas-message-id">-</span>
                            <span class="separator">|</span>
                            <span id="offcanvas-flow">-</span>
                            <span class="separator">|</span>
                            <span id="offcanvas-env-org">-</span>
                            <span class="separator">|</span>
                            <span id="offcanvas-status-code">-</span>
                        </h5>
                    </div>
                    <div class="offcanvas-actions">
                        <button type="button" class="offcanvas-copy-btn" id="copyLogBtn" title="Copy">
                            <i class="bi bi-copy"></i>
                        </button>
                        <button type="button" class="offcanvas-copy-btn" id="exportLogBtn" title="Export to TXT">
                            <i class="bi bi-download"></i>
                        </button>
                        <button type="button" class="offcanvas-mask-btn" id="toggleMaskBtn" title="Show sensitive data">
                            <i class="bi bi-eye-slash"></i>
                        </button>
                        <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
                    </div>
                </div>
                <div class="offcanvas-header-bottom">
                    <div class="offcanvas-navigation">
                        <button type="button" class="nav-btn" id="prevLogBtn" title="Previous log (←)">
                            <i class="bi bi-chevron-left"></i>
                        </button>
                        <span id="logNavigationCounter" class="log-nav-counter">Entry 0 of 0</span>
                        <button type="button" class="nav-btn" id="nextLogBtn" title="Next log (→)">
                            <i class="bi bi-chevron-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="offcanvas-body" id="offcanvasEntryContent">
            <!-- Content will be populated by JavaScript -->
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay d-none">
        <div class="loading-content">
            <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="mt-3 text-center">
                <h5>Searching...</h5>
                <p class="text-muted">Please wait while we process your query</p>
            </div>
        </div>
    </div>

    <!-- Advanced Search Modal -->
    <div class="modal fade" id="advancedSearchModal" tabindex="-1" aria-labelledby="advancedSearchModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="advancedSearchModalLabel">Advanced Search</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <!-- Field-specific searches -->
                        <div class="col-md-6">
                            <h6>Field-Specific Search</h6>
                            <div class="mb-3">
                                <label for="advMessageId" class="form-label">Message ID</label>
                                <input type="text" class="form-control" id="advMessageId" placeholder="e.g., apigeeprod112-24056-2385438-1">
                            </div>
                            <div class="mb-3">
                                <label for="advUri" class="form-label">URI/Path</label>
                                <input type="text" class="form-control" id="advUri" placeholder="e.g., /api/v1/users">
                            </div>
                            <div class="mb-3">
                                <label for="advStatusCode" class="form-label">Status Code</label>
                                <input type="text" class="form-control" id="advStatusCode" placeholder="e.g., 500, 200">
                            </div>
                            <div class="mb-3">
                                <label for="advHttpMethod" class="form-label">HTTP Method</label>
                                <select class="form-select" id="advHttpMethod">
                                    <option value="">Any</option>
                                    <option value="GET">GET</option>
                                    <option value="POST">POST</option>
                                    <option value="PUT">PUT</option>
                                    <option value="DELETE">DELETE</option>
                                    <option value="PATCH">PATCH</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="advFlow" class="form-label">Flow Type</label>
                                <select class="form-select" id="advFlow">
                                    <option value="">Any</option>
                                    <option value="PROXY_REQ_FLOW">Proxy Request</option>
                                    <option value="TARGET_REQ_FLOW">Target Request</option>
                                    <option value="TARGET_RESP_FLOW">Target Response</option>
                                    <option value="PROXY_RESP_FLOW">Proxy Response</option>
                                </select>
                            </div>
                        </div>

                        <!-- Content search -->
                        <div class="col-md-6">
                            <h6>Content Search</h6>
                            <div class="mb-3">
                                <label for="advMessageBody" class="form-label">Message Body Contains</label>
                                <textarea class="form-control" id="advMessageBody" rows="3" placeholder="Search in message bodies..."></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="advClientId" class="form-label">Client ID</label>
                                <input type="text" class="form-control" id="advClientId" placeholder="e.g., mobile-app">
                            </div>
                            <div class="mb-3">
                                <label for="advAppName" class="form-label">App Name</label>
                                <input type="text" class="form-control" id="advAppName" placeholder="e.g., my-api-app">
                            </div>

                            <!-- Time range -->
                            <div class="mb-3">
                                <label for="advTimeRange" class="form-label">Time Range</label>
                                <select class="form-select" id="advTimeRange">
                                    <option value="15m">Last 15 minutes</option>
                                    <option value="1h">Last 1 hour</option>
                                    <option value="4h">Last 4 hours</option>
                                    <option value="1d" selected>Last 24 hours</option>
                                    <option value="7d">Last 7 days</option>
                                    <option value="30d">Last 30 days</option>
                                </select>
                            </div>

                            <!-- Search options -->
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="advExactMatch">
                                    <label class="form-check-label" for="advExactMatch">
                                        Exact match only
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="advCaseSensitive">
                                    <label class="form-check-label" for="advCaseSensitive">
                                        Case sensitive
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-outline-secondary" id="advancedSearchClear">Clear All</button>
                    <button type="button" class="btn btn-primary" id="advancedSearchExecute">Search</button>
                </div>
            </div>
        </div>
    </div>

    <script type="module" src="modules/main.js"></script>
</body>
</html>
